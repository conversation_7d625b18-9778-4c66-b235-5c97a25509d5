<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест системи екіпірування</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .test-section {
            border: 1px solid #00ff00;
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .equipment-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 20px 0;
        }
        .equipment-slot {
            width: 60px;
            height: 60px;
            border: 2px dashed #666;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .equipment-slot.equipped {
            border-style: solid;
            background: rgba(0, 0, 0, 0.8);
        }
        .equipment-slot:hover {
            border-color: #00ff00;
            transform: scale(1.05);
        }
        .inventory-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 5px;
            margin: 20px 0;
        }
        .inventory-item {
            width: 50px;
            height: 50px;
            border: 2px solid #666;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: grab;
            transition: all 0.2s ease;
        }
        .inventory-item:hover {
            transform: scale(1.1);
            box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
        }
        .inventory-item.dragging {
            opacity: 0.5;
            cursor: grabbing;
        }
        .shop-items {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }
        .shop-item-card {
            background: #222;
            border: 2px solid #444;
            border-radius: 8px;
            padding: 15px;
            width: 200px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .shop-item-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        .stats-display {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .active-effects {
            background: #004400;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background: #003300;
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 10px 20px;
            cursor: pointer;
            margin: 5px;
            border-radius: 3px;
        }
        button:hover { background: #006600; }
        .success { color: #00ff00; }
        .error { color: #ff0000; }
        .warning { color: #ffff00; }
        .info { color: #0066ff; }
    </style>
</head>
<body>
    <h1>⚔️ Тест системи екіпірування та предметів</h1>
    
    <div class="test-section">
        <h2>Ініціалізація систем</h2>
        <button onclick="initializeSystems()">Ініціалізувати системи</button>
        <button onclick="generateTestItems()">Згенерувати тестові предмети</button>
        <button onclick="testShops()">Тестувати магазини</button>
        <button onclick="testBiomes()">Тестувати біоми</button>
        <button onclick="testKeyboardShortcuts()">Тестувати клавіші</button>
        <div id="initResults"></div>
    </div>

    <div class="test-section">
        <h2>Біомна система</h2>
        <canvas id="biomeCanvas" width="400" height="200" style="border: 1px solid #00ff00; background: #000;"></canvas>
        <div>
            <button onclick="changeBiome('cave')">Печера</button>
            <button onclick="changeBiome('forest')">Ліс</button>
            <button onclick="changeBiome('desert')">Пустеля</button>
            <button onclick="changeBiome('glacier')">Льодовики</button>
            <button onclick="changeBiome('volcano')">Вулкан</button>
            <button onclick="changeBiome('abyss')">Безодня</button>
        </div>
        <div id="biomeInfo"></div>
    </div>
    
    <div class="test-section">
        <h2>Характеристики персонажа</h2>
        <div id="playerStats" class="stats-display">
            <!-- Статистики будуть додані динамічно -->
        </div>
        <div id="activeEffects" class="active-effects">
            <!-- Активні ефекти будуть додані динамічно -->
        </div>
    </div>
    
    <div class="test-section">
        <h2>Екіпірування (9 слотів)</h2>
        <div class="equipment-grid">
            <div class="equipment-slot" data-slot="helmet" id="slot-helmet">⛑️</div>
            <div class="equipment-slot" data-slot="amulet" id="slot-amulet">🧿</div>
            <div class="equipment-slot" data-slot="ring1" id="slot-ring1">💍</div>
            
            <div class="equipment-slot" data-slot="weapon" id="slot-weapon">⚔️</div>
            <div class="equipment-slot" data-slot="armor" id="slot-armor">🛡️</div>
            <div class="equipment-slot" data-slot="shield" id="slot-shield">🛡️</div>
            
            <div class="equipment-slot" data-slot="gloves" id="slot-gloves">🧤</div>
            <div class="equipment-slot" data-slot="boots" id="slot-boots">🥾</div>
            <div class="equipment-slot" data-slot="ring2" id="slot-ring2">💍</div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Інвентар</h2>
        <div class="inventory-grid" id="inventory">
            <!-- Предмети інвентаря будуть додані динамічно -->
        </div>
    </div>
    
    <div class="test-section">
        <h2>Тест витратних предметів</h2>
        <button onclick="testConsumables()">Згенерувати зілля та сувої</button>
        <button onclick="useRandomConsumable()">Використати випадковий предмет</button>
        <div id="consumableResults"></div>
    </div>
    
    <div class="test-section">
        <h2>Магазини з іконками</h2>
        <div id="shopDisplay" class="shop-items">
            <!-- Предмети магазинів будуть додані динамічно -->
        </div>
    </div>

    <script type="module">
        let gameState, equipmentSystem, iconSystem, consumableSystem, shopSystem, contentGenerator, biomeSystem;

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = type;
            div.textContent = message;
            container.appendChild(div);
        }

        window.initializeSystems = async function() {
            try {
                // Import all systems
                const { GameState } = await import('./js/gameState.js');
                const { EquipmentSystem } = await import('./js/equipmentSystem.js');
                const { ItemIconSystem } = await import('./js/itemIconSystem.js');
                const { ConsumableSystem } = await import('./js/consumableSystem.js');
                const { ShopSystem } = await import('./js/shopSystem.js');
                const { ContentGenerator } = await import('./js/contentGenerator.js');
                const { BiomeBackgroundSystem } = await import('./js/biomeBackgroundSystem.js');

                // Initialize systems
                gameState = new GameState();
                contentGenerator = new ContentGenerator();
                iconSystem = new ItemIconSystem();
                equipmentSystem = new EquipmentSystem(gameState);
                consumableSystem = new ConsumableSystem(gameState);
                shopSystem = new ShopSystem(gameState, contentGenerator);

                // Initialize biome system
                const biomeCanvas = document.getElementById('biomeCanvas');
                const biomeCtx = biomeCanvas.getContext('2d');
                biomeSystem = new BiomeBackgroundSystem(biomeCanvas, biomeCtx);

                addResult('initResults', '✅ Всі системи ініціалізовано успішно', 'success');
                
                // Initialize equipment bonuses
                equipmentSystem.syncFromGameState();
                updatePlayerStatsDisplay();
                updateEquipmentDisplay();
                updateInventoryDisplay();

            } catch (error) {
                addResult('initResults', `❌ Помилка ініціалізації: ${error.message}`, 'error');
                console.error(error);
            }
        };

        window.generateTestItems = function() {
            if (!gameState || !contentGenerator) {
                addResult('initResults', '❌ Спочатку ініціалізуйте системи', 'error');
                return;
            }

            // Clear inventory
            gameState.player.inventory = [];

            // Generate test items of different types
            const itemTypes = ['sword', 'armor', 'helmet', 'shield', 'gloves', 'boots', 'ring', 'amulet'];
            
            itemTypes.forEach(type => {
                const item = contentGenerator.generateItem(5, type);
                gameState.player.inventory.push(item);
            });

            addResult('initResults', `✅ Згенеровано ${itemTypes.length} тестових предметів`, 'success');
            updateInventoryDisplay();
        };

        window.testConsumables = function() {
            if (!gameState || !contentGenerator) {
                addResult('consumableResults', '❌ Спочатку ініціалізуйте системи', 'error');
                return;
            }

            // Generate consumable items
            const consumableTypes = ['potion', 'scroll', 'food'];
            
            consumableTypes.forEach(type => {
                const item = contentGenerator.generateItem(3, type);
                gameState.player.inventory.push(item);
            });

            addResult('consumableResults', `✅ Згенеровано ${consumableTypes.length} витратних предметів`, 'success');
            updateInventoryDisplay();
        };

        window.useRandomConsumable = function() {
            if (!gameState || !consumableSystem) {
                addResult('consumableResults', '❌ Спочатку ініціалізуйте системи', 'error');
                return;
            }

            const consumables = gameState.player.inventory.filter(item => 
                consumableSystem.isConsumable(item)
            );

            if (consumables.length === 0) {
                addResult('consumableResults', '⚠️ Немає витратних предметів', 'warning');
                return;
            }

            const randomConsumable = consumables[Math.floor(Math.random() * consumables.length)];
            const index = gameState.player.inventory.indexOf(randomConsumable);
            
            const result = consumableSystem.useItem(randomConsumable, index);
            
            if (result.success) {
                addResult('consumableResults', `✅ ${result.message}`, 'success');
            } else {
                addResult('consumableResults', `❌ ${result.message}`, 'error');
            }

            updatePlayerStatsDisplay();
            updateInventoryDisplay();
        };

        window.testShops = function() {
            if (!shopSystem) {
                addResult('initResults', '❌ Спочатку ініціалізуйте системи', 'error');
                return;
            }

            const shopContainer = document.getElementById('shopDisplay');
            shopContainer.innerHTML = '';

            const shops = ['weaponsmith', 'armorsmith', 'magicVendor', 'generalStore'];
            
            shops.forEach(shopType => {
                const shopData = shopSystem.getShopData(shopType);
                
                if (shopData.locked) {
                    const lockedDiv = document.createElement('div');
                    lockedDiv.textContent = `${shopData.name} заблокований до рівня ${shopData.unlockLevel}`;
                    lockedDiv.style.color = '#666';
                    shopContainer.appendChild(lockedDiv);
                    return;
                }

                const shopTitle = document.createElement('h3');
                shopTitle.textContent = shopData.name;
                shopTitle.style.color = '#00ff00';
                shopContainer.appendChild(shopTitle);

                shopData.items.slice(0, 3).forEach((item, index) => {
                    const card = createShopItemCard(item, shopType, index);
                    shopContainer.appendChild(card);
                });
            });

            addResult('initResults', '✅ Магазини відображено з іконками', 'success');
        };

        function createShopItemCard(item, shopType, index) {
            const card = document.createElement('div');
            card.className = 'shop-item-card';
            card.style.borderColor = iconSystem.getRarityColor(item.rarity);

            const icon = iconSystem.createItemIconElement(item, 32);
            card.appendChild(icon);

            const name = document.createElement('div');
            name.textContent = item.name;
            name.style.color = iconSystem.getRarityColor(item.rarity);
            name.style.fontWeight = 'bold';
            name.style.margin = '10px 0';
            card.appendChild(name);

            const price = document.createElement('div');
            price.textContent = `${item.shopPrice} золота`;
            price.style.color = '#ffd700';
            card.appendChild(price);

            return card;
        }

        function updatePlayerStatsDisplay() {
            if (!gameState) return;

            const container = document.getElementById('playerStats');
            const player = gameState.player;
            
            container.innerHTML = `
                <h3>Характеристики персонажа:</h3>
                <div>Рівень: ${player.level}</div>
                <div>Здоров'я: ${player.hp}/${player.maxHp}</div>
                <div>Мана: ${player.mp || 0}/${player.maxMp || 50}</div>
                <div>Атака: ${player.attack}</div>
                <div>Захист: ${player.defense}</div>
                <div>Золото: ${player.gold}</div>
            `;

            // Show active effects
            if (consumableSystem) {
                const effects = consumableSystem.getActiveEffects();
                const effectsContainer = document.getElementById('activeEffects');
                
                if (effects.length > 0) {
                    effectsContainer.innerHTML = '<h3>Активні ефекти:</h3>' + 
                        effects.map(effect => 
                            `<div>${effect.name}: +${effect.value} (${Math.ceil(effect.timeLeft / 1000)}с)</div>`
                        ).join('');
                } else {
                    effectsContainer.innerHTML = '<h3>Активні ефекти:</h3><div>Немає активних ефектів</div>';
                }
            }
        }

        function updateEquipmentDisplay() {
            if (!equipmentSystem) return;

            Object.keys(equipmentSystem.equippedItems).forEach(slotType => {
                const slot = document.getElementById(`slot-${slotType}`);
                const item = equipmentSystem.equippedItems[slotType];
                
                if (item) {
                    slot.innerHTML = '';
                    const icon = iconSystem.createItemIconElement(item, 40);
                    slot.appendChild(icon);
                    slot.className = 'equipment-slot equipped';
                } else {
                    slot.innerHTML = iconSystem.getSlotIcon(slotType);
                    slot.className = 'equipment-slot';
                }
            });
        }

        function updateInventoryDisplay() {
            if (!gameState) return;

            const container = document.getElementById('inventory');
            container.innerHTML = '';

            gameState.player.inventory.forEach((item, index) => {
                const itemElement = iconSystem.createInventoryItemElement(item, index, 45);
                
                // Add click handler for consumables
                if (consumableSystem && consumableSystem.isConsumable(item)) {
                    itemElement.addEventListener('click', () => {
                        const result = consumableSystem.useItem(item, index);
                        if (result.success) {
                            addResult('consumableResults', result.message, 'success');
                        } else {
                            addResult('consumableResults', result.message, 'error');
                        }
                        updatePlayerStatsDisplay();
                        updateInventoryDisplay();
                    });
                    itemElement.style.cursor = 'pointer';
                    itemElement.title = 'Клікніть для використання';
                }

                container.appendChild(itemElement);
            });

            // Fill empty slots
            for (let i = gameState.player.inventory.length; i < 20; i++) {
                const emptySlot = document.createElement('div');
                emptySlot.className = 'inventory-item';
                emptySlot.style.borderStyle = 'dashed';
                emptySlot.style.opacity = '0.3';
                container.appendChild(emptySlot);
            }
        }

        // Test biome system
        window.testBiomes = function() {
            if (!biomeSystem) {
                addResult('initResults', '❌ Спочатку ініціалізуйте системи', 'error');
                return;
            }

            // Start biome animation
            function animateBiome() {
                biomeSystem.update(16);
                biomeSystem.render();
                requestAnimationFrame(animateBiome);
            }
            animateBiome();

            addResult('initResults', '✅ Біомна система запущена', 'success');
        };

        window.changeBiome = function(biomeType) {
            if (!biomeSystem) return;

            biomeSystem.currentBiome = biomeType;
            biomeSystem.updateCSSBackground();
            biomeSystem.initializeParticles();

            const biomeInfo = biomeSystem.getCurrentBiomeInfo();
            document.getElementById('biomeInfo').innerHTML = `
                <h3>Поточний біом: ${biomeInfo.name}</h3>
                <p>Частинки: ${biomeInfo.particleType} (${biomeInfo.particleCount})</p>
                <p>Колір: ${biomeInfo.particleColor}</p>
            `;

            addResult('initResults', `✅ Змінено біом на: ${biomeInfo.name}`, 'success');
        };

        window.testKeyboardShortcuts = function() {
            addResult('initResults', '⌨️ Тестування клавіатурних скорочень:', 'info');
            addResult('initResults', 'I - Інвентар, E - Екіпірування, S - Магазини', 'info');
            addResult('initResults', 'F - Авто-бій, Space/Enter - Атака/Прогрес', 'info');
            addResult('initResults', 'Escape - Закрити інтерфейси', 'info');
        };

        // Initialize on page load
        window.addEventListener('load', () => {
            addResult('initResults', 'Сторінка завантажена. Натисніть "Ініціалізувати системи"', 'info');
        });
    </script>
</body>
</html>

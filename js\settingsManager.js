// Settings and statistics manager
export class SettingsManager {
    constructor(gameState, audioManager) {
        this.gameState = gameState;
        this.audioManager = audioManager;
        
        // Game settings
        this.settings = {
            // Audio settings
            masterVolume: 0.5,
            musicVolume: 0.5,
            sfxVolume: 0.7,
            audioEnabled: true,
            
            // Visual settings
            showDamageNumbers: true,
            showParticles: true,
            fastAnimations: false,
            reducedMotion: false,
            
            // Gameplay settings
            autoSave: true,
            autoSaveInterval: 30, // seconds
            showTooltips: true,
            confirmPrestige: true,
            
            // UI settings
            compactMode: false,
            showAdvancedStats: false,
            logMaxEntries: 50,
            
            // Performance settings
            maxParticles: 100,
            targetFPS: 60,
            vsync: true
        };
        
        // Extended statistics
        this.extendedStats = {
            // Session stats
            sessionStartTime: Date.now(),
            sessionPlayTime: 0,
            sessionEnemiesKilled: 0,
            sessionGoldEarned: 0,
            sessionLevelsGained: 0,
            
            // All-time records
            highestLevel: 1,
            deepestFloor: 1,
            mostGoldAtOnce: 0,
            longestSession: 0,
            fastestFloor10: 0,
            
            // Combat stats
            totalDamageDealt: 0,
            totalDamageTaken: 0,
            criticalHits: 0,
            perfectRuns: 0, // Runs without dying
            
            // Item stats
            commonItemsFound: 0,
            uncommonItemsFound: 0,
            rareItemsFound: 0,
            epicItemsFound: 0,
            legendaryItemsFound: 0,
            
            // Boss stats
            bossesEncountered: 0,
            bossesDefeated: 0,
            bossWinRate: 0,
            
            // Prestige stats
            totalPrestigeResets: 0,
            totalPrestigePointsEarned: 0,
            averagePrestigePoints: 0,
            
            // Skill stats
            totalSkillLevelsGained: 0,
            highestSkillLevel: 1,
            skillPointsSpent: 0,
            
            // Efficiency stats
            goldPerHour: 0,
            expPerHour: 0,
            floorsPerHour: 0,
            
            // Misc stats
            buttonsClicked: 0,
            menuOpened: 0,
            gameLoaded: 0
        };
        
        this.loadSettings();
    }
    
    // Settings management
    loadSettings() {
        try {
            const saved = localStorage.getItem('idleRoguelikeSettings');
            if (saved) {
                const savedSettings = JSON.parse(saved);
                this.settings = { ...this.settings, ...savedSettings };
            }
            
            const savedStats = localStorage.getItem('idleRoguelikeExtendedStats');
            if (savedStats) {
                const stats = JSON.parse(savedStats);
                this.extendedStats = { ...this.extendedStats, ...stats };
            }
            
            this.applySettings();
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    }
    
    saveSettings() {
        try {
            localStorage.setItem('idleRoguelikeSettings', JSON.stringify(this.settings));
            localStorage.setItem('idleRoguelikeExtendedStats', JSON.stringify(this.extendedStats));
        } catch (error) {
            console.error('Failed to save settings:', error);
        }
    }
    
    applySettings() {
        // Apply audio settings only if audio is initialized
        if (this.audioManager && this.audioManager.masterGain) {
            this.audioManager.setMasterVolume(this.settings.masterVolume);
            this.audioManager.setMusicVolume(this.settings.musicVolume);
            this.audioManager.setSfxVolume(this.settings.sfxVolume);

            if (!this.settings.audioEnabled) {
                this.audioManager.toggle();
            }
        }
        
        // Apply visual settings
        if (this.settings.reducedMotion) {
            document.body.classList.add('reduced-motion');
        } else {
            document.body.classList.remove('reduced-motion');
        }
        
        if (this.settings.compactMode) {
            document.body.classList.add('compact-mode');
        } else {
            document.body.classList.remove('compact-mode');
        }
    }
    
    updateSetting(key, value) {
        if (this.settings.hasOwnProperty(key)) {
            this.settings[key] = value;
            this.applySettings();
            this.saveSettings();
            return true;
        }
        return false;
    }
    
    // Statistics tracking
    updateStats(deltaTime) {
        this.extendedStats.sessionPlayTime += deltaTime;
        
        // Update records
        const player = this.gameState.player;
        const floor = this.gameState.currentFloor;
        
        if (player.level > this.extendedStats.highestLevel) {
            this.extendedStats.highestLevel = player.level;
        }
        
        if (floor > this.extendedStats.deepestFloor) {
            this.extendedStats.deepestFloor = floor;
        }
        
        if (player.gold > this.extendedStats.mostGoldAtOnce) {
            this.extendedStats.mostGoldAtOnce = player.gold;
        }
        
        // Update efficiency stats
        const hoursPlayed = this.extendedStats.sessionPlayTime / (1000 * 60 * 60);
        if (hoursPlayed > 0) {
            this.extendedStats.goldPerHour = this.extendedStats.sessionGoldEarned / hoursPlayed;
            this.extendedStats.expPerHour = (player.level - 1) * 100 / hoursPlayed; // Approximate
            this.extendedStats.floorsPerHour = (floor - 1) / hoursPlayed;
        }
        
        // Update session length record
        if (this.extendedStats.sessionPlayTime > this.extendedStats.longestSession) {
            this.extendedStats.longestSession = this.extendedStats.sessionPlayTime;
        }
    }
    
    // Event tracking
    trackEvent(eventType, data = {}) {
        switch (eventType) {
            case 'enemyKilled':
                this.extendedStats.sessionEnemiesKilled++;
                break;
                
            case 'goldEarned':
                this.extendedStats.sessionGoldEarned += data.amount || 0;
                break;
                
            case 'levelGained':
                this.extendedStats.sessionLevelsGained++;
                break;
                
            case 'damageDealt':
                this.extendedStats.totalDamageDealt += data.amount || 0;
                if (data.critical) {
                    this.extendedStats.criticalHits++;
                }
                break;
                
            case 'damageTaken':
                this.extendedStats.totalDamageTaken += data.amount || 0;
                break;
                
            case 'itemFound':
                const rarity = data.rarity || 'common';
                this.extendedStats[`${rarity}ItemsFound`]++;
                break;
                
            case 'bossEncountered':
                this.extendedStats.bossesEncountered++;
                break;
                
            case 'bossDefeated':
                this.extendedStats.bossesDefeated++;
                this.extendedStats.bossWinRate = 
                    this.extendedStats.bossesDefeated / this.extendedStats.bossesEncountered;
                break;
                
            case 'prestigeReset':
                this.extendedStats.totalPrestigeResets++;
                this.extendedStats.totalPrestigePointsEarned += data.points || 0;
                this.extendedStats.averagePrestigePoints = 
                    this.extendedStats.totalPrestigePointsEarned / this.extendedStats.totalPrestigeResets;
                break;
                
            case 'skillLevelGained':
                this.extendedStats.totalSkillLevelsGained++;
                if (data.level > this.extendedStats.highestSkillLevel) {
                    this.extendedStats.highestSkillLevel = data.level;
                }
                break;
                
            case 'buttonClicked':
                this.extendedStats.buttonsClicked++;
                break;
                
            case 'menuOpened':
                this.extendedStats.menuOpened++;
                break;
                
            case 'gameLoaded':
                this.extendedStats.gameLoaded++;
                break;
        }
        
        // Auto-save stats periodically
        if (Math.random() < 0.01) { // 1% chance per event
            this.saveSettings();
        }
    }
    
    // Get formatted statistics for display
    getFormattedStats() {
        const stats = this.extendedStats;
        const gameStats = this.gameState.stats;
        
        return {
            session: {
                'Час сесії': this.formatTime(stats.sessionPlayTime),
                'Вбито ворогів': stats.sessionEnemiesKilled.toLocaleString(),
                'Заробленого золота': stats.sessionGoldEarned.toLocaleString(),
                'Підвищено рівнів': stats.sessionLevelsGained.toLocaleString()
            },
            records: {
                'Найвищий рівень': stats.highestLevel.toLocaleString(),
                'Найглибший поверх': stats.deepestFloor.toLocaleString(),
                'Найбільше золота': stats.mostGoldAtOnce.toLocaleString(),
                'Найдовша сесія': this.formatTime(stats.longestSession)
            },
            combat: {
                'Завдано шкоди': stats.totalDamageDealt.toLocaleString(),
                'Отримано шкоди': stats.totalDamageTaken.toLocaleString(),
                'Критичних ударів': stats.criticalHits.toLocaleString(),
                'Ідеальних прогонів': stats.perfectRuns.toLocaleString()
            },
            items: {
                'Звичайних предметів': stats.commonItemsFound.toLocaleString(),
                'Незвичайних предметів': stats.uncommonItemsFound.toLocaleString(),
                'Рідкісних предметів': stats.rareItemsFound.toLocaleString(),
                'Епічних предметів': stats.epicItemsFound.toLocaleString(),
                'Легендарних предметів': stats.legendaryItemsFound.toLocaleString()
            },
            bosses: {
                'Зустрічено боссів': stats.bossesEncountered.toLocaleString(),
                'Переможено боссів': stats.bossesDefeated.toLocaleString(),
                'Відсоток перемог': `${(stats.bossWinRate * 100).toFixed(1)}%`
            },
            efficiency: {
                'Золота за годину': Math.floor(stats.goldPerHour).toLocaleString(),
                'Досвіду за годину': Math.floor(stats.expPerHour).toLocaleString(),
                'Поверхів за годину': stats.floorsPerHour.toFixed(1)
            }
        };
    }
    
    formatTime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) {
            return `${hours}г ${minutes % 60}хв`;
        } else if (minutes > 0) {
            return `${minutes}хв ${seconds % 60}с`;
        } else {
            return `${seconds}с`;
        }
    }
    
    // Reset statistics
    resetStats() {
        this.extendedStats = {
            ...this.extendedStats,
            sessionStartTime: Date.now(),
            sessionPlayTime: 0,
            sessionEnemiesKilled: 0,
            sessionGoldEarned: 0,
            sessionLevelsGained: 0
        };
    }
    
    // Export/Import settings
    exportSettings() {
        return {
            settings: this.settings,
            stats: this.extendedStats,
            exportDate: new Date().toISOString()
        };
    }
    
    importSettings(data) {
        try {
            if (data.settings) {
                this.settings = { ...this.settings, ...data.settings };
            }
            if (data.stats) {
                this.extendedStats = { ...this.extendedStats, ...data.stats };
            }
            this.applySettings();
            this.saveSettings();
            return true;
        } catch (error) {
            console.error('Failed to import settings:', error);
            return false;
        }
    }
}

<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Idle Roguelike - Test</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #00ff00;
            margin: 10px 0;
            padding: 10px;
        }
        .test-result {
            margin: 5px 0;
        }
        .pass { color: #00ff00; }
        .fail { color: #ff0000; }
        button {
            background: #003300;
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 8px 16px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #006600;
        }
    </style>
</head>
<body>
    <h1>Idle Roguelike - Тестування модулів</h1>
    
    <div class="test-section">
        <h3>Тест модулів</h3>
        <button onclick="testModules()">Тестувати модулі</button>
        <div id="moduleResults"></div>
    </div>
    
    <div class="test-section">
        <h3>Тест генерації контенту</h3>
        <button onclick="testContentGeneration()">Тестувати генерацію</button>
        <div id="contentResults"></div>
    </div>
    
    <div class="test-section">
        <h3>Тест збереження</h3>
        <button onclick="testSaveLoad()">Тестувати збереження</button>
        <div id="saveResults"></div>
    </div>
    
    <div class="test-section">
        <h3>Запуск гри</h3>
        <button onclick="window.location.href='index.html'">Запустити гру</button>
    </div>

    <script type="module">
        import { GameState } from './js/gameState.js';
        import { ContentGenerator } from './js/contentGenerator.js';
        import { SaveManager } from './js/saveManager.js';
        import { AudioManager } from './js/audio.js';
        import { Renderer } from './js/renderer.js';

        window.testModules = function() {
            const results = document.getElementById('moduleResults');
            results.innerHTML = '';
            
            const tests = [
                { name: 'GameState', test: () => new GameState() },
                { name: 'ContentGenerator', test: () => new ContentGenerator() },
                { name: 'AudioManager', test: () => new AudioManager() },
                { name: 'SaveManager', test: () => new SaveManager(new GameState()) },
                { name: 'Renderer', test: () => {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    return new Renderer(canvas, ctx);
                }}
            ];
            
            tests.forEach(test => {
                try {
                    const instance = test.test();
                    results.innerHTML += `<div class="test-result pass">✓ ${test.name} - OK</div>`;
                } catch (error) {
                    results.innerHTML += `<div class="test-result fail">✗ ${test.name} - ERROR: ${error.message}</div>`;
                }
            });
        };
        
        window.testContentGeneration = function() {
            const results = document.getElementById('contentResults');
            results.innerHTML = '';
            
            try {
                const generator = new ContentGenerator();
                
                // Test enemy generation
                const enemy = generator.generateEnemy(1, 1);
                results.innerHTML += `<div class="test-result pass">✓ Ворог: ${enemy.name} (HP: ${enemy.hp}, ATK: ${enemy.attack})</div>`;
                
                // Test item generation
                const item = generator.generateItem(1);
                results.innerHTML += `<div class="test-result pass">✓ Предмет: ${item.name} (${item.type})</div>`;
                
                // Test location generation
                const location = generator.generateLocation(1);
                results.innerHTML += `<div class="test-result pass">✓ Локація: ${location.name}</div>`;
                
                // Test event generation
                const event = generator.generateRandomEvent(1, 1);
                results.innerHTML += `<div class="test-result pass">✓ Подія: ${event.type}</div>`;
                
            } catch (error) {
                results.innerHTML += `<div class="test-result fail">✗ Помилка генерації: ${error.message}</div>`;
            }
        };
        
        window.testSaveLoad = function() {
            const results = document.getElementById('saveResults');
            results.innerHTML = '';
            
            try {
                const gameState = new GameState();
                const saveManager = new SaveManager(gameState);
                
                // Test save
                gameState.player.level = 5;
                gameState.player.gold = 1000;
                const saveResult = saveManager.saveGame();
                results.innerHTML += `<div class="test-result ${saveResult ? 'pass' : 'fail'}">${saveResult ? '✓' : '✗'} Збереження</div>`;
                
                // Test load
                const newGameState = new GameState();
                const newSaveManager = new SaveManager(newGameState);
                const loadResult = newSaveManager.loadGame();
                results.innerHTML += `<div class="test-result ${loadResult ? 'pass' : 'fail'}">${loadResult ? '✓' : '✗'} Завантаження</div>`;
                
                if (loadResult) {
                    results.innerHTML += `<div class="test-result pass">✓ Рівень: ${newGameState.player.level}, Золото: ${newGameState.player.gold}</div>`;
                }
                
            } catch (error) {
                results.innerHTML += `<div class="test-result fail">✗ Помилка збереження: ${error.message}</div>`;
            }
        };
    </script>
</body>
</html>

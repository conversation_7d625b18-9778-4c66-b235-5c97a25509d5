<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enemy Debug - Тест ворогів</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            background: #000;
            color: #00ff00;
        }

        .debug-container {
            display: flex;
            gap: 20px;
        }

        .debug-canvas {
            border: 2px solid #00ff00;
            border-radius: 8px;
        }

        .debug-info {
            background: #111;
            border: 1px solid #333;
            padding: 15px;
            border-radius: 5px;
            font-size: 12px;
            width: 300px;
            max-height: 400px;
            overflow-y: auto;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        button {
            background: #003300;
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 10px 20px;
            cursor: pointer;
            margin: 5px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }

        button:hover {
            background: #006600;
            box-shadow: 0 0 10px #00ff00;
        }

        .status {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #333;
            border-radius: 5px;
        }

        .status.combat { border-color: #ff0000; }
        .status.peace { border-color: #00ff00; }
    </style>
</head>
<body>
    <h1>👹 Enemy Debug - Діагностика ворогів</h1>
    
    <div class="controls">
        <button onclick="startCombat()">Почати бій</button>
        <button onclick="endCombat()">Закінчити бій</button>
        <button onclick="checkGameState()">Перевірити стан гри</button>
        <button onclick="testRenderer()">Тест рендерера</button>
        <button onclick="clearLog()">Очистити лог</button>
    </div>

    <div class="debug-container">
        <canvas id="debugCanvas" width="600" height="400" class="debug-canvas"></canvas>
        
        <div class="debug-info" id="debugInfo">
            <h3>Інформація про діагностику:</h3>
            <div id="gameStateInfo"></div>
            <div id="logContainer"></div>
        </div>
    </div>

    <script type="module">
        let renderer = null;
        let gameState = null;

        function log(message) {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(message);
        }

        function updateGameStateDisplay() {
            const gameStateInfo = document.getElementById('gameStateInfo');
            if (gameState) {
                gameStateInfo.innerHTML = `
                    <div class="status ${gameState.inCombat ? 'combat' : 'peace'}">
                        <strong>Стан гри:</strong><br>
                        В бою: ${gameState.isInCombat ? 'ТАК' : 'НІ'}<br>
                        Поточний ворог: ${gameState.currentEnemy ? gameState.currentEnemy.name : 'Немає'}<br>
                        Поверх: ${gameState.currentFloor}<br>
                        Кімната: ${gameState.currentRoom}
                    </div>
                `;
            } else {
                gameStateInfo.innerHTML = '<div class="status">Гра не ініціалізована</div>';
            }
        }

        async function initTest() {
            try {
                log('Ініціалізація тесту...');
                
                const { Renderer } = await import('./js/renderer.js');
                
                const canvas = document.getElementById('debugCanvas');
                const ctx = canvas.getContext('2d');
                renderer = new Renderer(canvas, ctx);
                
                // Create mock game state
                gameState = {
                    currentFloor: 2,
                    currentRoom: 1,
                    currentLocation: { name: 'Тестова локація' },
                    isInCombat: false,
                    player: {
                        name: 'Тестовий герой',
                        level: 10,
                        hp: 75,
                        maxHp: 100,
                        equipment: {
                            weapon: { name: 'Меч' },
                            shield: { name: 'Щит' }
                        }
                    },
                    currentEnemy: null,
                    effects: []
                };

                updateGameStateDisplay();
                log('Тест ініціалізовано успішно');
                
            } catch (error) {
                log(`Помилка ініціалізації: ${error.message}`);
                console.error('Failed to initialize test:', error);
            }
        }

        function renderScene() {
            if (!renderer || !gameState) return;

            try {
                // Clear with biome background
                renderer.clear(gameState);
                
                // Render world without floor
                renderer.renderWorldWithoutFloor(gameState);
                
                log('Сцена відрендерена');
                
            } catch (error) {
                log(`Помилка рендерингу: ${error.message}`);
                console.error('Render error:', error);
            }
        }

        window.startCombat = function() {
            if (!gameState) {
                log('Спочатку ініціалізуйте тест');
                return;
            }

            gameState.isInCombat = true;
            gameState.currentEnemy = {
                name: 'Тестовий гоблін',
                type: 'goblin',
                level: 5,
                hp: 30,
                maxHp: 50
            };

            updateGameStateDisplay();
            renderScene();
            log('Бій почато - ворог має з\'явитися');
        };

        window.endCombat = function() {
            if (!gameState) return;

            gameState.isInCombat = false;
            gameState.currentEnemy = null;

            updateGameStateDisplay();
            renderScene();
            log('Бій закінчено - ворог має зникнути');
        };

        window.checkGameState = function() {
            if (!gameState) {
                log('GameState не ініціалізовано');
                return;
            }

            log('=== СТАН ГРИ ===');
            log(`isInCombat: ${gameState.isInCombat}`);
            log(`currentEnemy: ${gameState.currentEnemy ? 'Є' : 'Немає'}`);
            if (gameState.currentEnemy) {
                log(`Enemy name: ${gameState.currentEnemy.name}`);
                log(`Enemy type: ${gameState.currentEnemy.type}`);
                log(`Enemy HP: ${gameState.currentEnemy.hp}/${gameState.currentEnemy.maxHp}`);
            }
            log(`currentFloor: ${gameState.currentFloor}`);
            log('================');
        };

        window.testRenderer = function() {
            if (!renderer || !gameState) {
                log('Спочатку ініціалізуйте тест');
                return;
            }

            log('=== ТЕСТ РЕНДЕРЕРА ===');
            
            // Test drawCharacters directly
            try {
                renderer.drawCharacters(gameState);
                log('drawCharacters виконано успішно');
            } catch (error) {
                log(`Помилка в drawCharacters: ${error.message}`);
            }

            // Test drawEnemy directly if enemy exists
            if (gameState.currentEnemy) {
                try {
                    renderer.drawEnemy(gameState.currentEnemy);
                    log('drawEnemy виконано успішно');
                } catch (error) {
                    log(`Помилка в drawEnemy: ${error.message}`);
                }
            }

            log('=====================');
        };

        window.clearLog = function() {
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML = '';
        };

        // Auto-start test
        window.addEventListener('load', () => {
            log('Сторінка діагностики ворогів завантажена');
            initTest();
        });
    </script>
</body>
</html>

// Audio manager for procedural sound generation
export class AudioManager {
    constructor() {
        this.audioContext = null;
        this.masterGain = null;
        this.musicGain = null;
        this.sfxGain = null;
        
        // Music state
        this.currentTrack = null;
        this.musicTempo = 120; // BPM
        this.musicKey = 'C';
        this.musicScale = [0, 2, 4, 5, 7, 9, 11]; // Major scale
        
        // Sound effects cache
        this.sfxCache = new Map();
        
        // Settings
        this.masterVolume = 0.3;
        this.musicVolume = 0.5;
        this.sfxVolume = 0.7;
        this.enabled = true;
        
        // Music progression
        this.currentChord = 0;
        this.chordProgression = [0, 3, 4, 0]; // I-vi-V-I
        this.nextNoteTime = 0;
        this.noteIndex = 0;
    }
    
    async init() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // Create gain nodes
            this.masterGain = this.audioContext.createGain();
            this.musicGain = this.audioContext.createGain();
            this.sfxGain = this.audioContext.createGain();
            
            // Connect gain nodes
            this.musicGain.connect(this.masterGain);
            this.sfxGain.connect(this.masterGain);
            this.masterGain.connect(this.audioContext.destination);
            
            // Set initial volumes
            this.updateVolumes();
            
            console.log('Audio system initialized');
        } catch (error) {
            console.warn('Audio initialization failed:', error);
            this.enabled = false;
        }
    }
    
    updateVolumes() {
        if (!this.enabled || !this.masterGain) return;

        this.masterGain.gain.value = this.masterVolume;
        this.musicGain.gain.value = this.musicVolume;
        this.sfxGain.gain.value = this.sfxVolume;
    }
    
    // Utility functions for music theory
    noteToFrequency(note, octave = 4) {
        const A4 = 440;
        const semitoneRatio = Math.pow(2, 1/12);
        const noteOffsets = {
            'C': -9, 'C#': -8, 'D': -7, 'D#': -6,
            'E': -5, 'F': -4, 'F#': -3, 'G': -2,
            'G#': -1, 'A': 0, 'A#': 1, 'B': 2
        };
        
        const offset = noteOffsets[note] + (octave - 4) * 12;
        return A4 * Math.pow(semitoneRatio, offset);
    }
    
    scaleNoteToFrequency(scaleIndex, octave = 4) {
        const noteIndex = this.musicScale[scaleIndex % this.musicScale.length];
        const actualOctave = octave + Math.floor(scaleIndex / this.musicScale.length);
        const semitoneRatio = Math.pow(2, 1/12);
        const baseFreq = this.noteToFrequency('C', actualOctave);
        return baseFreq * Math.pow(semitoneRatio, noteIndex);
    }
    
    // Sound effect generators
    createOscillator(frequency, type = 'sine', duration = 0.5) {
        if (!this.enabled) return null;
        
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.type = type;
        oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
        
        // ADSR envelope
        const now = this.audioContext.currentTime;
        gainNode.gain.setValueAtTime(0, now);
        gainNode.gain.linearRampToValueAtTime(0.3, now + 0.01); // Attack
        gainNode.gain.exponentialRampToValueAtTime(0.1, now + 0.1); // Decay
        gainNode.gain.setValueAtTime(0.1, now + duration - 0.1); // Sustain
        gainNode.gain.exponentialRampToValueAtTime(0.001, now + duration); // Release
        
        oscillator.connect(gainNode);
        gainNode.connect(this.sfxGain);
        
        oscillator.start(now);
        oscillator.stop(now + duration);
        
        return { oscillator, gainNode };
    }
    
    createNoise(duration = 0.2, filterFreq = 1000) {
        if (!this.enabled) return null;
        
        const bufferSize = this.audioContext.sampleRate * duration;
        const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
        const data = buffer.getChannelData(0);
        
        // Generate white noise
        for (let i = 0; i < bufferSize; i++) {
            data[i] = Math.random() * 2 - 1;
        }
        
        const source = this.audioContext.createBufferSource();
        const filter = this.audioContext.createBiquadFilter();
        const gainNode = this.audioContext.createGain();
        
        source.buffer = buffer;
        filter.type = 'lowpass';
        filter.frequency.setValueAtTime(filterFreq, this.audioContext.currentTime);
        
        // Envelope
        const now = this.audioContext.currentTime;
        gainNode.gain.setValueAtTime(0.5, now);
        gainNode.gain.exponentialRampToValueAtTime(0.001, now + duration);
        
        source.connect(filter);
        filter.connect(gainNode);
        gainNode.connect(this.sfxGain);
        
        source.start(now);
        
        return { source, filter, gainNode };
    }
    
    // Specific sound effects
    playAttackSound() {
        if (!this.enabled) return;
        
        // Sword swing - quick frequency sweep
        const osc = this.createOscillator(200, 'sawtooth', 0.15);
        if (osc) {
            osc.oscillator.frequency.exponentialRampToValueAtTime(
                100, this.audioContext.currentTime + 0.15
            );
        }
        
        // Add some noise for impact
        setTimeout(() => {
            this.createNoise(0.05, 500);
        }, 100);
    }
    
    playHitSound(damage) {
        if (!this.enabled) return;
        
        // Higher damage = lower pitch
        const frequency = Math.max(150, 400 - damage * 5);
        this.createOscillator(frequency, 'square', 0.1);
        
        // Add noise burst
        this.createNoise(0.08, 300);
    }
    
    playHealSound() {
        if (!this.enabled) return;
        
        // Ascending arpeggio
        [0, 2, 4, 7].forEach((note, index) => {
            setTimeout(() => {
                const freq = this.scaleNoteToFrequency(note, 5);
                this.createOscillator(freq, 'sine', 0.3);
            }, index * 100);
        });
    }
    
    playLevelUpSound() {
        if (!this.enabled) return;

        // Triumphant fanfare
        const melody = [0, 2, 4, 7, 9, 12];
        melody.forEach((note, index) => {
            setTimeout(() => {
                const freq = this.scaleNoteToFrequency(note, 4);
                this.createOscillator(freq, 'triangle', 0.5);

                // Add harmony
                const harmonyFreq = this.scaleNoteToFrequency(note + 2, 4);
                this.createOscillator(harmonyFreq, 'sine', 0.5);
            }, index * 150);
        });
    }

    playPrestigeSound() {
        if (!this.enabled) return;

        // Epic prestige fanfare
        const melody = [0, 4, 7, 12, 16, 19, 24];
        melody.forEach((note, index) => {
            setTimeout(() => {
                const freq = this.scaleNoteToFrequency(note, 3);
                this.createOscillator(freq, 'sawtooth', 1.0);

                // Add multiple harmonies
                const harmony1 = this.scaleNoteToFrequency(note + 4, 3);
                const harmony2 = this.scaleNoteToFrequency(note + 7, 3);
                this.createOscillator(harmony1, 'triangle', 1.0);
                this.createOscillator(harmony2, 'sine', 1.0);
            }, index * 200);
        });
    }

    playBossSound() {
        if (!this.enabled) return;

        // Ominous boss theme
        const bassNotes = [0, 0, 3, 3, 5, 5, 7, 7];
        bassNotes.forEach((note, index) => {
            setTimeout(() => {
                const freq = this.scaleNoteToFrequency(note, 1);
                this.createOscillator(freq, 'sawtooth', 0.8);

                // Add dissonant harmony
                const dissonantFreq = this.scaleNoteToFrequency(note + 1, 2);
                this.createOscillator(dissonantFreq, 'square', 0.4);
            }, index * 300);
        });
    }

    playSkillUpSound() {
        if (!this.enabled) return;

        // Magical skill up sound
        const notes = [7, 9, 12, 14];
        notes.forEach((note, index) => {
            setTimeout(() => {
                const freq = this.scaleNoteToFrequency(note, 5);
                this.createOscillator(freq, 'sine', 0.4);

                // Add sparkle effect
                const sparkleFreq = freq * 2;
                this.createOscillator(sparkleFreq, 'triangle', 0.2);
            }, index * 100);
        });
    }

    playCriticalHitSound() {
        if (!this.enabled) return;

        // Sharp critical hit sound
        const freq = 800;
        this.createOscillator(freq, 'square', 0.15);

        // Add impact noise
        setTimeout(() => {
            this.createNoise(0.1, 2000);
        }, 50);

        // Add echo effect
        setTimeout(() => {
            this.createOscillator(freq * 0.7, 'triangle', 0.2);
        }, 100);
    }

    playItemDropSound(rarity = 'common') {
        if (!this.enabled) return;

        const rarityFreqs = {
            'common': 400,
            'uncommon': 500,
            'rare': 600,
            'epic': 700,
            'legendary': 800
        };

        const baseFreq = rarityFreqs[rarity] || 400;
        const duration = rarity === 'legendary' ? 1.0 : 0.3;

        // Main chime
        this.createOscillator(baseFreq, 'sine', duration);

        // Add harmonics for higher rarities
        if (rarity !== 'common') {
            this.createOscillator(baseFreq * 1.5, 'triangle', duration * 0.7);
        }

        if (rarity === 'epic' || rarity === 'legendary') {
            this.createOscillator(baseFreq * 2, 'sine', duration * 0.5);
        }

        if (rarity === 'legendary') {
            // Special legendary effect
            setTimeout(() => {
                this.createOscillator(baseFreq * 0.5, 'sawtooth', 0.5);
            }, 200);
        }
    }
    
    playGoldSound() {
        if (!this.enabled) return;
        
        // Coin clink - metallic sound
        const freq = 800 + Math.random() * 400;
        const osc = this.createOscillator(freq, 'triangle', 0.2);
        if (osc) {
            // Add slight vibrato
            const lfo = this.audioContext.createOscillator();
            const lfoGain = this.audioContext.createGain();
            
            lfo.frequency.setValueAtTime(5, this.audioContext.currentTime);
            lfoGain.gain.setValueAtTime(10, this.audioContext.currentTime);
            
            lfo.connect(lfoGain);
            lfoGain.connect(osc.oscillator.frequency);
            
            lfo.start();
            lfo.stop(this.audioContext.currentTime + 0.2);
        }
    }
    
    playDeathSound() {
        if (!this.enabled) return;
        
        // Descending death sound
        const osc = this.createOscillator(300, 'sawtooth', 1.0);
        if (osc) {
            osc.oscillator.frequency.exponentialRampToValueAtTime(
                50, this.audioContext.currentTime + 1.0
            );
        }
    }
    
    // Background music generation
    startBackgroundMusic(gameState) {
        if (!this.enabled || this.currentTrack) return;

        this.adaptMusicToGameState(gameState);
        this.scheduleNextNote();
        this.currentTrack = this.selectMusicTrack(gameState);
    }

    selectMusicTrack(gameState) {
        if (gameState.currentEnemy && gameState.currentEnemy.isBoss) {
            return 'boss';
        } else if (gameState.isInCombat) {
            return 'combat';
        } else if (gameState.currentFloor >= 20) {
            return 'deep';
        } else if (gameState.currentFloor >= 10) {
            return 'dungeon';
        } else {
            return 'exploration';
        }
    }
    
    stopBackgroundMusic() {
        this.currentTrack = null;
    }
    
    adaptMusicToGameState(gameState) {
        // Adapt tempo based on combat state
        if (gameState.currentEnemy) {
            this.musicTempo = 140 + gameState.currentFloor * 2; // Faster in combat
        } else {
            this.musicTempo = 100 + gameState.currentFloor; // Slower when exploring
        }
        
        // Change key based on floor
        const keys = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];
        this.musicKey = keys[gameState.currentFloor % keys.length];
    }
    
    scheduleNextNote() {
        if (!this.enabled || !this.currentTrack) return;
        
        const now = this.audioContext.currentTime;
        const secondsPerBeat = 60.0 / this.musicTempo;
        
        if (now >= this.nextNoteTime) {
            this.playBackgroundNote();
            this.nextNoteTime += secondsPerBeat;
            this.noteIndex++;
        }
        
        // Schedule next call
        setTimeout(() => this.scheduleNextNote(), 25);
    }
    
    playBackgroundNote() {
        if (!this.enabled) return;

        switch (this.currentTrack) {
            case 'exploration':
                this.playExplorationMusic();
                break;
            case 'combat':
                this.playCombatMusic();
                break;
            case 'boss':
                this.playBossMusic();
                break;
            case 'dungeon':
                this.playDungeonMusic();
                break;
            case 'deep':
                this.playDeepMusic();
                break;
            default:
                this.playExplorationMusic();
        }
    }

    playExplorationMusic() {
        const chordRoot = this.chordProgression[this.currentChord];
        const beat = this.noteIndex % 4;

        // Gentle bass
        if (beat === 0 || beat === 2) {
            const bassNote = chordRoot;
            const freq = this.scaleNoteToFrequency(bassNote, 2);
            const osc = this.createOscillator(freq, 'sine', 0.6);
            if (osc) {
                osc.gainNode.disconnect();
                osc.gainNode.connect(this.musicGain);
                osc.gainNode.gain.setValueAtTime(0.08, this.audioContext.currentTime);
            }
        }

        // Peaceful melody
        if (beat === 1 || beat === 3) {
            const melodyNotes = [chordRoot, chordRoot + 2, chordRoot + 4];
            const note = melodyNotes[Math.floor(Math.random() * melodyNotes.length)];
            const freq = this.scaleNoteToFrequency(note, 4);
            const osc = this.createOscillator(freq, 'triangle', 0.4);
            if (osc) {
                osc.gainNode.disconnect();
                osc.gainNode.connect(this.musicGain);
                osc.gainNode.gain.setValueAtTime(0.04, this.audioContext.currentTime);
            }
        }

        if (beat === 3) {
            this.currentChord = (this.currentChord + 1) % this.chordProgression.length;
        }
    }

    playCombatMusic() {
        const chordRoot = this.chordProgression[this.currentChord];
        const beat = this.noteIndex % 4;

        // Aggressive bass
        if (beat === 0 || beat === 2) {
            const bassNote = chordRoot;
            const freq = this.scaleNoteToFrequency(bassNote, 1);
            const osc = this.createOscillator(freq, 'sawtooth', 0.3);
            if (osc) {
                osc.gainNode.disconnect();
                osc.gainNode.connect(this.musicGain);
                osc.gainNode.gain.setValueAtTime(0.12, this.audioContext.currentTime);
            }
        }

        // Intense melody
        if (beat === 1 || beat === 3) {
            const melodyNotes = [chordRoot + 7, chordRoot + 9, chordRoot + 12];
            const note = melodyNotes[Math.floor(Math.random() * melodyNotes.length)];
            const freq = this.scaleNoteToFrequency(note, 4);
            const osc = this.createOscillator(freq, 'square', 0.2);
            if (osc) {
                osc.gainNode.disconnect();
                osc.gainNode.connect(this.musicGain);
                osc.gainNode.gain.setValueAtTime(0.06, this.audioContext.currentTime);
            }
        }

        if (beat === 3) {
            this.currentChord = (this.currentChord + 1) % this.chordProgression.length;
        }
    }

    playBossMusic() {
        const beat = this.noteIndex % 8;

        // Ominous bass pattern
        if (beat === 0 || beat === 4) {
            const freq = this.scaleNoteToFrequency(0, 1);
            const osc = this.createOscillator(freq, 'sawtooth', 0.8);
            if (osc) {
                osc.gainNode.disconnect();
                osc.gainNode.connect(this.musicGain);
                osc.gainNode.gain.setValueAtTime(0.15, this.audioContext.currentTime);
            }
        }

        // Dissonant harmony
        if (beat === 2 || beat === 6) {
            const freq = this.scaleNoteToFrequency(1, 2); // Dissonant note
            const osc = this.createOscillator(freq, 'square', 0.4);
            if (osc) {
                osc.gainNode.disconnect();
                osc.gainNode.connect(this.musicGain);
                osc.gainNode.gain.setValueAtTime(0.08, this.audioContext.currentTime);
            }
        }
    }

    playDungeonMusic() {
        const chordRoot = this.chordProgression[this.currentChord];
        const beat = this.noteIndex % 6;

        // Mysterious bass
        if (beat === 0 || beat === 3) {
            const bassNote = chordRoot;
            const freq = this.scaleNoteToFrequency(bassNote, 2);
            const osc = this.createOscillator(freq, 'triangle', 0.5);
            if (osc) {
                osc.gainNode.disconnect();
                osc.gainNode.connect(this.musicGain);
                osc.gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
            }
        }

        // Eerie melody
        if (beat === 2 || beat === 5) {
            const melodyNotes = [chordRoot + 3, chordRoot + 6, chordRoot + 10];
            const note = melodyNotes[Math.floor(Math.random() * melodyNotes.length)];
            const freq = this.scaleNoteToFrequency(note, 5);
            const osc = this.createOscillator(freq, 'sine', 0.3);
            if (osc) {
                osc.gainNode.disconnect();
                osc.gainNode.connect(this.musicGain);
                osc.gainNode.gain.setValueAtTime(0.03, this.audioContext.currentTime);
            }
        }

        if (beat === 5) {
            this.currentChord = (this.currentChord + 1) % this.chordProgression.length;
        }
    }

    playDeepMusic() {
        const beat = this.noteIndex % 8;

        // Deep, foreboding bass
        if (beat === 0 || beat === 4) {
            const freq = this.scaleNoteToFrequency(-12, 1); // Very low note
            const osc = this.createOscillator(freq, 'sawtooth', 1.0);
            if (osc) {
                osc.gainNode.disconnect();
                osc.gainNode.connect(this.musicGain);
                osc.gainNode.gain.setValueAtTime(0.2, this.audioContext.currentTime);
            }
        }

        // Sparse, haunting melody
        if (beat === 3 || beat === 7) {
            const freq = this.scaleNoteToFrequency(Math.floor(Math.random() * 7), 6);
            const osc = this.createOscillator(freq, 'sine', 0.8);
            if (osc) {
                osc.gainNode.disconnect();
                osc.gainNode.connect(this.musicGain);
                osc.gainNode.gain.setValueAtTime(0.02, this.audioContext.currentTime);
            }
        }
    }
    
    // Main update function
    update(gameState) {
        if (!this.enabled) return;
        
        // Start music if not playing
        if (!this.currentTrack) {
            this.startBackgroundMusic(gameState);
        }
        
        // Adapt music to current state
        this.adaptMusicToGameState(gameState);
    }
    
    // Settings
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        if (this.masterGain) {
            this.updateVolumes();
        }
    }

    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));
        if (this.musicGain) {
            this.updateVolumes();
        }
    }

    setSfxVolume(volume) {
        this.sfxVolume = Math.max(0, Math.min(1, volume));
        if (this.sfxGain) {
            this.updateVolumes();
        }
    }
    
    toggle() {
        this.enabled = !this.enabled;
        if (!this.enabled) {
            this.stopBackgroundMusic();
        }
    }
}

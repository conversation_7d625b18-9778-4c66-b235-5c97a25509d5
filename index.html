<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Idle Roguelike Adventure</title>
    <link rel="stylesheet" href="css/animations.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            overflow: hidden;
            user-select: none;
        }

        #gameContainer {
            display: flex;
            width: 100vw;
            height: 100vh;
        }

        #gameCanvas {
            background: #000;
            border: 2px solid #00ff00;
            flex: 1;
            max-width: 70%;
        }

        #ui {
            width: 30%;
            min-width: 300px;
            background: #0a0a0a;
            border-left: 2px solid #00ff00;
            padding: 10px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .ui-panel {
            border: 1px solid #00ff00;
            padding: 10px;
            background: #111;
        }

        .ui-panel h3 {
            color: #ffff00;
            margin-bottom: 5px;
            text-align: center;
        }

        .stat-line {
            display: flex;
            justify-content: space-between;
            margin: 2px 0;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #333;
            border: 1px solid #00ff00;
            margin: 5px 0;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ff00, #ffff00);
            transition: width 0.3s ease;
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            color: #fff;
            text-shadow: 1px 1px 2px #000;
        }

        .button {
            background: #003300;
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 8px 16px;
            cursor: pointer;
            transition: all 0.2s;
            margin: 2px;
        }

        .button:hover {
            background: #006600;
            box-shadow: 0 0 10px #00ff00;
        }

        .button:active {
            background: #009900;
        }

        .button:disabled {
            background: #222;
            color: #666;
            cursor: not-allowed;
        }

        .inventory-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 2px;
            margin-top: 5px;
        }

        .inventory-slot {
            width: 40px;
            height: 40px;
            border: 1px solid #00ff00;
            background: #111;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
        }

        .inventory-slot:hover {
            background: #333;
        }

        .item-icon {
            width: 30px;
            height: 30px;
            background: #00ff00;
            border-radius: 2px;
        }

        .log {
            height: 150px;
            overflow-y: auto;
            background: #000;
            border: 1px solid #00ff00;
            padding: 5px;
            font-size: 12px;
        }

        .log-entry {
            margin: 1px 0;
            opacity: 0.8;
        }

        .log-entry.combat {
            color: #ff6666;
        }

        .log-entry.loot {
            color: #66ff66;
        }

        .log-entry.level {
            color: #ffff66;
        }

        .log-entry.achievement {
            color: #ff9900;
            font-weight: bold;
        }

        .log-entry.boss {
            color: #ff0066;
            font-weight: bold;
        }

        .skill-line {
            display: flex;
            justify-content: space-between;
            margin: 2px 0;
            font-size: 12px;
        }

        .achievement-count {
            text-align: center;
            margin-bottom: 5px;
        }

        .boss-health-bar {
            width: 100%;
            height: 25px;
            background: #333;
            border: 2px solid #ff0000;
            margin: 5px 0;
            position: relative;
        }

        .boss-health-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff0000, #ff6666);
            transition: width 0.3s ease;
        }

        .boss-name {
            color: #ff0066;
            font-weight: bold;
            text-align: center;
        }

        .boss-phase {
            color: #ffff00;
            font-size: 12px;
            text-align: center;
        }

        @media (max-width: 768px) {
            #gameContainer {
                flex-direction: column;
            }
            
            #gameCanvas {
                max-width: 100%;
                height: 60vh;
            }
            
            #ui {
                width: 100%;
                height: 40vh;
                min-width: auto;
            }
        }

        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .loading-text {
            color: #00ff00;
            font-size: 24px;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div id="loading" class="loading">
        <div class="loading-text">Завантаження гри...</div>
    </div>

    <div id="gameContainer" style="display: none;">
        <canvas id="gameCanvas"></canvas>
        
        <div id="ui">
            <!-- Player Stats -->
            <div class="ui-panel">
                <h3>Герой</h3>
                <div class="stat-line">
                    <span>Рівень:</span>
                    <span id="playerLevel">1</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="xpBar" style="width: 0%"></div>
                    <div class="progress-text" id="xpText">0 / 100</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="hpBar" style="width: 100%; background: #ff4444;"></div>
                    <div class="progress-text" id="hpText">100 / 100</div>
                </div>
                <div class="stat-line">
                    <span>Атака:</span>
                    <span id="playerAttack">10</span>
                </div>
                <div class="stat-line">
                    <span>Захист:</span>
                    <span id="playerDefense">5</span>
                </div>
                <div class="stat-line">
                    <span>Золото:</span>
                    <span id="playerGold">0</span>
                </div>
            </div>

            <!-- Current Location -->
            <div class="ui-panel">
                <h3>Локація</h3>
                <div id="locationName">Початкова печера</div>
                <div class="stat-line">
                    <span>Поверх:</span>
                    <span id="currentFloor">1</span>
                </div>
                <div class="stat-line">
                    <span>Кімната:</span>
                    <span id="currentRoom">1/10</span>
                </div>
                <button class="button" id="nextRoomBtn">Наступна кімната</button>
                <button class="button" id="nextFloorBtn">Наступний поверх</button>
            </div>

            <!-- Combat -->
            <div class="ui-panel">
                <h3>Бій</h3>
                <div id="enemyInfo" style="display: none;">
                    <div id="enemyName">Ворог</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="enemyHpBar" style="width: 100%; background: #ff4444;"></div>
                        <div class="progress-text" id="enemyHpText">100 / 100</div>
                    </div>
                </div>
                <div id="noCombat" style="text-align: center; color: #666;">
                    Немає активного бою
                </div>
                <button class="button" id="autoFightBtn">Авто-бій: ВИМК</button>
            </div>

            <!-- Inventory -->
            <div class="ui-panel">
                <h3>Інвентар</h3>
                <div class="inventory-grid" id="inventory">
                    <!-- Inventory slots will be generated by JS -->
                </div>
            </div>

            <!-- Skills -->
            <div class="ui-panel">
                <h3>Навички</h3>
                <div id="skillsContainer">
                    <div class="skill-line">
                        <span>Бій:</span>
                        <span id="combatSkill">1 (0/100)</span>
                    </div>
                    <div class="skill-line">
                        <span>Дослідження:</span>
                        <span id="explorationSkill">1 (0/100)</span>
                    </div>
                    <div class="skill-line">
                        <span>Виживання:</span>
                        <span id="survivalSkill">1 (0/100)</span>
                    </div>
                    <div class="skill-line">
                        <span>Удача:</span>
                        <span id="luckSkill">1 (0/100)</span>
                    </div>
                    <div class="stat-line">
                        <span>Очки навичок:</span>
                        <span id="skillPoints">0</span>
                    </div>
                </div>
            </div>

            <!-- Achievements -->
            <div class="ui-panel">
                <h3>Досягнення</h3>
                <div id="achievementsContainer">
                    <div class="achievement-count">
                        <span>Розблоковано: </span>
                        <span id="achievementCount">0/10</span>
                    </div>
                    <button class="button" id="showAchievementsBtn">Показати всі</button>
                </div>
            </div>

            <!-- Prestige -->
            <div class="ui-panel">
                <h3>Престиж</h3>
                <div id="prestigeContainer">
                    <div class="stat-line">
                        <span>Рівень престижу:</span>
                        <span id="prestigeLevel">0</span>
                    </div>
                    <div class="stat-line">
                        <span>Очки престижу:</span>
                        <span id="prestigePoints">0</span>
                    </div>
                    <button class="button" id="prestigeBtn" disabled>Престиж (0 очок)</button>
                    <button class="button" id="prestigeUpgradesBtn">Покращення</button>
                </div>
            </div>

            <!-- Game Log -->
            <div class="ui-panel">
                <h3>Журнал</h3>
                <div class="log" id="gameLog">
                    <div class="log-entry">Гра розпочата!</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Game Scripts -->
    <script type="module" src="js/main.js"></script>
</body>
</html>

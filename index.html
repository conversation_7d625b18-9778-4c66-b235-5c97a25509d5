<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Idle Roguelike Adventure</title>
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/icons.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            overflow: hidden;
            user-select: none;
        }

        #gameContainer {
            display: flex;
            width: 100vw;
            height: 100vh;
        }

        #gameCanvas {
            background:
                radial-gradient(circle at 20% 30%, rgba(0, 255, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(255, 255, 0, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(0, 100, 255, 0.08) 0%, transparent 50%),
                linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0f0f0f 100%);
            border: 2px solid #00ff00;
            border-radius: 8px;
            flex: 1;
            max-width: 70%;
            box-shadow:
                inset 0 0 50px rgba(0, 255, 0, 0.1),
                0 0 20px rgba(0, 255, 0, 0.2),
                0 0 40px rgba(0, 255, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        #gameCanvas::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                repeating-linear-gradient(
                    0deg,
                    transparent,
                    transparent 2px,
                    rgba(0, 255, 0, 0.03) 2px,
                    rgba(0, 255, 0, 0.03) 4px
                );
            pointer-events: none;
            z-index: 1;
        }

        #ui {
            width: 30%;
            min-width: 300px;
            background:
                linear-gradient(180deg, #0a0a0a 0%, #151515 50%, #0a0a0a 100%);
            border-left: 2px solid #00ff00;
            border-radius: 0 8px 8px 0;
            padding: 15px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
            box-shadow:
                inset 2px 0 10px rgba(0, 255, 0, 0.1),
                -5px 0 15px rgba(0, 0, 0, 0.5);
            position: relative;
        }

        #ui::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 2px;
            height: 100%;
            background: linear-gradient(
                180deg,
                transparent 0%,
                #00ff00 20%,
                #ffff00 50%,
                #00ff00 80%,
                transparent 100%
            );
            animation: ui-glow 3s ease-in-out infinite;
        }

        @keyframes ui-glow {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .ui-panel {
            border: 1px solid #00ff00;
            border-radius: 8px;
            padding: 15px;
            background:
                linear-gradient(135deg, #111 0%, #1a1a1a 50%, #111 100%);
            box-shadow:
                0 4px 8px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(0, 255, 0, 0.2),
                inset 0 -1px 0 rgba(0, 255, 0, 0.1);
            position: relative;
            transition: all 0.3s ease;
        }

        .ui-panel:hover {
            border-color: #ffff00;
            box-shadow:
                0 6px 12px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 0, 0.2),
                inset 0 -1px 0 rgba(255, 255, 0, 0.1),
                0 0 15px rgba(0, 255, 0, 0.3);
            transform: translateY(-2px);
        }

        .ui-panel::before {
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            background: linear-gradient(
                45deg,
                #00ff00,
                #ffff00,
                #00ff00,
                #ffff00
            );
            border-radius: 8px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .ui-panel:hover::before {
            opacity: 0.3;
        }

        .ui-panel h3 {
            color: #ffff00;
            margin: -5px -5px 15px -5px;
            text-align: center;
            background:
                linear-gradient(90deg,
                    transparent 0%,
                    rgba(255, 255, 0, 0.1) 20%,
                    rgba(255, 255, 0, 0.2) 50%,
                    rgba(255, 255, 0, 0.1) 80%,
                    transparent 100%
                );
            padding: 10px;
            border-bottom: 1px solid rgba(255, 255, 0, 0.3);
            font-size: 16px;
            font-weight: bold;
            text-shadow:
                0 0 5px rgba(255, 255, 0, 0.5),
                0 0 10px rgba(255, 255, 0, 0.3);
            letter-spacing: 1px;
            position: relative;
        }

        .ui-panel h3::before,
        .ui-panel h3::after {
            content: '◆';
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            color: #00ff00;
            font-size: 12px;
            animation: panel-glow 2s ease-in-out infinite;
        }

        .ui-panel h3::before {
            left: 10px;
        }

        .ui-panel h3::after {
            right: 10px;
        }

        @keyframes panel-glow {
            0%, 100% { opacity: 0.5; text-shadow: 0 0 5px #00ff00; }
            50% { opacity: 1; text-shadow: 0 0 10px #00ff00, 0 0 15px #00ff00; }
        }

        .stat-line {
            display: flex;
            justify-content: space-between;
            margin: 2px 0;
        }

        .progress-bar {
            width: 100%;
            height: 24px;
            background:
                linear-gradient(90deg, #1a1a1a 0%, #333 50%, #1a1a1a 100%);
            border: 1px solid #00ff00;
            border-radius: 12px;
            margin: 8px 0;
            position: relative;
            overflow: hidden;
            box-shadow:
                inset 0 2px 4px rgba(0, 0, 0, 0.5),
                0 1px 2px rgba(0, 255, 0, 0.2);
        }

        .progress-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 2px,
                    rgba(0, 255, 0, 0.1) 2px,
                    rgba(0, 255, 0, 0.1) 4px
                );
            animation: progress-scan 2s linear infinite;
        }

        @keyframes progress-scan {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-fill {
            height: 100%;
            background:
                linear-gradient(90deg,
                    #00ff00 0%,
                    #66ff00 25%,
                    #ffff00 50%,
                    #ff6600 75%,
                    #ff0000 100%
                );
            border-radius: 11px;
            transition: width 0.5s ease;
            position: relative;
            box-shadow:
                0 0 10px rgba(0, 255, 0, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                linear-gradient(90deg,
                    rgba(255, 255, 255, 0.3) 0%,
                    transparent 50%,
                    rgba(255, 255, 255, 0.1) 100%
                );
            border-radius: 11px;
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            color: #fff;
            text-shadow: 1px 1px 2px #000;
        }

        .button {
            background:
                linear-gradient(135deg, #003300 0%, #004400 50%, #003300 100%);
            border: 1px solid #00ff00;
            border-radius: 6px;
            color: #00ff00;
            padding: 10px 18px;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 3px;
            text-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
            box-shadow:
                0 2px 4px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(0, 255, 0, 0.2);
            position: relative;
            overflow: hidden;
        }

        .button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(0, 255, 0, 0.2),
                transparent
            );
            transition: left 0.5s ease;
        }

        .button:hover::before {
            left: 100%;
        }

        .button:hover {
            background:
                linear-gradient(135deg, #00ff00 0%, #66ff00 50%, #00ff00 100%);
            color: #000;
            border-color: #ffff00;
            box-shadow:
                0 4px 8px rgba(0, 255, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3),
                0 0 15px rgba(0, 255, 0, 0.6);
            transform: translateY(-2px);
            text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
        }

        .button:active {
            transform: translateY(0);
            box-shadow:
                0 2px 4px rgba(0, 255, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .button:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            background: #111;
            border-color: #444;
            color: #666;
            text-shadow: none;
            box-shadow: none;
            transform: none;
        }

        .button:disabled:hover {
            background: #111;
            color: #666;
            border-color: #444;
            transform: none;
        }

        .inventory-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(45px, 1fr));
            gap: 6px;
            margin-top: 10px;
            padding: 10px;
            background:
                linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.6) 100%);
            border-radius: 8px;
            border: 1px solid rgba(0, 255, 0, 0.3);
        }

        .inventory-slot {
            width: 45px;
            height: 45px;
            border: 2px solid #333;
            border-radius: 6px;
            background:
                linear-gradient(135deg, #111 0%, #1a1a1a 50%, #111 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            box-shadow:
                inset 0 1px 0 rgba(255, 255, 255, 0.1),
                0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .inventory-slot::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 4px;
            background:
                radial-gradient(circle at center, rgba(0, 255, 0, 0.1) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .inventory-slot:hover {
            border-color: #00ff00;
            background:
                linear-gradient(135deg, #222 0%, #2a2a2a 50%, #222 100%);
            box-shadow:
                inset 0 1px 0 rgba(0, 255, 0, 0.2),
                0 2px 4px rgba(0, 0, 0, 0.4),
                0 0 10px rgba(0, 255, 0, 0.3);
            transform: translateY(-2px);
        }

        .inventory-slot:hover::before {
            opacity: 1;
        }

        .inventory-slot.has-item {
            border-color: #00ff00;
            background:
                linear-gradient(135deg, #003300 0%, #004400 50%, #003300 100%);
            box-shadow:
                inset 0 1px 0 rgba(0, 255, 0, 0.3),
                0 2px 4px rgba(0, 0, 0, 0.4),
                0 0 8px rgba(0, 255, 0, 0.4);
        }

        .inventory-slot.has-item:hover {
            border-color: #ffff00;
            box-shadow:
                inset 0 1px 0 rgba(255, 255, 0, 0.3),
                0 4px 8px rgba(0, 0, 0, 0.5),
                0 0 15px rgba(255, 255, 0, 0.5);
        }

        .item-icon {
            width: 30px;
            height: 30px;
            background: #00ff00;
            border-radius: 2px;
        }

        .log {
            height: 150px;
            overflow-y: auto;
            background: #000;
            border: 1px solid #00ff00;
            padding: 5px;
            font-size: 12px;
        }

        .log-entry {
            margin: 1px 0;
            opacity: 0.8;
        }

        .log-entry.combat {
            color: #ff6666;
        }

        .log-entry.loot {
            color: #66ff66;
        }

        .log-entry.level {
            color: #ffff66;
        }

        .log-entry.achievement {
            color: #ff9900;
            font-weight: bold;
        }

        .log-entry.boss {
            color: #ff0066;
            font-weight: bold;
        }

        .skill-line {
            display: flex;
            justify-content: space-between;
            margin: 2px 0;
            font-size: 12px;
        }

        .achievement-count {
            text-align: center;
            margin-bottom: 5px;
        }

        .boss-health-bar {
            width: 100%;
            height: 25px;
            background: #333;
            border: 2px solid #ff0000;
            margin: 5px 0;
            position: relative;
        }

        .boss-health-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff0000, #ff6666);
            transition: width 0.3s ease;
        }

        .boss-name {
            color: #ff0066;
            font-weight: bold;
            text-align: center;
        }

        .boss-phase {
            color: #ffff00;
            font-size: 12px;
            text-align: center;
        }

        /* Loading animation */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at center, #1a1a1a 0%, #000 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            transition: opacity 0.5s ease;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(0, 255, 0, 0.3);
            border-top: 3px solid #00ff00;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        .loading-text {
            color: #00ff00;
            font-size: 18px;
            text-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        /* Fade in animation for UI elements */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @media (max-width: 768px) {
            #gameContainer {
                flex-direction: column;
            }

            #gameCanvas {
                max-width: 100%;
                height: 60vh;
            }

            #ui {
                width: 100%;
                height: 40vh;
                min-width: auto;
            }

            .ui-panel {
                margin: 5px 0;
                padding: 10px;
            }

            .inventory-grid {
                grid-template-columns: repeat(auto-fill, minmax(35px, 1fr));
                gap: 4px;
            }

            .inventory-slot {
                width: 35px;
                height: 35px;
            }
        }

        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .loading-text {
            color: #00ff00;
            font-size: 24px;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">Завантаження Idle Roguelike Adventure...</div>
    </div>

    <div id="gameContainer" style="display: none;">
        <canvas id="gameCanvas"></canvas>
        
        <div id="ui">
            <!-- Player Stats -->
            <div class="ui-panel">
                <h3>Герой</h3>
                <div class="stat-line">
                    <span>Рівень:</span>
                    <span id="playerLevel">1</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="xpBar" style="width: 0%"></div>
                    <div class="progress-text" id="xpText">0 / 100</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="hpBar" style="width: 100%; background: #ff4444;"></div>
                    <div class="progress-text" id="hpText">100 / 100</div>
                </div>
                <div class="stat-line">
                    <span>Атака:</span>
                    <span id="playerAttack">10</span>
                </div>
                <div class="stat-line">
                    <span>Захист:</span>
                    <span id="playerDefense">5</span>
                </div>
                <div class="stat-line">
                    <span>Золото:</span>
                    <span id="playerGold">0</span>
                </div>
            </div>

            <!-- Current Location -->
            <div class="ui-panel">
                <h3>Локація</h3>
                <div id="locationName">Початкова печера</div>
                <div class="stat-line">
                    <span>Поверх:</span>
                    <span id="currentFloor">1</span>
                </div>
                <div class="stat-line">
                    <span>Кімната:</span>
                    <span id="currentRoom">1/10</span>
                </div>
                <button class="button" id="nextRoomBtn">Наступна кімната</button>
                <button class="button" id="nextFloorBtn">Наступний поверх</button>
                <button class="button" id="equipmentBtn">Екіпірування</button>
                <button class="button" id="shopBtn">Магазини</button>
                <button class="button" id="settingsBtn">Налаштування</button>
            </div>

            <!-- Combat -->
            <div class="ui-panel">
                <h3>Бій</h3>
                <div id="enemyInfo" style="display: none;">
                    <div id="enemyName">Ворог</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="enemyHpBar" style="width: 100%; background: #ff4444;"></div>
                        <div class="progress-text" id="enemyHpText">100 / 100</div>
                    </div>
                </div>
                <div id="noCombat" style="text-align: center; color: #666;">
                    Немає активного бою
                </div>
                <button class="button" id="autoFightBtn">Авто-бій: ВИМК</button>
            </div>

            <!-- Inventory -->
            <div class="ui-panel">
                <h3>Інвентар</h3>
                <div class="inventory-grid" id="inventory">
                    <!-- Inventory slots will be generated by JS -->
                </div>
            </div>

            <!-- Skills -->
            <div class="ui-panel">
                <h3>Навички</h3>
                <div id="skillsContainer">
                    <div class="skill-line">
                        <span>Бій:</span>
                        <span id="combatSkill">1 (0/100)</span>
                    </div>
                    <div class="skill-line">
                        <span>Дослідження:</span>
                        <span id="explorationSkill">1 (0/100)</span>
                    </div>
                    <div class="skill-line">
                        <span>Виживання:</span>
                        <span id="survivalSkill">1 (0/100)</span>
                    </div>
                    <div class="skill-line">
                        <span>Удача:</span>
                        <span id="luckSkill">1 (0/100)</span>
                    </div>
                    <div class="stat-line">
                        <span>Очки навичок:</span>
                        <span id="skillPoints">0</span>
                    </div>
                </div>
            </div>

            <!-- Achievements -->
            <div class="ui-panel">
                <h3>Досягнення</h3>
                <div id="achievementsContainer">
                    <div class="achievement-count">
                        <span>Розблоковано: </span>
                        <span id="achievementCount">0/10</span>
                    </div>
                    <button class="button" id="showAchievementsBtn">Показати всі</button>
                </div>
            </div>

            <!-- Prestige -->
            <div class="ui-panel">
                <h3>Престиж</h3>
                <div id="prestigeContainer">
                    <div class="stat-line">
                        <span>Рівень престижу:</span>
                        <span id="prestigeLevel">0</span>
                    </div>
                    <div class="stat-line">
                        <span>Очки престижу:</span>
                        <span id="prestigePoints">0</span>
                    </div>
                    <button class="button" id="prestigeBtn" disabled>Престиж (0 очок)</button>
                    <button class="button" id="prestigeUpgradesBtn">Покращення</button>
                </div>
            </div>

            <!-- Game Log -->
            <div class="ui-panel">
                <h3>Журнал</h3>
                <div class="log" id="gameLog">
                    <div class="log-entry">Гра розпочата!</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Game Scripts -->
    <script type="module" src="js/main.js"></script>
</body>
</html>

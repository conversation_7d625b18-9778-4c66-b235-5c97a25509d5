// Sprite caching system for improved performance
export class SpriteCache {
    constructor() {
        this.cache = new Map();
        this.maxCacheSize = 100; // Maximum number of cached sprites
        this.cacheHits = 0;
        this.cacheMisses = 0;
        
        // Performance monitoring
        this.renderTimes = [];
        this.maxRenderTimeHistory = 60; // Keep last 60 render times
    }
    
    // Generate a cache key for a sprite
    generateKey(type, params) {
        return `${type}_${JSON.stringify(params)}`;
    }
    
    // Get sprite from cache or generate new one
    getSprite(type, params, generator) {
        const key = this.generateKey(type, params);
        
        if (this.cache.has(key)) {
            this.cacheHits++;
            return this.cache.get(key);
        }
        
        // Generate new sprite
        this.cacheMisses++;
        const startTime = performance.now();
        const sprite = generator();
        const endTime = performance.now();
        
        // Track render time
        this.renderTimes.push(endTime - startTime);
        if (this.renderTimes.length > this.maxRenderTimeHistory) {
            this.renderTimes.shift();
        }
        
        // Cache the sprite
        this.cacheSprite(key, sprite);
        
        return sprite;
    }
    
    // Cache a sprite with LRU eviction
    cacheSprite(key, sprite) {
        // If cache is full, remove oldest entry
        if (this.cache.size >= this.maxCacheSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        
        this.cache.set(key, sprite);
    }
    
    // Clear cache
    clearCache() {
        this.cache.clear();
        this.cacheHits = 0;
        this.cacheMisses = 0;
    }
    
    // Get cache statistics
    getStats() {
        const totalRequests = this.cacheHits + this.cacheMisses;
        const hitRate = totalRequests > 0 ? (this.cacheHits / totalRequests) * 100 : 0;
        const avgRenderTime = this.renderTimes.length > 0 ? 
            this.renderTimes.reduce((a, b) => a + b, 0) / this.renderTimes.length : 0;
        
        return {
            cacheSize: this.cache.size,
            maxCacheSize: this.maxCacheSize,
            cacheHits: this.cacheHits,
            cacheMisses: this.cacheMisses,
            hitRate: hitRate.toFixed(2),
            avgRenderTime: avgRenderTime.toFixed(2)
        };
    }
    
    // Preload common sprites
    preloadCommonSprites(renderer) {
        const commonSprites = [
            // Player sprites
            { type: 'player', params: { level: 1, equipment: {} } },
            { type: 'player', params: { level: 5, equipment: {} } },
            { type: 'player', params: { level: 10, equipment: {} } },
            
            // Common enemies
            { type: 'enemy', params: { type: 'goblin', level: 1 } },
            { type: 'enemy', params: { type: 'skeleton', level: 1 } },
            { type: 'enemy', params: { type: 'orc', level: 1 } },
            
            // Common items
            { type: 'item', params: { type: 'weapon', rarity: 'common' } },
            { type: 'item', params: { type: 'armor', rarity: 'common' } },
            { type: 'item', params: { type: 'potion', rarity: 'common' } }
        ];
        
        commonSprites.forEach(sprite => {
            this.getSprite(sprite.type, sprite.params, () => {
                switch (sprite.type) {
                    case 'player':
                        return renderer.generatePlayerSprite(sprite.params);
                    case 'enemy':
                        return renderer.generateEnemySprite(sprite.params);
                    case 'item':
                        return renderer.generateItemSprite(sprite.params);
                    default:
                        return null;
                }
            });
        });
        
        console.log(`Preloaded ${commonSprites.length} common sprites`);
    }
}

// Performance monitor for frame rate and optimization
export class PerformanceMonitor {
    constructor() {
        this.frameCount = 0;
        this.lastTime = performance.now();
        this.fps = 60;
        this.frameHistory = [];
        this.maxFrameHistory = 60;
        
        // Performance thresholds
        this.targetFPS = 60;
        this.lowFPSThreshold = 30;
        this.criticalFPSThreshold = 15;
        
        // Optimization flags
        this.optimizationLevel = 0; // 0 = none, 1 = light, 2 = aggressive
        this.particleReduction = 1.0;
        this.animationQuality = 1.0;
    }
    
    // Update FPS calculation
    update() {
        const currentTime = performance.now();
        const deltaTime = currentTime - this.lastTime;
        
        if (deltaTime >= 1000) { // Update every second
            this.fps = (this.frameCount * 1000) / deltaTime;
            this.frameHistory.push(this.fps);
            
            if (this.frameHistory.length > this.maxFrameHistory) {
                this.frameHistory.shift();
            }
            
            this.frameCount = 0;
            this.lastTime = currentTime;
            
            // Auto-optimize based on performance
            this.autoOptimize();
        }
        
        this.frameCount++;
    }
    
    // Automatic optimization based on FPS
    autoOptimize() {
        const avgFPS = this.getAverageFPS();
        
        if (avgFPS < this.criticalFPSThreshold && this.optimizationLevel < 2) {
            this.optimizationLevel = 2;
            this.particleReduction = 0.3;
            this.animationQuality = 0.5;
            console.log('Performance: Aggressive optimization enabled');
        } else if (avgFPS < this.lowFPSThreshold && this.optimizationLevel < 1) {
            this.optimizationLevel = 1;
            this.particleReduction = 0.7;
            this.animationQuality = 0.8;
            console.log('Performance: Light optimization enabled');
        } else if (avgFPS > this.targetFPS * 0.9 && this.optimizationLevel > 0) {
            this.optimizationLevel = 0;
            this.particleReduction = 1.0;
            this.animationQuality = 1.0;
            console.log('Performance: Optimization disabled');
        }
    }
    
    // Get average FPS over recent frames
    getAverageFPS() {
        if (this.frameHistory.length === 0) return this.fps;
        return this.frameHistory.reduce((a, b) => a + b, 0) / this.frameHistory.length;
    }
    
    // Get performance statistics
    getStats() {
        return {
            currentFPS: Math.round(this.fps),
            averageFPS: Math.round(this.getAverageFPS()),
            optimizationLevel: this.optimizationLevel,
            particleReduction: this.particleReduction,
            animationQuality: this.animationQuality,
            frameHistory: [...this.frameHistory]
        };
    }
    
    // Manual optimization control
    setOptimizationLevel(level) {
        this.optimizationLevel = Math.max(0, Math.min(2, level));
        
        switch (this.optimizationLevel) {
            case 0:
                this.particleReduction = 1.0;
                this.animationQuality = 1.0;
                break;
            case 1:
                this.particleReduction = 0.7;
                this.animationQuality = 0.8;
                break;
            case 2:
                this.particleReduction = 0.3;
                this.animationQuality = 0.5;
                break;
        }
    }
    
    // Check if feature should be reduced for performance
    shouldReduceParticles() {
        return this.particleReduction < 1.0;
    }
    
    shouldReduceAnimations() {
        return this.animationQuality < 1.0;
    }
    
    // Get recommended particle count
    getOptimalParticleCount(baseCount) {
        return Math.floor(baseCount * this.particleReduction);
    }
    
    // Get recommended animation duration
    getOptimalAnimationDuration(baseDuration) {
        return baseDuration * this.animationQuality;
    }
}

// Loading optimization
export class LoadingOptimizer {
    constructor() {
        this.loadingSteps = [];
        this.currentStep = 0;
        this.startTime = 0;
    }
    
    // Add loading step
    addStep(name, loader) {
        this.loadingSteps.push({ name, loader, completed: false, duration: 0 });
    }
    
    // Execute all loading steps with progress tracking
    async executeSteps(progressCallback) {
        this.startTime = performance.now();
        
        for (let i = 0; i < this.loadingSteps.length; i++) {
            const step = this.loadingSteps[i];
            this.currentStep = i;
            
            if (progressCallback) {
                progressCallback(i, this.loadingSteps.length, step.name);
            }
            
            const stepStart = performance.now();
            
            try {
                await step.loader();
                step.completed = true;
            } catch (error) {
                console.error(`Loading step "${step.name}" failed:`, error);
                throw error;
            }
            
            step.duration = performance.now() - stepStart;
        }
        
        const totalTime = performance.now() - this.startTime;
        console.log(`Loading completed in ${totalTime.toFixed(2)}ms`);
        
        return this.getLoadingStats();
    }
    
    // Get loading statistics
    getLoadingStats() {
        const totalTime = this.loadingSteps.reduce((sum, step) => sum + step.duration, 0);
        
        return {
            totalSteps: this.loadingSteps.length,
            completedSteps: this.loadingSteps.filter(s => s.completed).length,
            totalTime: totalTime.toFixed(2),
            steps: this.loadingSteps.map(step => ({
                name: step.name,
                duration: step.duration.toFixed(2),
                completed: step.completed
            }))
        };
    }
}

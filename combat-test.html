<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Combat Test - Тест бою</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            background: #000;
            color: #00ff00;
        }

        .test-container {
            display: flex;
            gap: 20px;
        }

        .test-canvas {
            border: 2px solid #00ff00;
            border-radius: 8px;
        }

        .test-controls {
            background: #111;
            border: 1px solid #333;
            padding: 15px;
            border-radius: 5px;
            width: 300px;
        }

        button {
            background: #003300;
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 10px 20px;
            cursor: pointer;
            margin: 5px 0;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            width: 100%;
        }

        button:hover {
            background: #006600;
            box-shadow: 0 0 10px #00ff00;
        }

        .status {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #333;
            border-radius: 5px;
            font-size: 12px;
        }

        .combat { border-color: #ff0000; background: #330000; }
        .peace { border-color: #00ff00; background: #003300; }
    </style>
</head>
<body>
    <h1>⚔️ Combat Test - Тест системи бою</h1>
    
    <div class="test-container">
        <canvas id="testCanvas" width="600" height="400" class="test-canvas"></canvas>
        
        <div class="test-controls">
            <h3>Управління боєм</h3>
            
            <button onclick="startCombat()">🗡️ Почати бій</button>
            <button onclick="playerAttack()">⚔️ Атака гравця</button>
            <button onclick="endCombat()">🏃 Закінчити бій</button>
            
            <div id="gameStatus" class="status peace">
                Стан: Мир
            </div>
            
            <div id="playerStats" class="status">
                <strong>Гравець:</strong><br>
                HP: <span id="playerHp">100/100</span><br>
                Атака: <span id="playerAttack">10</span><br>
                Захист: <span id="playerDefense">5</span>
            </div>
            
            <div id="enemyStats" class="status" style="display: none;">
                <strong>Ворог:</strong><br>
                Ім'я: <span id="enemyName">-</span><br>
                HP: <span id="enemyHp">-/-</span><br>
                Атака: <span id="enemyAttack">-</span><br>
                Захист: <span id="enemyDefense">-</span>
            </div>
            
            <h3>Тест генерації ворогів</h3>
            <button onclick="testEnemyGeneration()">🧪 Тест генерації</button>
            <div id="enemyGenTest" class="status"></div>
        </div>
    </div>

    <script type="module">
        let gameState = null;
        let renderer = null;
        let contentGenerator = null;

        async function initTest() {
            try {
                const { GameState } = await import('./js/gameState.js');
                const { Renderer } = await import('./js/renderer.js');
                const { ContentGenerator } = await import('./js/contentGenerator.js');
                
                gameState = new GameState();
                contentGenerator = new ContentGenerator();
                
                const canvas = document.getElementById('testCanvas');
                const ctx = canvas.getContext('2d');
                renderer = new Renderer(canvas, ctx);
                
                updateUI();
                renderScene();
                
                console.log('Combat test initialized');
                
            } catch (error) {
                console.error('Failed to initialize combat test:', error);
            }
        }

        function updateUI() {
            if (!gameState) return;

            // Game status
            const gameStatus = document.getElementById('gameStatus');
            if (gameState.isInCombat) {
                gameStatus.textContent = 'Стан: Бій';
                gameStatus.className = 'status combat';
            } else {
                gameStatus.textContent = 'Стан: Мир';
                gameStatus.className = 'status peace';
            }

            // Player stats
            document.getElementById('playerHp').textContent = `${gameState.player.hp}/${gameState.player.maxHp}`;
            document.getElementById('playerAttack').textContent = gameState.player.attack;
            document.getElementById('playerDefense').textContent = gameState.player.defense;

            // Enemy stats
            const enemyStats = document.getElementById('enemyStats');
            if (gameState.currentEnemy) {
                enemyStats.style.display = 'block';
                document.getElementById('enemyName').textContent = gameState.currentEnemy.name;
                document.getElementById('enemyHp').textContent = `${gameState.currentEnemy.hp}/${gameState.currentEnemy.maxHp}`;
                document.getElementById('enemyAttack').textContent = gameState.currentEnemy.attack;
                document.getElementById('enemyDefense').textContent = gameState.currentEnemy.defense;
            } else {
                enemyStats.style.display = 'none';
            }
        }

        function renderScene() {
            if (!renderer || !gameState) return;

            try {
                renderer.clear(gameState);
                renderer.renderWorldWithoutFloor(gameState);
            } catch (error) {
                console.error('Render error:', error);
            }
        }

        window.startCombat = function() {
            if (!gameState || !contentGenerator) return;

            // Generate enemy
            const enemy = contentGenerator.generateEnemy(gameState.currentFloor);
            gameState.currentEnemy = enemy;
            gameState.isInCombat = true;

            updateUI();
            renderScene();
            
            console.log('Combat started with:', enemy.name);
        };

        window.playerAttack = function() {
            if (!gameState || !gameState.isInCombat || !gameState.currentEnemy) {
                console.log('Cannot attack - not in combat');
                return;
            }

            try {
                gameState.playerAttack();
                updateUI();
                renderScene();
                
                console.log('Player attacked');
                
                // Check if enemy is dead
                if (gameState.currentEnemy && gameState.currentEnemy.hp <= 0) {
                    console.log('Enemy defeated!');
                    setTimeout(() => {
                        gameState.isInCombat = false;
                        gameState.currentEnemy = null;
                        updateUI();
                        renderScene();
                    }, 1000);
                }
                
            } catch (error) {
                console.error('Attack error:', error);
            }
        };

        window.endCombat = function() {
            if (!gameState) return;

            gameState.isInCombat = false;
            gameState.currentEnemy = null;

            updateUI();
            renderScene();
            
            console.log('Combat ended');
        };

        window.testEnemyGeneration = function() {
            if (!contentGenerator) return;

            const testDiv = document.getElementById('enemyGenTest');
            testDiv.innerHTML = '<h4>Тест генерації ворогів:</h4>';

            for (let floor = 1; floor <= 10; floor++) {
                const enemy = contentGenerator.generateEnemy(floor);
                testDiv.innerHTML += `
                    <div>Поверх ${floor}: ${enemy.name} (HP: ${enemy.maxHp}, Атака: ${enemy.attack})</div>
                `;
            }
        };

        // Auto-start
        window.addEventListener('load', () => {
            initTest();
        });
    </script>
</body>
</html>

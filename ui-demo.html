<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Demo - Idle Roguelike Adventure</title>
    <link rel="stylesheet" href="css/icons.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Courier New', monospace;
            background: 
                radial-gradient(circle at 20% 30%, rgba(0, 255, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(255, 255, 0, 0.05) 0%, transparent 50%),
                linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0f0f0f 100%);
            color: #00ff00;
            overflow-x: hidden;
        }

        .demo-container {
            display: flex;
            height: 100vh;
        }

        .demo-canvas {
            flex: 1;
            max-width: 70%;
            background: 
                radial-gradient(circle at 20% 30%, rgba(0, 255, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(255, 255, 0, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(0, 100, 255, 0.08) 0%, transparent 50%),
                linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0f0f0f 100%);
            border: 2px solid #00ff00;
            border-radius: 8px;
            margin: 10px;
            box-shadow: 
                inset 0 0 50px rgba(0, 255, 0, 0.1),
                0 0 20px rgba(0, 255, 0, 0.2),
                0 0 40px rgba(0, 255, 0, 0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            text-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
        }

        .demo-canvas::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                repeating-linear-gradient(
                    0deg,
                    transparent,
                    transparent 2px,
                    rgba(0, 255, 0, 0.03) 2px,
                    rgba(0, 255, 0, 0.03) 4px
                );
            pointer-events: none;
            z-index: 1;
        }

        .demo-ui {
            width: 30%;
            min-width: 300px;
            background: 
                linear-gradient(180deg, #0a0a0a 0%, #151515 50%, #0a0a0a 100%);
            border-left: 2px solid #00ff00;
            border-radius: 0 8px 8px 0;
            padding: 15px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
            box-shadow: 
                inset 2px 0 10px rgba(0, 255, 0, 0.1),
                -5px 0 15px rgba(0, 0, 0, 0.5);
            position: relative;
        }

        .demo-ui::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 2px;
            height: 100%;
            background: linear-gradient(
                180deg,
                transparent 0%,
                #00ff00 20%,
                #ffff00 50%,
                #00ff00 80%,
                transparent 100%
            );
            animation: ui-glow 3s ease-in-out infinite;
        }

        @keyframes ui-glow {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .ui-panel {
            border: 1px solid #00ff00;
            border-radius: 8px;
            padding: 15px;
            background: 
                linear-gradient(135deg, #111 0%, #1a1a1a 50%, #111 100%);
            box-shadow: 
                0 4px 8px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(0, 255, 0, 0.2),
                inset 0 -1px 0 rgba(0, 255, 0, 0.1);
            position: relative;
            transition: all 0.3s ease;
        }

        .ui-panel:hover {
            border-color: #ffff00;
            box-shadow: 
                0 6px 12px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 0, 0.2),
                inset 0 -1px 0 rgba(255, 255, 0, 0.1),
                0 0 15px rgba(0, 255, 0, 0.3);
            transform: translateY(-2px);
        }

        .ui-panel h3 {
            color: #ffff00;
            margin: -5px -5px 15px -5px;
            text-align: center;
            background: 
                linear-gradient(90deg, 
                    transparent 0%,
                    rgba(255, 255, 0, 0.1) 20%,
                    rgba(255, 255, 0, 0.2) 50%,
                    rgba(255, 255, 0, 0.1) 80%,
                    transparent 100%
                );
            padding: 10px;
            border-bottom: 1px solid rgba(255, 255, 0, 0.3);
            font-size: 16px;
            font-weight: bold;
            text-shadow: 
                0 0 5px rgba(255, 255, 0, 0.5),
                0 0 10px rgba(255, 255, 0, 0.3);
            letter-spacing: 1px;
            position: relative;
        }

        .ui-panel h3::before,
        .ui-panel h3::after {
            content: '◆';
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            color: #00ff00;
            font-size: 12px;
            animation: panel-glow 2s ease-in-out infinite;
        }

        .ui-panel h3::before { left: 10px; }
        .ui-panel h3::after { right: 10px; }

        @keyframes panel-glow {
            0%, 100% { opacity: 0.5; text-shadow: 0 0 5px #00ff00; }
            50% { opacity: 1; text-shadow: 0 0 10px #00ff00, 0 0 15px #00ff00; }
        }

        .progress-bar {
            width: 100%;
            height: 24px;
            background: 
                linear-gradient(90deg, #1a1a1a 0%, #333 50%, #1a1a1a 100%);
            border: 1px solid #00ff00;
            border-radius: 12px;
            margin: 8px 0;
            position: relative;
            overflow: hidden;
            box-shadow: 
                inset 0 2px 4px rgba(0, 0, 0, 0.5),
                0 1px 2px rgba(0, 255, 0, 0.2);
        }

        .progress-fill {
            height: 100%;
            background: 
                linear-gradient(90deg, 
                    #00ff00 0%, 
                    #66ff00 25%, 
                    #ffff00 50%, 
                    #ff6600 75%, 
                    #ff0000 100%
                );
            border-radius: 11px;
            transition: width 1s ease;
            position: relative;
            box-shadow: 
                0 0 10px rgba(0, 255, 0, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .button {
            background: 
                linear-gradient(135deg, #003300 0%, #004400 50%, #003300 100%);
            border: 1px solid #00ff00;
            border-radius: 6px;
            color: #00ff00;
            padding: 10px 18px;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 3px;
            text-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
            box-shadow: 
                0 2px 4px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(0, 255, 0, 0.2);
            position: relative;
            overflow: hidden;
        }

        .button:hover {
            background: 
                linear-gradient(135deg, #00ff00 0%, #66ff00 50%, #00ff00 100%);
            color: #000;
            border-color: #ffff00;
            box-shadow: 
                0 4px 8px rgba(0, 255, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3),
                0 0 15px rgba(0, 255, 0, 0.6);
            transform: translateY(-2px);
            text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
        }

        .inventory-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 6px;
            margin-top: 10px;
            padding: 10px;
            background: 
                linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.6) 100%);
            border-radius: 8px;
            border: 1px solid rgba(0, 255, 0, 0.3);
        }

        .inventory-slot {
            width: 40px;
            height: 40px;
            border: 2px solid #333;
            border-radius: 6px;
            background: 
                linear-gradient(135deg, #111 0%, #1a1a1a 50%, #111 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            box-shadow: 
                inset 0 1px 0 rgba(255, 255, 255, 0.1),
                0 1px 2px rgba(0, 0, 0, 0.3);
            font-size: 20px;
        }

        .inventory-slot:hover {
            border-color: #00ff00;
            background: 
                linear-gradient(135deg, #222 0%, #2a2a2a 50%, #222 100%);
            box-shadow: 
                inset 0 1px 0 rgba(0, 255, 0, 0.2),
                0 2px 4px rgba(0, 0, 0, 0.4),
                0 0 10px rgba(0, 255, 0, 0.3);
            transform: translateY(-2px);
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-canvas">
            🎮 Game Canvas Area
            <br>
            <small>Покращений фон з градієнтами та ефектами</small>
        </div>
        
        <div class="demo-ui">
            <div class="ui-panel fade-in">
                <h3>Персонаж</h3>
                <div>Рівень: 15</div>
                <div>Здоров'я: 
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 75%;"></div>
                    </div>
                </div>
                <div>Досвід:
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 45%;"></div>
                    </div>
                </div>
            </div>
            
            <div class="ui-panel fade-in">
                <h3>Дії</h3>
                <button class="button">⚔️ Атакувати</button>
                <button class="button">🛡️ Захищатися</button>
                <button class="button">🏃 Втекти</button>
                <button class="button">🔍 Дослідити</button>
            </div>
            
            <div class="ui-panel fade-in">
                <h3>Інвентар</h3>
                <div class="inventory-grid">
                    <div class="inventory-slot">⚔️</div>
                    <div class="inventory-slot">🛡️</div>
                    <div class="inventory-slot">💍</div>
                    <div class="inventory-slot">🧪</div>
                    <div class="inventory-slot">📜</div>
                    <div class="inventory-slot">🧿</div>
                    <div class="inventory-slot"></div>
                    <div class="inventory-slot"></div>
                    <div class="inventory-slot"></div>
                    <div class="inventory-slot"></div>
                    <div class="inventory-slot"></div>
                    <div class="inventory-slot"></div>
                </div>
            </div>
            
            <div class="ui-panel fade-in">
                <h3>Статистика</h3>
                <div>Атака: 45 (+15)</div>
                <div>Захист: 32 (+8)</div>
                <div>Швидкість: 28</div>
                <div>Удача: 12</div>
            </div>
        </div>
    </div>

    <script>
        // Анімація progress bars
        function animateProgressBars() {
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const currentWidth = parseInt(bar.style.width);
                const newWidth = Math.random() * 100;
                bar.style.width = newWidth + '%';
            });
        }

        // Анімація кожні 3 секунди
        setInterval(animateProgressBars, 3000);

        // Додаємо інтерактивність до слотів інвентаря
        document.querySelectorAll('.inventory-slot').forEach(slot => {
            slot.addEventListener('click', () => {
                if (slot.textContent) {
                    slot.style.animation = 'none';
                    slot.offsetHeight; // Trigger reflow
                    slot.style.animation = 'fadeIn 0.3s ease';
                }
            });
        });

        console.log('UI Demo loaded successfully!');
    </script>
</body>
</html>

// Game state management
export class GameState {
    constructor() {
        // Player data
        this.player = {
            name: 'Герой',
            level: 1,
            hp: 100,
            maxHp: 100,
            attack: 10,
            defense: 5,
            experience: 0,
            experienceToNext: 100,
            gold: 0,
            inventory: [],
            equipment: {
                weapon: null,
                armor: null,
                accessory: null
            }
        };
        
        // Current location
        this.currentFloor = 1;
        this.currentRoom = 1;
        this.maxRoomsPerFloor = 10;
        this.roomCombatCompleted = false; // Track if current room combat is completed
        this.roomCombatRequired = true; // Whether combat is required to progress
        this.currentLocation = {
            name: 'Початкова печера',
            type: 'dungeon',
            description: 'Темна печера з вологими стінами'
        };
        
        // Combat state
        this.currentEnemy = null;
        this.isInCombat = false;
        this.combatLog = [];
        
        // Game effects
        this.effects = [];
        
        // Game settings - autoFight enabled by default
        this.autoFight = true;
        this.autoLoot = true;
        this.autoProgress = false;
        
        // Game statistics
        this.stats = {
            totalPlayTime: 0,
            enemiesKilled: 0,
            floorsCleared: 0,
            goldEarned: 0,
            itemsFound: 0,
            deathCount: 0
        };
        
        // Flags
        this.needsUIUpdate = true;
        this.lastSaveTime = Date.now();
    }
    
    // Player progression
    gainExperience(amount) {
        this.player.experience += amount;
        this.addEffect({
            type: 'experience',
            amount: amount,
            x: 200,
            y: 300,
            duration: 1000,
            age: 0
        });
        
        // Check for level up
        while (this.player.experience >= this.player.experienceToNext) {
            this.levelUp();
        }
        
        this.needsUIUpdate = true;
    }
    
    levelUp() {
        this.player.experience -= this.player.experienceToNext;
        this.player.level++;

        // Increase stats
        const hpIncrease = 20 + Math.floor(this.player.level * 1.5);
        const attackIncrease = 2 + Math.floor(this.player.level * 0.5);
        const defenseIncrease = 1 + Math.floor(this.player.level * 0.3);

        this.player.maxHp += hpIncrease;
        this.player.hp = this.player.maxHp; // Full heal on level up
        this.player.attack += attackIncrease;
        this.player.defense += defenseIncrease;

        // Calculate next level requirement
        this.player.experienceToNext = Math.floor(100 * Math.pow(1.2, this.player.level - 1));

        // Add level up effect
        this.addEffect({
            type: 'levelup',
            duration: 3000,
            age: 0
        });

        // Create particle effects
        if (window.game && window.game.renderer) {
            window.game.renderer.createLevelUpParticles(200, 300);
        }

        this.addLogEntry(`Рівень підвищено! Тепер ${this.player.level} рівень!`, 'level');
        this.needsUIUpdate = true;
    }
    
    // Combat system
    startCombat(enemy) {
        this.currentEnemy = enemy;
        this.isInCombat = true;
        this.currentEnemy.isInCombat = true;
        this.addLogEntry(`Зустрів ${enemy.name}!`, 'combat');
        this.needsUIUpdate = true;
    }
    
    endCombat(victory = true) {
        if (!this.currentEnemy) return;

        if (victory) {
            // Gain experience and gold
            const expGain = this.currentEnemy.expReward;
            const goldGain = this.currentEnemy.goldReward;

            this.gainExperience(expGain);
            this.gainGold(goldGain);

            // Drop loot
            if (this.currentEnemy.loot && Math.random() < this.currentEnemy.dropChance) {
                this.addItemToInventory(this.currentEnemy.loot);
                this.addLogEntry(`Знайшов ${this.currentEnemy.loot.name}!`, 'loot');
            }

            this.addLogEntry(`Переміг ${this.currentEnemy.name}!`, 'combat');
            this.stats.enemiesKilled++;

            // Mark room combat as completed
            this.roomCombatCompleted = true;
        } else {
            this.addLogEntry('Поразка...', 'combat');
            this.stats.deathCount++;
            this.respawn();
        }

        this.currentEnemy = null;
        this.isInCombat = false;
        this.needsUIUpdate = true;
    }
    
    playerAttack() {
        if (!this.currentEnemy) return;

        // Check for critical hit
        const isCritical = Math.random() < 0.1; // 10% crit chance
        let damage = Math.max(1, this.player.attack - this.currentEnemy.defense + Math.floor(Math.random() * 5));

        if (isCritical) {
            damage = Math.floor(damage * 2);
            // Play critical hit sound
            if (window.game && window.game.audio) {
                window.game.audio.playCriticalHitSound();
            }
        } else {
            // Play normal attack sound
            if (window.game && window.game.audio) {
                window.game.audio.playAttackSound();
            }
        }

        this.currentEnemy.hp -= damage;

        this.addEffect({
            type: 'damage',
            damage: damage,
            x: 500,
            y: 250,
            duration: 1000,
            age: 0,
            critical: isCritical
        });

        // Create particle effects
        if (window.game && window.game.renderer) {
            window.game.renderer.createDamageParticles(500, 250, damage, 'damage');
            window.game.renderer.createCombatParticles(500, 250, isCritical ? 'critical' : 'hit');
        }

        this.addLogEntry(`Завдав ${damage} шкоди ${this.currentEnemy.name}${isCritical ? ' (КРИТИЧНИЙ!)' : ''}`, 'combat');

        if (this.currentEnemy.hp <= 0) {
            this.endCombat(true);
        }
    }
    
    enemyAttack() {
        if (!this.currentEnemy) return;
        
        const damage = Math.max(1, this.currentEnemy.attack - this.player.defense + Math.floor(Math.random() * 3));
        this.player.hp -= damage;
        
        this.addEffect({
            type: 'damage',
            damage: damage,
            x: 200,
            y: 250,
            duration: 1000,
            age: 0
        });
        
        this.addLogEntry(`${this.currentEnemy.name} завдав ${damage} шкоди`, 'combat');
        
        if (this.player.hp <= 0) {
            this.endCombat(false);
        }
    }
    
    // Healing
    healPlayer(amount) {
        const actualHeal = Math.min(amount, this.player.maxHp - this.player.hp);
        this.player.hp += actualHeal;
        
        if (actualHeal > 0) {
            this.addEffect({
                type: 'heal',
                amount: actualHeal,
                x: 200,
                y: 280,
                duration: 1000,
                age: 0
            });
            
            this.addLogEntry(`Відновив ${actualHeal} здоров'я`, 'heal');
        }
        
        this.needsUIUpdate = true;
    }
    
    // Gold management
    gainGold(amount) {
        this.player.gold += amount;
        this.stats.goldEarned += amount;

        // Create gold particle effects
        if (window.game && window.game.renderer) {
            window.game.renderer.createGoldParticles(300, 400, amount);
        }

        this.addLogEntry(`Отримав ${amount} золота`, 'loot');
        this.needsUIUpdate = true;
    }
    
    spendGold(amount) {
        if (this.player.gold >= amount) {
            this.player.gold -= amount;
            this.needsUIUpdate = true;
            return true;
        }
        return false;
    }
    
    // Inventory management
    addItemToInventory(item) {
        if (this.player.inventory.length < 20) { // Max 20 items
            this.player.inventory.push(item);
            this.stats.itemsFound++;

            // Play item drop sound based on rarity
            if (window.game && window.game.audio) {
                window.game.audio.playItemDropSound(item.rarity);
            }

            // Create item drop particles
            if (window.game && window.game.renderer) {
                window.game.renderer.createItemDropParticles(400, 350, item.rarity);
            }

            this.needsUIUpdate = true;
            return true;
        }
        return false;
    }
    
    removeItemFromInventory(index) {
        if (index >= 0 && index < this.player.inventory.length) {
            return this.player.inventory.splice(index, 1)[0];
        }
        return null;
    }
    
    equipItem(item, slot) {
        if (this.player.equipment[slot]) {
            // Unequip current item
            this.addItemToInventory(this.player.equipment[slot]);
        }
        
        this.player.equipment[slot] = item;
        this.applyItemStats(item, true);
        this.needsUIUpdate = true;
    }
    
    unequipItem(slot) {
        const item = this.player.equipment[slot];
        if (item) {
            this.player.equipment[slot] = null;
            this.applyItemStats(item, false);
            this.addItemToInventory(item);
            this.needsUIUpdate = true;
        }
    }
    
    applyItemStats(item, equip = true) {
        const multiplier = equip ? 1 : -1;
        
        if (item.stats) {
            if (item.stats.attack) this.player.attack += item.stats.attack * multiplier;
            if (item.stats.defense) this.player.defense += item.stats.defense * multiplier;
            if (item.stats.maxHp) {
                this.player.maxHp += item.stats.maxHp * multiplier;
                if (!equip && this.player.hp > this.player.maxHp) {
                    this.player.hp = this.player.maxHp;
                }
            }
        }
    }
    
    // Location management
    nextRoom() {
        if (this.isInCombat) return false;

        // Check if combat is required and completed
        if (this.roomCombatRequired && !this.roomCombatCompleted) {
            this.addLogEntry('Потрібно перемогти ворога перед переходом!', 'warning');
            return false;
        }

        this.currentRoom++;
        if (this.currentRoom > this.maxRoomsPerFloor) {
            this.nextFloor();
        } else {
            this.addLogEntry(`Перейшов до кімнати ${this.currentRoom}`, 'explore');
            // Reset combat completion for new room
            this.roomCombatCompleted = false;
        }
        this.needsUIUpdate = true;
        return true;
    }
    
    nextFloor() {
        this.currentFloor++;
        this.currentRoom = 1;
        this.stats.floorsCleared++;

        // Reset combat completion for new floor
        this.roomCombatCompleted = false;

        // Update location
        this.updateLocation();

        this.addLogEntry(`Піднявся на ${this.currentFloor} поверх!`, 'level');
        this.needsUIUpdate = true;
    }
    
    updateLocation() {
        const floorTypes = [
            { name: 'Печера', desc: 'Темна печера з вологими стінами' },
            { name: 'Катакомби', desc: 'Древні підземні ходи' },
            { name: 'Підземелля', desc: 'Забуте підземелля замку' },
            { name: 'Шахта', desc: 'Покинута шахта' },
            { name: 'Склеп', desc: 'Моторошний склеп' }
        ];
        
        const typeIndex = Math.floor(this.currentFloor / 5) % floorTypes.length;
        const type = floorTypes[typeIndex];
        
        this.currentLocation = {
            name: `${type.name} (Поверх ${this.currentFloor})`,
            type: 'dungeon',
            description: type.desc
        };
    }
    
    // Respawn system
    respawn() {
        // Restore health but preserve all other progression
        this.player.hp = Math.floor(this.player.maxHp * 0.5);

        // Stay in current room but reset combat completion
        this.roomCombatCompleted = false;

        // Note: We preserve currentFloor, currentRoom, level, experience, skills, equipment, gold, prestige bonuses
        this.addLogEntry('Відродився в поточній кімнаті...', 'system');
        this.needsUIUpdate = true;
    }

    // Check if player can progress to next room
    canProgressToNextRoom() {
        if (this.isInCombat) return false;
        if (this.roomCombatRequired && !this.roomCombatCompleted) return false;
        return true;
    }

    // Check if player can progress to next floor
    canProgressToNextFloor() {
        if (this.isInCombat) return false;
        if (this.currentRoom < this.maxRoomsPerFloor) return false;
        if (this.roomCombatRequired && !this.roomCombatCompleted) return false;
        return true;
    }

    // Inventory management methods for shop system
    hasInventorySpace() {
        return this.player.inventory.length < this.player.maxInventorySize;
    }

    removeItemFromInventory(item) {
        const index = this.player.inventory.findIndex(invItem =>
            invItem.id === item.id ||
            (invItem.name === item.name && invItem.type === item.type)
        );

        if (index !== -1) {
            this.player.inventory.splice(index, 1);
            this.needsUIUpdate = true;
            return true;
        }
        return false;
    }
    
    // Effects system
    addEffect(effect) {
        this.effects.push(effect);
    }
    
    updateEffects(deltaTime) {
        this.effects = this.effects.filter(effect => {
            effect.age += deltaTime;
            return effect.age < effect.duration;
        });
    }
    
    // Logging system
    addLogEntry(message, type = 'info') {
        const entry = {
            message,
            type,
            timestamp: Date.now()
        };
        
        this.combatLog.push(entry);
        
        // Keep only last 50 entries
        if (this.combatLog.length > 50) {
            this.combatLog.shift();
        }
        
        this.needsUIUpdate = true;
    }
    
    // Serialization for save/load
    serialize() {
        return {
            player: this.player,
            currentFloor: this.currentFloor,
            currentRoom: this.currentRoom,
            currentLocation: this.currentLocation,
            roomCombatCompleted: this.roomCombatCompleted,
            roomCombatRequired: this.roomCombatRequired,
            autoFight: this.autoFight,
            autoLoot: this.autoLoot,
            autoProgress: this.autoProgress,
            stats: this.stats,
            lastSaveTime: Date.now()
        };
    }
    
    deserialize(data) {
        if (data.player) this.player = { ...this.player, ...data.player };
        if (data.currentFloor) this.currentFloor = data.currentFloor;
        if (data.currentRoom) this.currentRoom = data.currentRoom;
        if (data.currentLocation) this.currentLocation = data.currentLocation;
        if (data.roomCombatCompleted !== undefined) this.roomCombatCompleted = data.roomCombatCompleted;
        if (data.roomCombatRequired !== undefined) this.roomCombatRequired = data.roomCombatRequired;
        if (data.autoFight !== undefined) this.autoFight = data.autoFight;
        if (data.autoLoot !== undefined) this.autoLoot = data.autoLoot;
        if (data.autoProgress !== undefined) this.autoProgress = data.autoProgress;
        if (data.stats) this.stats = { ...this.stats, ...data.stats };
        if (data.lastSaveTime) this.lastSaveTime = data.lastSaveTime;

        this.needsUIUpdate = true;
    }
}

#!/bin/bash

echo "Starting Idle Roguelike Adventure..."
echo ""
echo "Opening browser at http://localhost:8000"
echo "Press Ctrl+C to stop the server"
echo ""

# Try to open browser (works on most systems)
if command -v xdg-open > /dev/null; then
    xdg-open http://localhost:8000
elif command -v open > /dev/null; then
    open http://localhost:8000
elif command -v start > /dev/null; then
    start http://localhost:8000
fi

# Start Python HTTP server
python3 -m http.server 8000 || python -m http.server 8000

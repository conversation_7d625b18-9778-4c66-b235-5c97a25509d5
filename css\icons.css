/* Item Icon System Styles */

/* Base icon container */
.item-icon-container {
    position: relative;
    display: inline-block;
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.8);
    cursor: pointer;
    transition: all 0.2s ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.8));
    user-select: none;
}

.item-icon-container:hover {
    transform: scale(1.1);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.9));
}

/* Enhancement indicators */
.item-icon-container .enhancement-indicator {
    position: absolute;
    top: -4px;
    right: -4px;
    background: #ffff00;
    color: #000;
    font-size: 10px;
    font-weight: bold;
    padding: 1px 3px;
    border-radius: 2px;
    line-height: 1;
    min-width: 16px;
    text-align: center;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Rarity glow effects */
.item-icon-container.rarity-common {
    border-color: #ffffff;
}

.item-icon-container.rarity-uncommon {
    border-color: #00ff00;
    box-shadow: 0 0 8px rgba(0, 255, 0, 0.3);
}

.item-icon-container.rarity-rare {
    border-color: #0066ff;
    box-shadow: 0 0 8px rgba(0, 102, 255, 0.3);
}

.item-icon-container.rarity-epic {
    border-color: #9966ff;
    box-shadow: 0 0 8px rgba(153, 102, 255, 0.3);
}

.item-icon-container.rarity-legendary {
    border-color: #ff6600;
    box-shadow: 0 0 8px rgba(255, 102, 0, 0.3);
    animation: legendary-pulse 2s ease-in-out infinite;
}

@keyframes legendary-pulse {
    0%, 100% { box-shadow: 0 0 8px rgba(255, 102, 0, 0.3); }
    50% { box-shadow: 0 0 16px rgba(255, 102, 0, 0.6); }
}

/* Inventory grid system */
.inventory-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 8px;
    padding: 10px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px;
}

@media (max-width: 768px) {
    .inventory-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: 6px;
    }
}

@media (max-width: 480px) {
    .inventory-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 4px;
    }
}

/* Inventory slots */
.inventory-slot {
    width: 50px;
    height: 50px;
    border: 2px dashed #666;
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    position: relative;
}

.inventory-slot.has-item {
    border-style: solid;
    background: rgba(0, 0, 0, 0.8);
}

.inventory-slot:hover {
    border-color: #00ff00;
    transform: scale(1.05);
}

/* Equipment slots */
.equipment-slot {
    width: 60px;
    height: 60px;
    border: 2px dashed #666;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.equipment-slot.equipped {
    border-style: solid;
    background: rgba(0, 0, 0, 0.8);
}

.equipment-slot:hover {
    border-color: #00ff00;
    transform: scale(1.05);
}

/* Equipment grid layout */
.equipment-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    padding: 20px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    max-width: 300px;
    margin: 0 auto;
}

/* Shop item cards */
.shop-item-card {
    background: #222;
    border: 2px solid #444;
    border-radius: 8px;
    padding: 15px;
    margin: 10px;
    width: 220px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.shop-item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Drag and drop states */
.inventory-item.dragging {
    opacity: 0.5;
    cursor: grabbing;
    transform: rotate(5deg);
}

.equipment-slot.drag-over {
    border-color: #ffff00;
    background: rgba(255, 255, 0, 0.1);
    transform: scale(1.1);
}

/* Touch targets for mobile */
@media (max-width: 768px) {
    .inventory-slot,
    .equipment-slot,
    .item-icon-container {
        min-width: 44px;
        min-height: 44px;
    }
}

/* Accessibility improvements */
.item-icon-container:focus,
.inventory-slot:focus,
.equipment-slot:focus {
    outline: 2px solid #00ff00;
    outline-offset: 2px;
}

/* Loading states */
.item-icon-container.loading {
    opacity: 0.5;
    animation: loading-pulse 1s ease-in-out infinite;
}

@keyframes loading-pulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 0.8; }
}

/* Error states */
.item-icon-container.error {
    border-color: #ff0000;
    background: rgba(255, 0, 0, 0.1);
}

/* Tooltip positioning helpers */
.tooltip-container {
    position: fixed;
    background: rgba(0, 0, 0, 0.95);
    border: 1px solid #00ff00;
    color: #00ff00;
    padding: 10px;
    border-radius: 5px;
    font-size: 12px;
    line-height: 1.4;
    z-index: 1000;
    max-width: 300px;
    pointer-events: none;
    white-space: pre-line;
}

/* Performance optimizations */
.item-icon-container,
.inventory-slot,
.equipment-slot {
    will-change: transform;
    backface-visibility: hidden;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .item-icon-container,
    .inventory-slot,
    .equipment-slot,
    .shop-item-card {
        transition: none;
        animation: none;
    }
    
    .item-icon-container:hover,
    .inventory-slot:hover,
    .equipment-slot:hover {
        transform: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .item-icon-container,
    .inventory-slot,
    .equipment-slot {
        border-width: 3px;
    }
    
    .item-icon-container.rarity-legendary {
        animation: none;
        border-color: #ffffff;
    }
}

/* Print styles */
@media print {
    .item-icon-container,
    .inventory-grid,
    .equipment-grid {
        display: none;
    }
}

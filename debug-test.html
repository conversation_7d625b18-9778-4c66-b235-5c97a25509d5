<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test - Idle Roguelike Adventure</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .test-result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 3px;
        }
        .success { background: #003300; color: #00ff00; }
        .error { background: #330000; color: #ff0000; }
        .warning { background: #333300; color: #ffff00; }
        .info { background: #000033; color: #0066ff; }
        button {
            background: #003300;
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 10px 20px;
            cursor: pointer;
            margin: 5px;
            border-radius: 3px;
        }
        button:hover { background: #006600; }
    </style>
</head>
<body>
    <h1>🔍 Debug Test - Перевірка завантаження модулів</h1>
    
    <button onclick="testModuleLoading()">Тестувати завантаження модулів</button>
    <button onclick="testMainGame()">Тестувати основну гру</button>
    <button onclick="testIconSystem()">Тестувати систему іконок</button>
    
    <div id="results"></div>

    <script type="module">
        function addResult(message, type = 'info') {
            const container = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }

        window.testModuleLoading = async function() {
            const container = document.getElementById('results');
            container.innerHTML = '';
            
            addResult('🔍 Початок тестування завантаження модулів...', 'info');
            
            const modules = [
                { name: 'GameState', path: './js/gameState.js' },
                { name: 'UIManager', path: './js/ui.js' },
                { name: 'ItemIconSystem', path: './js/itemIconSystem.js' },
                { name: 'ConsumableSystem', path: './js/consumableSystem.js' },
                { name: 'BiomeBackgroundSystem', path: './js/biomeBackgroundSystem.js' },
                { name: 'ContentGenerator', path: './js/contentGenerator.js' },
                { name: 'EquipmentSystem', path: './js/equipmentSystem.js' },
                { name: 'ShopSystem', path: './js/shopSystem.js' }
            ];
            
            for (const module of modules) {
                try {
                    const imported = await import(module.path);
                    if (imported[module.name]) {
                        addResult(`✅ ${module.name} завантажено успішно`, 'success');
                    } else {
                        addResult(`❌ ${module.name} не експортує клас`, 'error');
                    }
                } catch (error) {
                    addResult(`❌ Помилка завантаження ${module.name}: ${error.message}`, 'error');
                    console.error(`Error loading ${module.name}:`, error);
                }
            }
            
            addResult('🎉 Тестування завантаження модулів завершено', 'info');
        };

        window.testMainGame = async function() {
            addResult('🎮 Тестування основної гри...', 'info');
            
            try {
                // Перевіряємо, чи існує основна гра
                if (window.game) {
                    addResult('✅ Основна гра знайдена в window.game', 'success');
                    
                    if (window.game.iconSystem) {
                        addResult('✅ IconSystem ініціалізовано в основній грі', 'success');
                    } else {
                        addResult('❌ IconSystem НЕ ініціалізовано в основній грі', 'error');
                    }
                    
                    if (window.game.ui && window.game.ui.iconSystem) {
                        addResult('✅ IconSystem ініціалізовано в UI', 'success');
                    } else {
                        addResult('❌ IconSystem НЕ ініціалізовано в UI', 'error');
                    }
                    
                } else {
                    addResult('❌ Основна гра НЕ знайдена в window.game', 'error');
                    addResult('💡 Спробуйте відкрити index.html спочатку', 'warning');
                }
                
            } catch (error) {
                addResult(`❌ Помилка тестування основної гри: ${error.message}`, 'error');
            }
        };

        window.testIconSystem = async function() {
            addResult('🎨 Тестування системи іконок...', 'info');
            
            try {
                const { ItemIconSystem } = await import('./js/itemIconSystem.js');
                const iconSystem = new ItemIconSystem();
                
                addResult('✅ ItemIconSystem створено успішно', 'success');
                
                // Тестуємо іконки
                const testItem = {
                    name: 'Тестовий меч',
                    type: 'sword',
                    rarity: 'epic',
                    level: 5,
                    enhancement: 2,
                    stats: { attack: 25 }
                };
                
                const iconElement = iconSystem.createItemIconElement(testItem, 48);
                if (iconElement) {
                    addResult('✅ Іконка предмета створена успішно', 'success');
                    
                    // Додаємо іконку на сторінку для візуальної перевірки
                    const testContainer = document.createElement('div');
                    testContainer.style.cssText = 'margin: 10px 0; padding: 10px; border: 1px solid #00ff00;';
                    testContainer.appendChild(document.createTextNode('Тестова іконка: '));
                    testContainer.appendChild(iconElement);
                    document.getElementById('results').appendChild(testContainer);
                    
                } else {
                    addResult('❌ Не вдалося створити іконку предмета', 'error');
                }
                
                // Тестуємо кольори рідкості
                const rarities = ['common', 'uncommon', 'rare', 'epic', 'legendary'];
                rarities.forEach(rarity => {
                    const color = iconSystem.getRarityColor(rarity);
                    if (color) {
                        addResult(`✅ Колір для ${rarity}: ${color}`, 'success');
                    } else {
                        addResult(`❌ Не знайдено колір для ${rarity}`, 'error');
                    }
                });
                
            } catch (error) {
                addResult(`❌ Помилка тестування IconSystem: ${error.message}`, 'error');
                console.error('IconSystem test error:', error);
            }
        };

        // Автоматично запускаємо тест при завантаженні
        window.addEventListener('load', () => {
            addResult('🚀 Debug Test завантажено. Натисніть кнопки для тестування.', 'info');
        });
    </script>
</body>
</html>

// UI Manager for game interface
export class UIManager {
    constructor(gameState) {
        this.gameState = gameState;
        this.skillSystem = null;
        this.bossSystem = null;
        this.prestigeSystem = null;
        this.settingsManager = null;
        this.elements = {};
        this.updateInterval = 100; // Update UI every 100ms
        this.lastUpdate = 0;
        
        // Animation states
        this.animations = {
            healthBar: { target: 1, current: 1 },
            xpBar: { target: 0, current: 0 },
            enemyHealthBar: { target: 1, current: 1 }
        };
    }
    
    init() {
        this.cacheElements();
        this.setupEventListeners();
        this.generateInventorySlots();
        this.updateUI();

        // Start update loop
        setInterval(() => this.updateAnimations(), 16); // 60fps for smooth animations
    }

    // Set references to game systems
    setSystems(skillSystem, bossSystem, prestigeSystem, settingsManager) {
        this.skillSystem = skillSystem;
        this.bossSystem = bossSystem;
        this.prestigeSystem = prestigeSystem;
        this.settingsManager = settingsManager;
    }
    
    cacheElements() {
        // Player stats
        this.elements.playerLevel = document.getElementById('playerLevel');
        this.elements.xpBar = document.getElementById('xpBar');
        this.elements.xpText = document.getElementById('xpText');
        this.elements.hpBar = document.getElementById('hpBar');
        this.elements.hpText = document.getElementById('hpText');
        this.elements.playerAttack = document.getElementById('playerAttack');
        this.elements.playerDefense = document.getElementById('playerDefense');
        this.elements.playerGold = document.getElementById('playerGold');
        
        // Location
        this.elements.locationName = document.getElementById('locationName');
        this.elements.currentFloor = document.getElementById('currentFloor');
        this.elements.currentRoom = document.getElementById('currentRoom');
        this.elements.nextRoomBtn = document.getElementById('nextRoomBtn');
        this.elements.nextFloorBtn = document.getElementById('nextFloorBtn');
        
        // Combat
        this.elements.enemyInfo = document.getElementById('enemyInfo');
        this.elements.noCombat = document.getElementById('noCombat');
        this.elements.enemyName = document.getElementById('enemyName');
        this.elements.enemyHpBar = document.getElementById('enemyHpBar');
        this.elements.enemyHpText = document.getElementById('enemyHpText');
        this.elements.autoFightBtn = document.getElementById('autoFightBtn');
        
        // Inventory
        this.elements.inventory = document.getElementById('inventory');
        
        // Log
        this.elements.gameLog = document.getElementById('gameLog');

        // Skills
        this.elements.combatSkill = document.getElementById('combatSkill');
        this.elements.explorationSkill = document.getElementById('explorationSkill');
        this.elements.survivalSkill = document.getElementById('survivalSkill');
        this.elements.luckSkill = document.getElementById('luckSkill');
        this.elements.skillPoints = document.getElementById('skillPoints');

        // Achievements
        this.elements.achievementCount = document.getElementById('achievementCount');
        this.elements.showAchievementsBtn = document.getElementById('showAchievementsBtn');

        // Prestige
        this.elements.prestigeLevel = document.getElementById('prestigeLevel');
        this.elements.prestigePoints = document.getElementById('prestigePoints');
        this.elements.prestigeBtn = document.getElementById('prestigeBtn');
        this.elements.prestigeUpgradesBtn = document.getElementById('prestigeUpgradesBtn');

        // Settings
        this.elements.settingsBtn = document.getElementById('settingsBtn');
    }
    
    setupEventListeners() {
        // Inventory item clicks
        this.elements.inventory.addEventListener('click', (e) => {
            const slot = e.target.closest('.inventory-slot');
            if (slot) {
                const index = parseInt(slot.dataset.index);
                this.handleInventoryClick(index);
            }
        });
        
        // Button hover effects
        document.querySelectorAll('.button').forEach(button => {
            button.addEventListener('mouseenter', () => {
                this.playHoverSound();
            });
        });
        
        // Achievements button
        this.elements.showAchievementsBtn.addEventListener('click', () => {
            this.showAchievementsModal();
        });

        // Prestige buttons
        this.elements.prestigeBtn.addEventListener('click', () => {
            this.performPrestige();
        });

        this.elements.prestigeUpgradesBtn.addEventListener('click', () => {
            this.showPrestigeUpgradesModal();
        });

        // Settings button
        this.elements.settingsBtn.addEventListener('click', () => {
            this.showSettingsModal();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyPress(e);
        });
    }
    
    generateInventorySlots() {
        const inventory = this.elements.inventory;
        inventory.innerHTML = '';
        
        for (let i = 0; i < 20; i++) {
            const slot = document.createElement('div');
            slot.className = 'inventory-slot';
            slot.dataset.index = i;
            slot.title = `Слот ${i + 1}`;
            inventory.appendChild(slot);
        }
    }
    
    updateUI() {
        const now = Date.now();
        if (now - this.lastUpdate < this.updateInterval) return;
        this.lastUpdate = now;
        
        this.updatePlayerStats();
        this.updateLocation();
        this.updateCombat();
        this.updateInventory();
        this.updateSkills();
        this.updateAchievements();
        this.updatePrestige();
        this.updateGameLog();
        this.updateButtons();
    }
    
    updatePlayerStats() {
        const player = this.gameState.player;
        
        // Basic stats
        this.elements.playerLevel.textContent = player.level;
        this.elements.playerAttack.textContent = player.attack;
        this.elements.playerDefense.textContent = player.defense;
        this.elements.playerGold.textContent = player.gold.toLocaleString();
        
        // Health bar
        const healthPercent = player.hp / player.maxHp;
        this.animations.healthBar.target = healthPercent;
        this.elements.hpText.textContent = `${player.hp} / ${player.maxHp}`;
        
        // Experience bar
        const xpPercent = player.experience / player.experienceToNext;
        this.animations.xpBar.target = xpPercent;
        this.elements.xpText.textContent = `${player.experience} / ${player.experienceToNext}`;
    }
    
    updateLocation() {
        this.elements.locationName.textContent = this.gameState.currentLocation.name;
        this.elements.currentFloor.textContent = this.gameState.currentFloor;
        this.elements.currentRoom.textContent = `${this.gameState.currentRoom}/${this.gameState.maxRoomsPerFloor}`;
    }
    
    updateCombat() {
        const enemy = this.gameState.currentEnemy;
        
        if (enemy && this.gameState.isInCombat) {
            this.elements.enemyInfo.style.display = 'block';
            this.elements.noCombat.style.display = 'none';
            
            this.elements.enemyName.textContent = enemy.name;
            
            const enemyHealthPercent = enemy.hp / enemy.maxHp;
            this.animations.enemyHealthBar.target = enemyHealthPercent;
            this.elements.enemyHpText.textContent = `${enemy.hp} / ${enemy.maxHp}`;
        } else {
            this.elements.enemyInfo.style.display = 'none';
            this.elements.noCombat.style.display = 'block';
        }
        
        // Auto fight button
        this.elements.autoFightBtn.textContent = `Авто-бій: ${this.gameState.autoFight ? 'УВІМК' : 'ВИМК'}`;
        this.elements.autoFightBtn.className = `button ${this.gameState.autoFight ? 'active' : ''}`;
    }
    
    updateInventory() {
        const slots = this.elements.inventory.querySelectorAll('.inventory-slot');
        const inventory = this.gameState.player.inventory;
        
        slots.forEach((slot, index) => {
            slot.innerHTML = '';
            
            if (index < inventory.length) {
                const item = inventory[index];
                const icon = this.createItemIcon(item);
                slot.appendChild(icon);
                slot.title = this.createItemTooltip(item);
                slot.classList.add('has-item');
            } else {
                slot.title = `Слот ${index + 1}`;
                slot.classList.remove('has-item');
            }
        });
    }
    
    createItemIcon(item) {
        const icon = document.createElement('div');
        icon.className = 'item-icon';
        
        // Color based on rarity
        const rarityColors = {
            'common': '#ffffff',
            'uncommon': '#00ff00',
            'rare': '#0066ff',
            'epic': '#9966ff',
            'legendary': '#ff6600'
        };
        
        icon.style.backgroundColor = rarityColors[item.rarity] || '#ffffff';
        
        // Add item type indicator
        const typeIndicator = document.createElement('div');
        typeIndicator.style.cssText = `
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 8px;
            height: 8px;
            background: ${item.type === 'weapon' ? '#ff4444' : '#4444ff'};
            border-radius: 50%;
        `;
        icon.appendChild(typeIndicator);
        
        return icon;
    }
    
    createItemTooltip(item) {
        let tooltip = `${item.name}\n`;
        tooltip += `Тип: ${item.type === 'weapon' ? 'Зброя' : 'Обладунок'}\n`;
        tooltip += `Рідкість: ${item.rarity}\n`;
        
        if (item.stats) {
            if (item.stats.attack) tooltip += `Атака: +${item.stats.attack}\n`;
            if (item.stats.defense) tooltip += `Захист: +${item.stats.defense}\n`;
            if (item.stats.maxHp) tooltip += `Здоров'я: +${item.stats.maxHp}\n`;
        }
        
        tooltip += `Вартість: ${item.value} золота`;
        
        return tooltip;
    }
    
    updateGameLog() {
        const log = this.elements.gameLog;
        const entries = this.gameState.combatLog.slice(-20); // Show last 20 entries
        
        // Only update if there are new entries
        if (log.children.length !== entries.length) {
            log.innerHTML = '';
            
            entries.forEach(entry => {
                const div = document.createElement('div');
                div.className = `log-entry ${entry.type}`;
                div.textContent = entry.message;
                log.appendChild(div);
            });
            
            // Auto-scroll to bottom
            log.scrollTop = log.scrollHeight;
        }
    }
    
    updateSkills() {
        if (!this.skillSystem) return;

        const skills = this.skillSystem.skills;

        // Update skill displays
        if (this.elements.combatSkill) {
            const combat = skills.combat;
            this.elements.combatSkill.textContent = `${combat.level} (${combat.experience}/${combat.experienceToNext})`;
        }

        if (this.elements.explorationSkill) {
            const exploration = skills.exploration;
            this.elements.explorationSkill.textContent = `${exploration.level} (${exploration.experience}/${exploration.experienceToNext})`;
        }

        if (this.elements.survivalSkill) {
            const survival = skills.survival;
            this.elements.survivalSkill.textContent = `${survival.level} (${survival.experience}/${survival.experienceToNext})`;
        }

        if (this.elements.luckSkill) {
            const luck = skills.luck;
            this.elements.luckSkill.textContent = `${luck.level} (${luck.experience}/${luck.experienceToNext})`;
        }

        if (this.elements.skillPoints) {
            this.elements.skillPoints.textContent = this.skillSystem.skillPoints;
        }
    }

    updateAchievements() {
        if (!this.skillSystem) return;

        const achievements = this.skillSystem.achievements;
        const unlockedCount = Object.values(achievements).filter(a => a.unlocked).length;
        const totalCount = Object.keys(achievements).length;

        if (this.elements.achievementCount) {
            this.elements.achievementCount.textContent = `${unlockedCount}/${totalCount}`;
        }
    }

    updatePrestige() {
        if (!this.prestigeSystem) return;

        const prestigeInfo = this.prestigeSystem.getPrestigeInfo();

        if (this.elements.prestigeLevel) {
            this.elements.prestigeLevel.textContent = prestigeInfo.level;
        }

        if (this.elements.prestigePoints) {
            this.elements.prestigePoints.textContent = prestigeInfo.points;
        }

        if (this.elements.prestigeBtn) {
            const canPrestige = prestigeInfo.canPrestige;
            const pointsFromReset = prestigeInfo.pointsFromReset;

            this.elements.prestigeBtn.disabled = !canPrestige;
            this.elements.prestigeBtn.textContent = canPrestige ?
                `Престиж (${pointsFromReset} очок)` :
                'Престиж (недоступно)';
        }
    }

    updateButtons() {
        // Disable buttons based on game state
        this.elements.nextRoomBtn.disabled = this.gameState.isInCombat;
        this.elements.nextFloorBtn.disabled =
            this.gameState.isInCombat || this.gameState.currentRoom < this.gameState.maxRoomsPerFloor;
    }
    
    updateAnimations() {
        // Smooth bar animations
        this.animateBar('healthBar', this.elements.hpBar);
        this.animateBar('xpBar', this.elements.xpBar);
        this.animateBar('enemyHealthBar', this.elements.enemyHpBar);
    }
    
    animateBar(animationKey, element) {
        const animation = this.animations[animationKey];
        const diff = animation.target - animation.current;
        
        if (Math.abs(diff) > 0.001) {
            animation.current += diff * 0.1; // Smooth interpolation
            element.style.width = `${animation.current * 100}%`;
        }
    }
    
    // Event handlers
    handleInventoryClick(index) {
        const item = this.gameState.player.inventory[index];
        if (!item) return;
        
        // Show item context menu or auto-equip
        if (this.canEquipItem(item)) {
            this.equipItem(item, index);
        } else {
            this.showItemContextMenu(item, index);
        }
    }
    
    canEquipItem(item) {
        return item.type === 'weapon' || item.type === 'armor';
    }
    
    equipItem(item, index) {
        const slot = item.type === 'weapon' ? 'weapon' : 'armor';
        
        // Remove from inventory
        this.gameState.removeItemFromInventory(index);
        
        // Equip item
        this.gameState.equipItem(item, slot);
        
        this.gameState.addLogEntry(`Екіпірував ${item.name}`, 'loot');
        this.updateUI();
    }
    
    showItemContextMenu(item, index) {
        // Simple context menu for now
        const actions = ['Використати', 'Продати', 'Викинути'];
        const action = prompt(`${item.name}\n\nОберіть дію:\n1. ${actions[0]}\n2. ${actions[1]}\n3. ${actions[2]}`);
        
        switch (action) {
            case '1':
                this.useItem(item, index);
                break;
            case '2':
                this.sellItem(item, index);
                break;
            case '3':
                this.discardItem(index);
                break;
        }
    }
    
    useItem(item, index) {
        // Use item logic
        if (item.type === 'potion') {
            this.gameState.healPlayer(50);
            this.gameState.removeItemFromInventory(index);
            this.gameState.addLogEntry(`Використав ${item.name}`, 'heal');
        }
        this.updateUI();
    }
    
    sellItem(item, index) {
        const sellPrice = Math.floor(item.value * 0.5);
        this.gameState.gainGold(sellPrice);
        this.gameState.removeItemFromInventory(index);
        this.gameState.addLogEntry(`Продав ${item.name} за ${sellPrice} золота`, 'loot');
        this.updateUI();
    }
    
    discardItem(index) {
        const item = this.gameState.removeItemFromInventory(index);
        this.gameState.addLogEntry(`Викинув ${item.name}`, 'system');
        this.updateUI();
    }
    
    handleKeyPress(e) {
        switch (e.key) {
            case ' ': // Spacebar - toggle auto fight
                e.preventDefault();
                this.elements.autoFightBtn.click();
                break;
            case 'Enter': // Enter - next room
                e.preventDefault();
                if (!this.elements.nextRoomBtn.disabled) {
                    this.elements.nextRoomBtn.click();
                }
                break;
            case 'Escape': // Escape - show menu
                e.preventDefault();
                this.showGameMenu();
                break;
        }
    }
    
    showAchievementsModal() {
        if (!this.skillSystem) return;

        const modal = document.createElement('div');
        modal.className = 'achievements-modal';
        modal.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #0a0a0a;
            border: 2px solid #00ff00;
            padding: 20px;
            z-index: 1000;
            min-width: 400px;
            max-height: 80vh;
            overflow-y: auto;
        `;

        let content = '<h3 style="color: #ffff00; text-align: center; margin-bottom: 15px;">Досягнення</h3>';

        Object.entries(this.skillSystem.achievements).forEach(([key, achievement]) => {
            const status = achievement.unlocked ? '✓' : '✗';
            const statusColor = achievement.unlocked ? '#00ff00' : '#666666';

            content += `
                <div style="margin: 10px 0; padding: 10px; border: 1px solid ${statusColor};">
                    <div style="color: ${statusColor}; font-weight: bold;">
                        ${status} ${achievement.name}
                    </div>
                    <div style="color: #cccccc; font-size: 12px; margin-top: 5px;">
                        ${achievement.description}
                    </div>
                </div>
            `;
        });

        content += '<button class="button" onclick="this.parentElement.remove()" style="width: 100%; margin-top: 15px;">Закрити</button>';

        modal.innerHTML = content;
        document.body.appendChild(modal);

        // Close on click outside
        setTimeout(() => {
            document.addEventListener('click', function closeModal(e) {
                if (!modal.contains(e.target)) {
                    modal.remove();
                    document.removeEventListener('click', closeModal);
                }
            });
        }, 100);
    }

    performPrestige() {
        if (!this.prestigeSystem) return;

        const prestigeInfo = this.prestigeSystem.getPrestigeInfo();
        if (!prestigeInfo.canPrestige) return;

        const confirmed = confirm(
            `Ви впевнені, що хочете виконати престиж?\n\n` +
            `Ви отримаєте ${prestigeInfo.pointsFromReset} очок престижу.\n` +
            `Весь прогрес буде скинуто, але ви збережете покращення престижу.\n\n` +
            `Продовжити?`
        );

        if (confirmed) {
            this.prestigeSystem.performPrestige();
            this.updateUI();
            this.showNotification('Престиж виконано!', 'success', 3000);
        }
    }

    showPrestigeUpgradesModal() {
        if (!this.prestigeSystem) return;

        const modal = document.createElement('div');
        modal.className = 'prestige-modal';
        modal.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #0a0a0a;
            border: 2px solid #ffff00;
            padding: 20px;
            z-index: 1000;
            min-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        `;

        const prestigeInfo = this.prestigeSystem.getPrestigeInfo();
        let content = `
            <h3 style="color: #ffff00; text-align: center; margin-bottom: 15px;">
                Покращення престижу
            </h3>
            <div style="text-align: center; margin-bottom: 15px;">
                Доступно очок: <span style="color: #ffff00;">${prestigeInfo.points}</span>
            </div>
        `;

        Object.entries(this.prestigeSystem.upgrades).forEach(([key, upgrade]) => {
            const cost = this.prestigeSystem.getUpgradeCost(key);
            const canAfford = prestigeInfo.points >= cost;
            const maxed = upgrade.currentLevel >= upgrade.maxLevel;

            const statusColor = maxed ? '#666666' : (canAfford ? '#00ff00' : '#ff4444');
            const statusText = maxed ? 'МАКС' : `${cost} очок`;

            content += `
                <div style="margin: 10px 0; padding: 10px; border: 1px solid ${statusColor};">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div style="color: #ffff00; font-weight: bold;">
                                ${upgrade.name} (${upgrade.currentLevel}/${upgrade.maxLevel})
                            </div>
                            <div style="color: #cccccc; font-size: 12px; margin-top: 5px;">
                                ${upgrade.description}
                            </div>
                        </div>
                        <button class="button"
                                onclick="window.game.ui.buyPrestigeUpgrade('${key}')"
                                ${!canAfford || maxed ? 'disabled' : ''}>
                            ${statusText}
                        </button>
                    </div>
                </div>
            `;
        });

        content += '<button class="button" onclick="this.parentElement.remove()" style="width: 100%; margin-top: 15px;">Закрити</button>';

        modal.innerHTML = content;
        document.body.appendChild(modal);

        // Close on click outside
        setTimeout(() => {
            document.addEventListener('click', function closeModal(e) {
                if (!modal.contains(e.target)) {
                    modal.remove();
                    document.removeEventListener('click', closeModal);
                }
            });
        }, 100);
    }

    buyPrestigeUpgrade(upgradeKey) {
        if (!this.prestigeSystem) return;

        const success = this.prestigeSystem.purchaseUpgrade(upgradeKey);
        if (success) {
            this.updateUI();
            this.showNotification('Покращення куплено!', 'success', 2000);

            // Refresh the modal
            document.querySelector('.prestige-modal')?.remove();
            this.showPrestigeUpgradesModal();
        } else {
            this.showNotification('Недостатньо очок престижу!', 'error', 2000);
        }
    }

    showSettingsModal() {
        if (!this.settingsManager) return;

        const modal = document.createElement('div');
        modal.className = 'settings-modal';
        modal.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #0a0a0a;
            border: 2px solid #00ff00;
            padding: 20px;
            z-index: 1000;
            min-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        `;

        const settings = this.settingsManager.settings;
        const stats = this.settingsManager.getFormattedStats();

        let content = `
            <h3 style="color: #ffff00; text-align: center; margin-bottom: 15px;">
                Налаштування та Статистика
            </h3>

            <div style="display: flex; gap: 20px;">
                <div style="flex: 1;">
                    <h4 style="color: #00ff00; margin-bottom: 10px;">Аудіо</h4>

                    <div style="margin: 10px 0;">
                        <label>Загальна гучність: ${Math.round(settings.masterVolume * 100)}%</label>
                        <input type="range" min="0" max="1" step="0.1" value="${settings.masterVolume}"
                               onchange="window.game.ui.updateSetting('masterVolume', parseFloat(this.value))"
                               style="width: 100%; margin-top: 5px;">
                    </div>

                    <div style="margin: 10px 0;">
                        <label>Музика: ${Math.round(settings.musicVolume * 100)}%</label>
                        <input type="range" min="0" max="1" step="0.1" value="${settings.musicVolume}"
                               onchange="window.game.ui.updateSetting('musicVolume', parseFloat(this.value))"
                               style="width: 100%; margin-top: 5px;">
                    </div>

                    <div style="margin: 10px 0;">
                        <label>Звукові ефекти: ${Math.round(settings.sfxVolume * 100)}%</label>
                        <input type="range" min="0" max="1" step="0.1" value="${settings.sfxVolume}"
                               onchange="window.game.ui.updateSetting('sfxVolume', parseFloat(this.value))"
                               style="width: 100%; margin-top: 5px;">
                    </div>

                    <div style="margin: 10px 0;">
                        <label>
                            <input type="checkbox" ${settings.audioEnabled ? 'checked' : ''}
                                   onchange="window.game.ui.updateSetting('audioEnabled', this.checked)">
                            Увімкнути звук
                        </label>
                    </div>

                    <h4 style="color: #00ff00; margin: 20px 0 10px 0;">Візуальні ефекти</h4>

                    <div style="margin: 10px 0;">
                        <label>
                            <input type="checkbox" ${settings.showDamageNumbers ? 'checked' : ''}
                                   onchange="window.game.ui.updateSetting('showDamageNumbers', this.checked)">
                            Показувати числа шкоди
                        </label>
                    </div>

                    <div style="margin: 10px 0;">
                        <label>
                            <input type="checkbox" ${settings.showParticles ? 'checked' : ''}
                                   onchange="window.game.ui.updateSetting('showParticles', this.checked)">
                            Показувати частинки
                        </label>
                    </div>

                    <div style="margin: 10px 0;">
                        <label>
                            <input type="checkbox" ${settings.fastAnimations ? 'checked' : ''}
                                   onchange="window.game.ui.updateSetting('fastAnimations', this.checked)">
                            Швидкі анімації
                        </label>
                    </div>

                    <div style="margin: 10px 0;">
                        <label>
                            <input type="checkbox" ${settings.compactMode ? 'checked' : ''}
                                   onchange="window.game.ui.updateSetting('compactMode', this.checked)">
                            Компактний режим
                        </label>
                    </div>
                </div>

                <div style="flex: 1;">
                    <h4 style="color: #00ff00; margin-bottom: 10px;">Статистика сесії</h4>
        `;

        Object.entries(stats.session).forEach(([key, value]) => {
            content += `<div style="margin: 5px 0;">${key}: <span style="color: #ffff00;">${value}</span></div>`;
        });

        content += `
                    <h4 style="color: #00ff00; margin: 20px 0 10px 0;">Рекорди</h4>
        `;

        Object.entries(stats.records).forEach(([key, value]) => {
            content += `<div style="margin: 5px 0;">${key}: <span style="color: #ffff00;">${value}</span></div>`;
        });

        content += `
                    <h4 style="color: #00ff00; margin: 20px 0 10px 0;">Ефективність</h4>
        `;

        Object.entries(stats.efficiency).forEach(([key, value]) => {
            content += `<div style="margin: 5px 0;">${key}: <span style="color: #ffff00;">${value}</span></div>`;
        });

        content += `
                </div>
            </div>

            <div style="text-align: center; margin-top: 20px;">
                <button class="button" onclick="window.game.ui.showDetailedStats()">Детальна статистика</button>
                <button class="button" onclick="window.game.ui.exportData()">Експорт даних</button>
                <button class="button" onclick="this.parentElement.parentElement.remove()">Закрити</button>
            </div>
        `;

        modal.innerHTML = content;
        document.body.appendChild(modal);

        // Close on click outside
        setTimeout(() => {
            document.addEventListener('click', function closeModal(e) {
                if (!modal.contains(e.target)) {
                    modal.remove();
                    document.removeEventListener('click', closeModal);
                }
            });
        }, 100);
    }

    updateSetting(key, value) {
        if (this.settingsManager) {
            this.settingsManager.updateSetting(key, value);
            this.showNotification('Налаштування збережено', 'success', 1500);
        }
    }

    showDetailedStats() {
        if (!this.settingsManager) return;

        const stats = this.settingsManager.getFormattedStats();

        const modal = document.createElement('div');
        modal.className = 'detailed-stats-modal';
        modal.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #0a0a0a;
            border: 2px solid #00ff00;
            padding: 20px;
            z-index: 1001;
            min-width: 700px;
            max-height: 80vh;
            overflow-y: auto;
        `;

        let content = '<h3 style="color: #ffff00; text-align: center; margin-bottom: 15px;">Детальна статистика</h3>';

        Object.entries(stats).forEach(([category, categoryStats]) => {
            content += `<h4 style="color: #00ff00; margin: 15px 0 10px 0;">${category.charAt(0).toUpperCase() + category.slice(1)}</h4>`;
            Object.entries(categoryStats).forEach(([key, value]) => {
                content += `<div style="margin: 5px 0; display: flex; justify-content: space-between;">
                    <span>${key}:</span>
                    <span style="color: #ffff00;">${value}</span>
                </div>`;
            });
        });

        content += '<button class="button" onclick="this.parentElement.remove()" style="width: 100%; margin-top: 15px;">Закрити</button>';

        modal.innerHTML = content;
        document.body.appendChild(modal);
    }

    exportData() {
        if (!this.settingsManager) return;

        const data = {
            settings: this.settingsManager.exportSettings(),
            gameState: this.gameState.serialize(),
            skills: this.skillSystem ? this.skillSystem.serialize() : null,
            prestige: this.prestigeSystem ? this.prestigeSystem.serialize() : null,
            exportDate: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `idle_roguelike_data_${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);

        this.showNotification('Дані експортовано!', 'success', 2000);
    }

    showGameMenu() {
        const menu = document.createElement('div');
        menu.className = 'game-menu';
        menu.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #0a0a0a;
            border: 2px solid #00ff00;
            padding: 20px;
            z-index: 1000;
            min-width: 300px;
        `;

        menu.innerHTML = `
            <h3 style="color: #ffff00; text-align: center; margin-bottom: 15px;">Меню гри</h3>
            <button class="button" onclick="this.parentElement.remove()">Продовжити</button>
            <button class="button" onclick="location.reload()">Перезапустити</button>
            <button class="button" onclick="this.parentElement.remove()">Налаштування</button>
            <button class="button" onclick="this.parentElement.remove()">Статистика</button>
        `;

        document.body.appendChild(menu);

        // Close on click outside
        setTimeout(() => {
            document.addEventListener('click', function closeMenu(e) {
                if (!menu.contains(e.target)) {
                    menu.remove();
                    document.removeEventListener('click', closeMenu);
                }
            });
        }, 100);
    }
    
    // Notification system
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #0a0a0a;
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 10px 15px;
            border-radius: 5px;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;
        
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }, duration);
    }
    
    // Sound effects (placeholder)
    playHoverSound() {
        // This would integrate with the audio manager
        console.log('Hover sound');
    }
    
    playClickSound() {
        // This would integrate with the audio manager
        console.log('Click sound');
    }
    
    // Mobile responsiveness
    handleMobileLayout() {
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            // Adjust UI for mobile
            document.body.classList.add('mobile-layout');
        } else {
            document.body.classList.remove('mobile-layout');
        }
    }
    
    // Initialize mobile handling
    initMobileSupport() {
        this.handleMobileLayout();
        window.addEventListener('resize', () => this.handleMobileLayout());
        
        // Touch event handling
        let touchStartY = 0;
        document.addEventListener('touchstart', (e) => {
            touchStartY = e.touches[0].clientY;
        });
        
        document.addEventListener('touchmove', (e) => {
            const touchY = e.touches[0].clientY;
            const deltaY = touchStartY - touchY;
            
            // Prevent overscroll on game canvas
            if (e.target.closest('#gameCanvas')) {
                e.preventDefault();
            }
        });
    }
}

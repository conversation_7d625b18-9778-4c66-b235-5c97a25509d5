// Procedural content generation
export class ContentGenerator {
    constructor() {
        // Name generation data
        this.enemyPrefixes = [
            'Темний', 'Палаючий', 'Крижаний', 'Отруйний', 'Древній',
            'Дикий', 'Кровожерний', 'Примарний', 'Проклятий', 'Злий',
            'Могутній', 'Швидкий', 'Хитрий', 'Жорстокий', 'Безжальний'
        ];
        
        this.enemyTypes = [
            'Гоблін', 'Орк', 'Скелет', 'Зомбі', 'Вовк',
            'Павук', 'Кажан', 'Слиз', 'Гриб', 'Дух',
            'Тролль', 'Огр', 'Мінотавр', 'Дракон', 'Демон'
        ];
        
        this.itemPrefixes = [
            'Зачарований', 'Магічний', 'Легендарний', 'Древній', 'Проклятий',
            'Благословенний', 'Могутній', 'Швидкий', 'Гострий', 'Міцний',
            'Блискучий', 'Таємничий', 'Рунічний', 'Ельфійський', 'Драконячий'
        ];
        
        this.weaponTypes = [
            'Меч', 'Сокира', 'Булава', 'Кинджал', 'Спис',
            'Лук', 'Арбалет', 'Молот', 'Катана', 'Рапіра'
        ];
        
        this.armorTypes = [
            'Шолом', 'Кольчуга', 'Латний обладунок', 'Щит', 'Рукавички',
            'Чоботи', 'Плащ', 'Амулет', 'Кільце', 'Пояс'
        ];
        
        this.locationAdjectives = [
            'Темний', 'Забутий', 'Древній', 'Проклятий', 'Таємничий',
            'Глибокий', 'Холодний', 'Вологий', 'Моторошний', 'Безкрайній'
        ];
        
        this.locationTypes = [
            'Печера', 'Підземелля', 'Катакомби', 'Склеп', 'Шахта',
            'Лабіринт', 'Руїни', 'Храм', 'Фортеця', 'Вежа'
        ];
        
        // Random seed for consistent generation
        this.seed = Date.now();
    }
    
    // Seeded random number generator
    seededRandom(seed) {
        const x = Math.sin(seed) * 10000;
        return x - Math.floor(x);
    }
    
    randomFromArray(array, seed = null) {
        const random = seed !== null ? this.seededRandom(seed) : Math.random();
        return array[Math.floor(random * array.length)];
    }
    
    // Enemy generation
    generateEnemy(floor, room) {
        const seed = this.seed + floor * 1000 + room * 100;
        
        // Base stats scale with floor
        const baseHp = 30 + floor * 15;
        const baseAttack = 8 + floor * 3;
        const baseDefense = 2 + floor * 1;
        
        // Generate name
        const prefix = this.randomFromArray(this.enemyPrefixes, seed);
        const type = this.randomFromArray(this.enemyTypes, seed + 1);
        const name = `${prefix} ${type}`;
        
        // Determine enemy type for sprite
        const spriteType = this.getSpriteTypeFromName(type);
        
        // Random stat variations (±20%)
        const hpVariation = 1 + (this.seededRandom(seed + 2) - 0.5) * 0.4;
        const attackVariation = 1 + (this.seededRandom(seed + 3) - 0.5) * 0.4;
        const defenseVariation = 1 + (this.seededRandom(seed + 4) - 0.5) * 0.4;
        
        const enemy = {
            name: name,
            type: spriteType,
            hp: Math.floor(baseHp * hpVariation),
            maxHp: Math.floor(baseHp * hpVariation),
            attack: Math.floor(baseAttack * attackVariation),
            defense: Math.floor(baseDefense * defenseVariation),
            expReward: Math.floor((20 + floor * 5) * (0.8 + this.seededRandom(seed + 5) * 0.4)),
            goldReward: Math.floor((5 + floor * 2) * (0.8 + this.seededRandom(seed + 6) * 0.4)),
            dropChance: 0.3 + this.seededRandom(seed + 7) * 0.3,
            loot: null,
            isInCombat: false
        };
        
        // Generate potential loot
        if (this.seededRandom(seed + 8) < 0.4) {
            enemy.loot = this.generateItem(floor, seed + 10);
        }
        
        return enemy;
    }
    
    getSpriteTypeFromName(typeName) {
        const typeMap = {
            'Гоблін': 'goblin',
            'Орк': 'orc',
            'Скелет': 'skeleton',
            'Зомбі': 'skeleton',
            'Вовк': 'goblin',
            'Павук': 'goblin',
            'Кажан': 'goblin',
            'Слиз': 'goblin',
            'Гриб': 'goblin',
            'Дух': 'skeleton',
            'Тролль': 'orc',
            'Огр': 'orc',
            'Мінотавр': 'orc',
            'Дракон': 'orc',
            'Демон': 'skeleton'
        };
        
        return typeMap[typeName] || 'goblin';
    }
    
    // Item generation
    generateItem(floor, seed = null) {
        if (seed === null) seed = Math.floor(Math.random() * 10000);
        
        const itemType = this.seededRandom(seed) < 0.6 ? 'weapon' : 'armor';
        const isWeapon = itemType === 'weapon';
        
        // Generate name
        const prefix = this.randomFromArray(this.itemPrefixes, seed + 1);
        const baseType = isWeapon ? 
            this.randomFromArray(this.weaponTypes, seed + 2) :
            this.randomFromArray(this.armorTypes, seed + 2);
        
        const name = `${prefix} ${baseType}`;
        
        // Generate stats based on floor and rarity
        const rarity = this.getItemRarity(seed + 3);
        const rarityMultiplier = this.getRarityMultiplier(rarity);
        
        const baseStatValue = Math.floor((5 + floor * 2) * rarityMultiplier);
        const statVariation = 0.8 + this.seededRandom(seed + 4) * 0.4;
        
        const item = {
            name: name,
            type: itemType,
            rarity: rarity,
            stats: {},
            value: Math.floor(baseStatValue * 2 * statVariation),
            description: this.generateItemDescription(name, rarity)
        };
        
        // Assign stats based on type
        if (isWeapon) {
            item.stats.attack = Math.floor(baseStatValue * statVariation);
            item.spriteType = 'sword';
            
            // Chance for additional stats
            if (this.seededRandom(seed + 5) < 0.3) {
                item.stats.defense = Math.floor(baseStatValue * 0.3 * statVariation);
            }
        } else {
            item.stats.defense = Math.floor(baseStatValue * statVariation);
            item.spriteType = 'shield';
            
            // Chance for HP bonus
            if (this.seededRandom(seed + 6) < 0.4) {
                item.stats.maxHp = Math.floor(baseStatValue * 3 * statVariation);
            }
        }
        
        return item;
    }
    
    getItemRarity(seed) {
        const roll = this.seededRandom(seed);
        if (roll < 0.5) return 'common';
        if (roll < 0.8) return 'uncommon';
        if (roll < 0.95) return 'rare';
        if (roll < 0.99) return 'epic';
        return 'legendary';
    }
    
    getRarityMultiplier(rarity) {
        const multipliers = {
            'common': 1.0,
            'uncommon': 1.3,
            'rare': 1.7,
            'epic': 2.2,
            'legendary': 3.0
        };
        return multipliers[rarity] || 1.0;
    }
    
    generateItemDescription(name, rarity) {
        const descriptions = {
            'common': 'Звичайний предмет',
            'uncommon': 'Незвичайний предмет з легкою магією',
            'rare': 'Рідкісний предмет з потужною магією',
            'epic': 'Епічний предмет легендарної сили',
            'legendary': 'Легендарний артефакт неймовірної могутності'
        };
        
        return descriptions[rarity] || 'Таємничий предмет';
    }
    
    // Location generation
    generateLocation(floor) {
        const seed = this.seed + floor * 500;
        
        const adjective = this.randomFromArray(this.locationAdjectives, seed);
        const type = this.randomFromArray(this.locationTypes, seed + 1);
        const name = `${adjective} ${type}`;
        
        const descriptions = [
            'Вологі стіни покриті мохом та грибами',
            'Повітря наповнене таємничими звуками',
            'Древні руни світяться слабким світлом',
            'Холодний вітер дме з глибин',
            'Кістки минулих мандрівників розкидані по підлозі',
            'Дивні тіні танцюють на стінах',
            'Запах старовини та магії наповнює повітря'
        ];
        
        return {
            name: name,
            type: 'dungeon',
            description: this.randomFromArray(descriptions, seed + 2),
            floor: floor,
            difficulty: Math.floor(floor / 5) + 1
        };
    }
    
    // Event generation
    generateRandomEvent(floor, room) {
        const seed = this.seed + floor * 1000 + room * 100 + 50;
        const eventRoll = this.seededRandom(seed);
        
        if (eventRoll < 0.6) {
            // Combat encounter
            return {
                type: 'combat',
                enemy: this.generateEnemy(floor, room)
            };
        } else if (eventRoll < 0.8) {
            // Treasure
            return {
                type: 'treasure',
                gold: Math.floor((10 + floor * 3) * (0.8 + this.seededRandom(seed + 1) * 0.4)),
                item: this.seededRandom(seed + 2) < 0.3 ? this.generateItem(floor, seed + 3) : null
            };
        } else if (eventRoll < 0.9) {
            // Healing fountain
            return {
                type: 'healing',
                amount: Math.floor(20 + floor * 5)
            };
        } else {
            // Special event
            return this.generateSpecialEvent(floor, seed + 10);
        }
    }
    
    generateSpecialEvent(floor, seed) {
        const events = [
            {
                type: 'shrine',
                name: 'Святиня Сили',
                description: 'Древня святиня дарує благословення',
                effect: { type: 'stat_boost', stat: 'attack', amount: 2 }
            },
            {
                type: 'shrine',
                name: 'Святиня Витривалості',
                description: 'Святиня зміцнює тіло та дух',
                effect: { type: 'stat_boost', stat: 'maxHp', amount: 20 }
            },
            {
                type: 'merchant',
                name: 'Мандрівний торговець',
                description: 'Таємничий торговець пропонує свої товари',
                items: [
                    this.generateItem(floor, seed + 1),
                    this.generateItem(floor, seed + 2)
                ]
            },
            {
                type: 'trap',
                name: 'Пастка',
                description: 'Ви потрапили в пастку!',
                damage: Math.floor(10 + floor * 2)
            }
        ];
        
        return this.randomFromArray(events, seed);
    }
    
    // Mutation system for dynamic content
    generateMutation(floor) {
        const mutations = [
            {
                name: 'Сувій Берсерка',
                description: 'Всі вороги стають агресивнішими (+50% атака, -25% захист)',
                effect: { enemyAttackMultiplier: 1.5, enemyDefenseMultiplier: 0.75 }
            },
            {
                name: 'Сувій Багатства',
                description: 'Збільшує кількість золота та шанс випадання предметів',
                effect: { goldMultiplier: 2.0, dropChanceBonus: 0.3 }
            },
            {
                name: 'Сувій Швидкості',
                description: 'Прискорює всі процеси в грі',
                effect: { speedMultiplier: 1.5 }
            },
            {
                name: 'Сувій Хаосу',
                description: 'Випадкові ефекти на кожному поверсі',
                effect: { chaosMode: true }
            }
        ];
        
        const seed = this.seed + floor * 777;
        return this.randomFromArray(mutations, seed);
    }
    
    // Utility methods
    shouldSpawnEnemy(floor, room) {
        const seed = this.seed + floor * 1000 + room * 100 + 25;
        return this.seededRandom(seed) < 0.7; // 70% chance
    }

    shouldSpawnBoss(floor, room, maxRooms) {
        // Boss every 5 floors on last room
        return floor % 5 === 0 && room === maxRooms;
    }
    
    shouldSpawnTreasure(floor, room) {
        const seed = this.seed + floor * 1000 + room * 100 + 75;
        return this.seededRandom(seed) < 0.2; // 20% chance
    }
    
    getFloorDifficulty(floor) {
        return {
            enemyStatMultiplier: 1 + (floor - 1) * 0.2,
            enemyVariety: Math.min(5, Math.floor(floor / 2) + 1),
            specialEventChance: Math.min(0.3, floor * 0.02),
            bossChance: floor % 5 === 0 ? 1.0 : 0.0
        };
    }
}

// Shop system with multiple vendors and dynamic inventory
export class ShopSystem {
    constructor(gameState, contentGenerator) {
        this.gameState = gameState;
        this.contentGenerator = contentGenerator;
        
        // Shop types and their specializations
        this.shopTypes = {
            weaponsmith: {
                name: 'Зброярня',
                description: 'Спеціалізується на зброї та бойовому спорядженні',
                itemTypes: ['sword', 'axe', 'bow', 'dagger', 'staff'],
                priceMultiplier: 1.2,
                refreshInterval: 300000, // 5 minutes
                maxItems: 8
            },
            armorsmith: {
                name: 'Броньовик',
                description: 'Виготовляє броню та захисне спорядження',
                itemTypes: ['armor', 'helmet', 'shield', 'gloves', 'boots'],
                priceMultiplier: 1.1,
                refreshInterval: 300000,
                maxItems: 10
            },
            magicVendor: {
                name: 'Магічна лавка',
                description: 'Торгує магічними предметами та зіллями',
                itemTypes: ['ring', 'amulet', 'potion', 'scroll', 'staff'],
                priceMultiplier: 1.5,
                refreshInterval: 600000, // 10 minutes
                maxItems: 6
            },
            generalStore: {
                name: 'Загальний магазин',
                description: 'Різноманітні товари для пригодників',
                itemTypes: ['potion', 'food', 'tool', 'material'],
                priceMultiplier: 1.0,
                refreshInterval: 180000, // 3 minutes
                maxItems: 12
            },
            blackMarket: {
                name: 'Чорний ринок',
                description: 'Рідкісні та заборонені товари',
                itemTypes: ['legendary', 'epic', 'cursed'],
                priceMultiplier: 2.0,
                refreshInterval: 1800000, // 30 minutes
                maxItems: 4,
                unlockLevel: 20
            }
        };
        
        // Current shop inventories
        this.shopInventories = {};
        
        // Shop upgrade levels
        this.shopUpgrades = {
            weaponsmith: { level: 1, reputation: 0 },
            armorsmith: { level: 1, reputation: 0 },
            magicVendor: { level: 1, reputation: 0 },
            generalStore: { level: 1, reputation: 0 },
            blackMarket: { level: 1, reputation: 0 }
        };
        
        // Special currencies
        this.currencies = {
            gold: 0,
            tokens: 0,
            reputation: 0,
            crystals: 0
        };
        
        // Shop UI state
        this.currentShop = null;
        this.isVisible = false;
        
        this.initializeShops();
    }
    
    initializeShops() {
        // Initialize shop inventories
        Object.keys(this.shopTypes).forEach(shopType => {
            this.shopInventories[shopType] = {
                items: [],
                lastRefresh: 0,
                nextRefresh: Date.now() + this.shopTypes[shopType].refreshInterval
            };
            this.refreshShopInventory(shopType);
        });
        
        // Sync currencies with game state
        this.currencies.gold = this.gameState.player.gold || 0;
    }
    
    // Generate shop inventory
    refreshShopInventory(shopType) {
        const shop = this.shopTypes[shopType];
        const inventory = this.shopInventories[shopType];
        
        // Check if shop is unlocked
        if (shop.unlockLevel && this.gameState.player.level < shop.unlockLevel) {
            return;
        }
        
        // Clear current inventory
        inventory.items = [];
        
        // Generate new items
        const itemCount = Math.floor(shop.maxItems * (0.7 + Math.random() * 0.3));
        const shopLevel = this.shopUpgrades[shopType].level;
        
        for (let i = 0; i < itemCount; i++) {
            const item = this.generateShopItem(shopType, shopLevel);
            if (item) {
                item.shopPrice = this.calculateItemPrice(item, shop.priceMultiplier);
                item.shopType = shopType;
                inventory.items.push(item);
            }
        }
        
        // Update refresh times
        inventory.lastRefresh = Date.now();
        inventory.nextRefresh = Date.now() + shop.refreshInterval;
        
        console.log(`Refreshed ${shopType} inventory: ${inventory.items.length} items`);
    }
    
    generateShopItem(shopType, shopLevel) {
        const shop = this.shopTypes[shopType];
        const playerLevel = this.gameState.player.level;
        
        // Determine item level range
        const minLevel = Math.max(1, playerLevel - 2);
        const maxLevel = playerLevel + shopLevel;
        const itemLevel = minLevel + Math.floor(Math.random() * (maxLevel - minLevel + 1));
        
        // Select item type
        const itemType = shop.itemTypes[Math.floor(Math.random() * shop.itemTypes.length)];
        
        // Generate item based on shop specialization
        let item;
        if (shopType === 'magicVendor') {
            item = this.contentGenerator.generateMagicItem(itemLevel, itemType);
        } else if (shopType === 'blackMarket') {
            item = this.contentGenerator.generateRareItem(itemLevel);
        } else {
            // Для звичайних магазинів використовуємо базову генерацію
            item = this.contentGenerator.generateItem(itemLevel, itemType);
        }
        
        // Enhance item quality based on shop level
        if (shopLevel > 1) {
            item = this.enhanceItemForShop(item, shopLevel);
        }
        
        return item;
    }
    
    enhanceItemForShop(item, shopLevel) {
        // Increase chance of better rarity
        const rarityBonus = (shopLevel - 1) * 0.1;
        if (Math.random() < rarityBonus) {
            const rarities = ['common', 'uncommon', 'rare', 'epic', 'legendary'];
            const currentIndex = rarities.indexOf(item.rarity);
            if (currentIndex < rarities.length - 1) {
                item.rarity = rarities[currentIndex + 1];
            }
        }
        
        // Possible enhancement bonus
        if (Math.random() < 0.3 + (shopLevel - 1) * 0.1) {
            item.enhancement = Math.floor(Math.random() * shopLevel);
        }
        
        return item;
    }
    
    calculateItemPrice(item, priceMultiplier) {
        let basePrice = (item.level || 1) * 10;
        
        // Rarity multiplier
        const rarityMultipliers = {
            common: 1,
            uncommon: 2,
            rare: 4,
            epic: 8,
            legendary: 16
        };
        
        basePrice *= rarityMultipliers[item.rarity || 'common'] || 1;
        
        // Enhancement multiplier
        if (item.enhancement && item.enhancement > 0) {
            basePrice *= (1 + item.enhancement * 0.5);
        }
        
        // Shop multiplier
        basePrice *= priceMultiplier;
        
        return Math.floor(basePrice);
    }
    
    // Shop interactions
    buyItem(shopType, itemIndex, currency = 'gold') {
        const inventory = this.shopInventories[shopType];
        const item = inventory.items[itemIndex];
        
        if (!item) {
            return { success: false, message: 'Предмет не знайдено' };
        }
        
        // Check if player can afford
        const price = item.shopPrice;
        if (!this.canAfford(price, currency)) {
            return { success: false, message: 'Недостатньо коштів' };
        }
        
        // Check inventory space
        if (!this.gameState.hasInventorySpace()) {
            return { success: false, message: 'Інвентар переповнений' };
        }
        
        // Process purchase
        this.spendCurrency(price, currency);
        this.gameState.addItemToInventory(item);
        
        // Remove item from shop
        inventory.items.splice(itemIndex, 1);
        
        // Increase reputation
        this.increaseReputation(shopType, Math.floor(price / 100));
        
        this.gameState.addLogEntry(`Куплено: ${item.name} за ${price} золота`, 'shop');
        
        return { success: true, message: `Куплено ${item.name}` };
    }
    
    sellItem(item) {
        if (!item) {
            return { success: false, message: 'Предмет не знайдено' };
        }
        
        // Calculate sell price (50% of shop price)
        const sellPrice = Math.floor(this.calculateItemPrice(item, 1.0) * 0.5);
        
        // Remove from inventory
        this.gameState.removeItemFromInventory(item);
        
        // Add gold
        this.addCurrency(sellPrice, 'gold');
        
        this.gameState.addLogEntry(`Продано: ${item.name} за ${sellPrice} золота`, 'shop');
        
        return { success: true, message: `Продано за ${sellPrice} золота` };
    }
    
    // Currency management
    canAfford(amount, currency = 'gold') {
        return this.currencies[currency] >= amount;
    }
    
    spendCurrency(amount, currency = 'gold') {
        if (this.canAfford(amount, currency)) {
            this.currencies[currency] -= amount;
            
            // Sync with game state
            if (currency === 'gold') {
                this.gameState.player.gold = this.currencies.gold;
            }
            
            return true;
        }
        return false;
    }
    
    addCurrency(amount, currency = 'gold') {
        this.currencies[currency] += amount;
        
        // Sync with game state
        if (currency === 'gold') {
            this.gameState.player.gold = this.currencies.gold;
        }
    }
    
    // Reputation and upgrades
    increaseReputation(shopType, amount) {
        this.shopUpgrades[shopType].reputation += amount;
        
        // Check for level up
        const requiredRep = this.shopUpgrades[shopType].level * 1000;
        if (this.shopUpgrades[shopType].reputation >= requiredRep) {
            this.upgradeShop(shopType);
        }
    }
    
    upgradeShop(shopType) {
        this.shopUpgrades[shopType].level++;
        this.shopUpgrades[shopType].reputation = 0;
        
        this.gameState.addLogEntry(`${this.shopTypes[shopType].name} покращено до рівня ${this.shopUpgrades[shopType].level}!`, 'shop');
        
        // Refresh inventory with better items
        this.refreshShopInventory(shopType);
    }
    
    // Update system
    update(deltaTime) {
        const currentTime = Date.now();
        
        // Check for shop inventory refreshes
        Object.keys(this.shopInventories).forEach(shopType => {
            const inventory = this.shopInventories[shopType];
            if (currentTime >= inventory.nextRefresh) {
                this.refreshShopInventory(shopType);
            }
        });
        
        // Sync currencies
        this.currencies.gold = this.gameState.player.gold || 0;
    }
    
    // Get shop data for UI
    getShopData(shopType) {
        const shop = this.shopTypes[shopType];
        const inventory = this.shopInventories[shopType];
        const upgrades = this.shopUpgrades[shopType];
        
        // Check if unlocked
        if (shop.unlockLevel && this.gameState.player.level < shop.unlockLevel) {
            return {
                locked: true,
                unlockLevel: shop.unlockLevel,
                name: shop.name
            };
        }
        
        return {
            locked: false,
            name: shop.name,
            description: shop.description,
            level: upgrades.level,
            reputation: upgrades.reputation,
            nextLevelRep: upgrades.level * 1000,
            items: inventory.items,
            nextRefresh: inventory.nextRefresh,
            timeToRefresh: Math.max(0, inventory.nextRefresh - Date.now())
        };
    }
    
    // Get all available shops
    getAvailableShops() {
        return Object.keys(this.shopTypes).filter(shopType => {
            const shop = this.shopTypes[shopType];
            return !shop.unlockLevel || this.gameState.player.level >= shop.unlockLevel;
        });
    }
    
    // Serialization
    serialize() {
        return {
            shopInventories: this.shopInventories,
            shopUpgrades: this.shopUpgrades,
            currencies: this.currencies
        };
    }
    
    deserialize(data) {
        if (data.shopInventories) {
            this.shopInventories = { ...this.shopInventories, ...data.shopInventories };
        }
        if (data.shopUpgrades) {
            this.shopUpgrades = { ...this.shopUpgrades, ...data.shopUpgrades };
        }
        if (data.currencies) {
            this.currencies = { ...this.currencies, ...data.currencies };
        }
    }

    // UI Methods
    createShopInterface() {
        const shopPanel = document.createElement('div');
        shopPanel.id = 'shopPanel';
        shopPanel.className = 'shop-panel';
        shopPanel.style.cssText = `
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            width: 800px;
            height: 600px;
            background: rgba(0, 0, 0, 0.95);
            border: 2px solid #00ff00;
            border-radius: 10px;
            padding: 20px;
            display: none;
            z-index: 1000;
            color: #ffffff;
            font-family: 'Courier New', monospace;
            overflow-y: auto;
        `;

        // Title
        const title = document.createElement('h2');
        title.textContent = 'Магазини';
        title.style.cssText = `
            margin: 0 0 20px 0;
            text-align: center;
            color: #00ff00;
        `;
        shopPanel.appendChild(title);

        // Shop selection
        const shopSelection = document.createElement('div');
        shopSelection.className = 'shop-selection';
        shopSelection.style.cssText = `
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        `;

        // Create shop buttons
        Object.keys(this.shopTypes).forEach(shopType => {
            const shopData = this.getShopData(shopType);
            const button = document.createElement('button');
            button.textContent = shopData.name;
            button.className = 'shop-button';
            button.style.cssText = `
                padding: 10px 15px;
                background: ${shopData.locked ? '#666' : '#003300'};
                border: 1px solid #00ff00;
                color: ${shopData.locked ? '#999' : '#00ff00'};
                cursor: ${shopData.locked ? 'not-allowed' : 'pointer'};
                border-radius: 5px;
            `;

            if (!shopData.locked) {
                button.onclick = () => this.showShop(shopType);
            } else {
                button.title = `Розблокується на рівні ${shopData.unlockLevel}`;
            }

            shopSelection.appendChild(button);
        });

        shopPanel.appendChild(shopSelection);

        // Shop content area
        const shopContent = document.createElement('div');
        shopContent.id = 'shopContent';
        shopContent.style.cssText = `
            min-height: 400px;
            border: 1px solid #333;
            padding: 15px;
            background: #111;
        `;
        shopPanel.appendChild(shopContent);

        // Close button
        const closeBtn = document.createElement('button');
        closeBtn.textContent = '×';
        closeBtn.style.cssText = `
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            color: #ff0000;
            font-size: 24px;
            cursor: pointer;
        `;
        closeBtn.onclick = () => this.hideShopInterface();
        shopPanel.appendChild(closeBtn);

        document.body.appendChild(shopPanel);
        return shopPanel;
    }

    showShopInterface() {
        let panel = document.getElementById('shopPanel');
        if (!panel) {
            panel = this.createShopInterface();
        }

        panel.style.display = 'block';
        this.isVisible = true;

        // Show first available shop
        const availableShops = this.getAvailableShops();
        if (availableShops.length > 0) {
            this.showShop(availableShops[0]);
        }
    }

    hideShopInterface() {
        const panel = document.getElementById('shopPanel');
        if (panel) {
            panel.style.display = 'none';
        }
        this.isVisible = false;
        this.currentShop = null;
    }

    toggleShopInterface() {
        if (this.isVisible) {
            this.hideShopInterface();
        } else {
            this.showShopInterface();
        }
    }

    showShop(shopType) {
        this.currentShop = shopType;
        const shopData = this.getShopData(shopType);
        const content = document.getElementById('shopContent');

        if (!content) return;

        content.innerHTML = '';

        // Shop header
        const header = document.createElement('div');
        header.style.cssText = `
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #333;
        `;

        const shopName = document.createElement('h3');
        shopName.textContent = shopData.name;
        shopName.style.color = '#00ff00';
        header.appendChild(shopName);

        const shopDesc = document.createElement('p');
        shopDesc.textContent = shopData.description;
        shopDesc.style.color = '#ccc';
        header.appendChild(shopDesc);

        const shopInfo = document.createElement('div');
        shopInfo.innerHTML = `
            <div>Рівень: ${shopData.level}</div>
            <div>Репутація: ${shopData.reputation}/${shopData.nextLevelRep}</div>
            <div>Оновлення через: ${this.formatTime(shopData.timeToRefresh)}</div>
        `;
        shopInfo.style.cssText = `
            font-size: 12px;
            color: #999;
            margin-top: 10px;
        `;
        header.appendChild(shopInfo);

        content.appendChild(header);

        // Items grid
        const itemsGrid = document.createElement('div');
        itemsGrid.style.cssText = `
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 15px;
        `;

        shopData.items.forEach((item, index) => {
            const itemCard = this.createItemCard(item, index, shopType);
            itemsGrid.appendChild(itemCard);
        });

        content.appendChild(itemsGrid);

        // Refresh button
        const refreshBtn = document.createElement('button');
        refreshBtn.textContent = 'Оновити (100 золота)';
        refreshBtn.style.cssText = `
            margin-top: 20px;
            padding: 10px 20px;
            background: #003300;
            border: 1px solid #00ff00;
            color: #00ff00;
            cursor: pointer;
        `;
        refreshBtn.onclick = () => this.manualRefresh(shopType);
        content.appendChild(refreshBtn);
    }

    createItemCard(item, index, shopType) {
        const card = document.createElement('div');
        card.style.cssText = `
            border: 1px solid #333;
            padding: 10px;
            background: #222;
            border-radius: 5px;
            cursor: pointer;
        `;

        const rarityColors = {
            common: '#ffffff',
            uncommon: '#00ff00',
            rare: '#0066ff',
            epic: '#9966ff',
            legendary: '#ff6600'
        };

        card.style.borderColor = rarityColors[item.rarity] || '#333';

        const itemName = document.createElement('div');
        itemName.textContent = item.name;
        itemName.style.cssText = `
            font-weight: bold;
            color: ${rarityColors[item.rarity] || '#fff'};
            margin-bottom: 5px;
        `;
        card.appendChild(itemName);

        const itemType = document.createElement('div');
        itemType.textContent = `${item.type} (Рівень ${item.level})`;
        itemType.style.cssText = `
            font-size: 12px;
            color: #999;
            margin-bottom: 5px;
        `;
        card.appendChild(itemType);

        if (item.enhancement > 0) {
            const enhancement = document.createElement('div');
            enhancement.textContent = `+${item.enhancement}`;
            enhancement.style.cssText = `
                color: #ffff00;
                font-weight: bold;
                margin-bottom: 5px;
            `;
            card.appendChild(enhancement);
        }

        const price = document.createElement('div');
        price.textContent = `${item.shopPrice} золота`;
        price.style.cssText = `
            color: #ffd700;
            font-weight: bold;
            margin-top: 10px;
        `;
        card.appendChild(price);

        const buyBtn = document.createElement('button');
        buyBtn.textContent = 'Купити';
        buyBtn.style.cssText = `
            width: 100%;
            margin-top: 10px;
            padding: 5px;
            background: #003300;
            border: 1px solid #00ff00;
            color: #00ff00;
            cursor: pointer;
        `;

        buyBtn.onclick = (e) => {
            e.stopPropagation();
            this.buyItemFromShop(shopType, index);
        };

        card.appendChild(buyBtn);

        return card;
    }

    buyItemFromShop(shopType, itemIndex) {
        const result = this.buyItem(shopType, itemIndex);

        if (result.success) {
            this.showShop(shopType); // Refresh display
            this.gameState.needsUIUpdate = true;
        } else {
            alert(result.message);
        }
    }

    manualRefresh(shopType) {
        if (this.canAfford(100)) {
            this.spendCurrency(100);
            this.refreshShopInventory(shopType);
            this.showShop(shopType);
        } else {
            alert('Недостатньо золота для оновлення');
        }
    }

    formatTime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        if (hours > 0) {
            return `${hours}г ${minutes % 60}хв`;
        } else if (minutes > 0) {
            return `${minutes}хв ${seconds % 60}с`;
        } else {
            return `${seconds}с`;
        }
    }
}

<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Background Demo - Idle Roguelike Adventure</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            background: #000;
            color: #00ff00;
        }

        .demo-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
        }

        .biome-demo {
            width: 400px;
            height: 300px;
            border: 2px solid #00ff00;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .biome-canvas {
            width: 100%;
            height: 100%;
            display: block;
        }

        .biome-label {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 5px 10px;
            border-radius: 3px;
            color: #ffff00;
            font-weight: bold;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        button {
            background: #003300;
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 10px 20px;
            cursor: pointer;
            margin: 5px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }

        button:hover {
            background: #006600;
            box-shadow: 0 0 10px #00ff00;
        }

        .info {
            text-align: center;
            margin: 20px 0;
            color: #cccccc;
        }
    </style>
</head>
<body>
    <h1>🎨 Background Demo - Біомні фони з персонажами</h1>
    
    <div class="info">
        Демонстрація нових фонів з персонажами та ворогами для різних біомів
    </div>

    <div class="controls">
        <button onclick="startDemo()">Запустити демо</button>
        <button onclick="stopDemo()">Зупинити</button>
        <button onclick="nextBiome()">Наступний біом</button>
    </div>

    <div class="demo-container">
        <div class="biome-demo">
            <canvas class="biome-canvas" id="caveCanvas" width="400" height="300"></canvas>
            <div class="biome-label">Печера</div>
        </div>
        
        <div class="biome-demo">
            <canvas class="biome-canvas" id="forestCanvas" width="400" height="300"></canvas>
            <div class="biome-label">Ліс</div>
        </div>
        
        <div class="biome-demo">
            <canvas class="biome-canvas" id="desertCanvas" width="400" height="300"></canvas>
            <div class="biome-label">Пустеля</div>
        </div>
        
        <div class="biome-demo">
            <canvas class="biome-canvas" id="glacierCanvas" width="400" height="300"></canvas>
            <div class="biome-label">Льодовики</div>
        </div>
        
        <div class="biome-demo">
            <canvas class="biome-canvas" id="volcanoCanvas" width="400" height="300"></canvas>
            <div class="biome-label">Вулкан</div>
        </div>
        
        <div class="biome-demo">
            <canvas class="biome-canvas" id="abyssCanvas" width="400" height="300"></canvas>
            <div class="biome-label">Безодня</div>
        </div>
    </div>

    <script type="module">
        let renderers = [];
        let animationId = null;
        let currentBiomeIndex = 0;

        const biomes = [
            { name: 'cave', floor: 5 },
            { name: 'forest', floor: 15 },
            { name: 'desert', floor: 25 },
            { name: 'glacier', floor: 35 },
            { name: 'volcano', floor: 45 },
            { name: 'abyss', floor: 55 }
        ];

        async function initDemo() {
            try {
                const { Renderer } = await import('./js/renderer.js');
                
                // Create mock game state
                const mockGameState = {
                    currentFloor: 1,
                    currentRoom: 1,
                    currentLocation: { name: 'Тестова локація' },
                    inCombat: true,
                    player: {
                        hp: 75,
                        maxHp: 100,
                        equipment: {
                            weapon: { name: 'Меч' },
                            shield: { name: 'Щит' }
                        }
                    },
                    currentEnemy: {
                        name: 'Гоблін',
                        type: 'goblin',
                        level: 5,
                        hp: 30,
                        maxHp: 50
                    }
                };

                // Initialize renderers for each biome
                biomes.forEach((biome, index) => {
                    const canvas = document.getElementById(`${biome.name}Canvas`);
                    const ctx = canvas.getContext('2d');
                    const renderer = new Renderer(canvas, ctx);
                    
                    // Set floor for biome
                    const gameState = { ...mockGameState, currentFloor: biome.floor };
                    
                    renderers.push({ renderer, gameState, canvas, ctx });
                });

                console.log('Demo initialized successfully');
            } catch (error) {
                console.error('Failed to initialize demo:', error);
            }
        }

        function animate() {
            renderers.forEach(({ renderer, gameState }) => {
                // Clear with biome background
                renderer.clear(gameState);
                
                // Render world with characters
                renderer.renderWorld(gameState);
            });

            animationId = requestAnimationFrame(animate);
        }

        window.startDemo = function() {
            if (renderers.length === 0) {
                initDemo().then(() => {
                    animate();
                });
            } else {
                animate();
            }
        };

        window.stopDemo = function() {
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
        };

        window.nextBiome = function() {
            currentBiomeIndex = (currentBiomeIndex + 1) % biomes.length;
            const biome = biomes[currentBiomeIndex];
            
            // Highlight current biome
            document.querySelectorAll('.biome-demo').forEach((demo, index) => {
                if (index === currentBiomeIndex) {
                    demo.style.borderColor = '#ffff00';
                    demo.style.boxShadow = '0 0 20px #ffff00';
                } else {
                    demo.style.borderColor = '#00ff00';
                    demo.style.boxShadow = 'none';
                }
            });
            
            console.log(`Switched to biome: ${biome.name} (floor ${biome.floor})`);
        };

        // Auto-start demo
        window.addEventListener('load', () => {
            console.log('Background demo loaded');
            setTimeout(startDemo, 1000);
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Комплексний аналіз Idle Roguelike Adventure</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .test-section {
            border: 1px solid #00ff00;
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 3px;
        }
        .success { background: #003300; color: #00ff00; }
        .error { background: #330000; color: #ff0000; }
        .warning { background: #333300; color: #ffff00; }
        .info { background: #000033; color: #0066ff; }
        button {
            background: #003300;
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 10px 20px;
            cursor: pointer;
            margin: 5px;
            border-radius: 3px;
        }
        button:hover { background: #006600; }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #333;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ff00, #ffff00);
            width: 0%;
            transition: width 0.3s ease;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #222;
            border: 1px solid #444;
            padding: 15px;
            border-radius: 5px;
        }
        pre {
            background: #000;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔍 Комплексний аналіз Idle Roguelike Adventure</h1>
    
    <div class="test-section">
        <h2>1. Системний аналіз коду та стабільності</h2>
        <button onclick="runSystemAnalysis()">Запустити системний аналіз</button>
        <div class="progress-bar">
            <div class="progress-fill" id="systemProgress"></div>
        </div>
        <div id="systemResults"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Аналіз системи магазинів</h2>
        <button onclick="runShopAnalysis()">Аналізувати магазини</button>
        <div class="progress-bar">
            <div class="progress-fill" id="shopProgress"></div>
        </div>
        <div id="shopResults"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Система навичок та прогресії</h2>
        <button onclick="runSkillAnalysis()">Аналізувати навички</button>
        <div class="progress-bar">
            <div class="progress-fill" id="skillProgress"></div>
        </div>
        <div id="skillResults"></div>
    </div>
    
    <div class="test-section">
        <h2>📊 Загальна статистика</h2>
        <div class="stats-grid" id="overallStats">
            <!-- Статистика буде додана динамічно -->
        </div>
    </div>

    <script type="module">
        let testResults = {
            system: { passed: 0, failed: 0, warnings: 0 },
            shop: { passed: 0, failed: 0, warnings: 0 },
            skill: { passed: 0, failed: 0, warnings: 0 }
        };

        function updateProgress(section, percent) {
            document.getElementById(section + 'Progress').style.width = percent + '%';
        }

        function addResult(section, message, type = 'info') {
            const container = document.getElementById(section + 'Results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
            
            // Update counters
            if (type === 'success') testResults[section].passed++;
            else if (type === 'error') testResults[section].failed++;
            else if (type === 'warning') testResults[section].warnings++;
            
            updateOverallStats();
        }

        function updateOverallStats() {
            const container = document.getElementById('overallStats');
            const total = Object.values(testResults).reduce((sum, cat) => 
                sum + cat.passed + cat.failed + cat.warnings, 0);
            
            container.innerHTML = `
                <div class="stat-card">
                    <h3>Загальна статистика</h3>
                    <div>Всього тестів: ${total}</div>
                    <div style="color: #00ff00;">Пройдено: ${Object.values(testResults).reduce((sum, cat) => sum + cat.passed, 0)}</div>
                    <div style="color: #ff0000;">Помилок: ${Object.values(testResults).reduce((sum, cat) => sum + cat.failed, 0)}</div>
                    <div style="color: #ffff00;">Попереджень: ${Object.values(testResults).reduce((sum, cat) => sum + cat.warnings, 0)}</div>
                </div>
                <div class="stat-card">
                    <h3>За категоріями</h3>
                    <div>Система: ✅${testResults.system.passed} ❌${testResults.system.failed} ⚠️${testResults.system.warnings}</div>
                    <div>Магазини: ✅${testResults.shop.passed} ❌${testResults.shop.failed} ⚠️${testResults.shop.warnings}</div>
                    <div>Навички: ✅${testResults.skill.passed} ❌${testResults.skill.failed} ⚠️${testResults.skill.warnings}</div>
                </div>
            `;
        }

        // 1. Системний аналіз
        window.runSystemAnalysis = async function() {
            const container = document.getElementById('systemResults');
            container.innerHTML = '';
            updateProgress('system', 0);
            
            addResult('system', '🔍 Початок системного аналізу...', 'info');
            
            // Тест 1: Перевірка модулів
            try {
                updateProgress('system', 10);
                const modules = [
                    { name: 'GameState', path: './js/gameState.js' },
                    { name: 'ContentGenerator', path: './js/contentGenerator.js' },
                    { name: 'AudioManager', path: './js/audio.js' },
                    { name: 'Renderer', path: './js/renderer.js' },
                    { name: 'UIManager', path: './js/ui.js' },
                    { name: 'SkillSystem', path: './js/skillSystem.js' },
                    { name: 'ShopSystem', path: './js/shopSystem.js' },
                    { name: 'EquipmentSystem', path: './js/equipmentSystem.js' }
                ];
                
                for (let i = 0; i < modules.length; i++) {
                    const module = modules[i];
                    try {
                        const imported = await import(module.path);
                        if (imported[module.name]) {
                            addResult('system', `✅ Модуль ${module.name} завантажено успішно`, 'success');
                        } else {
                            addResult('system', `❌ Модуль ${module.name} не експортує клас`, 'error');
                        }
                    } catch (error) {
                        addResult('system', `❌ Помилка завантаження ${module.name}: ${error.message}`, 'error');
                    }
                    updateProgress('system', 10 + (i + 1) * 10);
                }
                
                // Тест 2: Ініціалізація об'єктів
                updateProgress('system', 60);
                const { GameState } = await import('./js/gameState.js');
                const gameState = new GameState();
                addResult('system', '✅ GameState ініціалізовано', 'success');
                
                const { ContentGenerator } = await import('./js/contentGenerator.js');
                const generator = new ContentGenerator();
                addResult('system', '✅ ContentGenerator ініціалізовано', 'success');
                
                // Тест 3: Генерація контенту
                updateProgress('system', 70);
                const enemy = generator.generateEnemy(1, 1);
                if (enemy && enemy.name && enemy.hp > 0) {
                    addResult('system', `✅ Генерація ворогів працює: ${enemy.name}`, 'success');
                } else {
                    addResult('system', '❌ Помилка генерації ворогів', 'error');
                }
                
                const item = generator.generateItem(1);
                if (item && item.name && item.type) {
                    addResult('system', `✅ Генерація предметів працює: ${item.name}`, 'success');
                } else {
                    addResult('system', '❌ Помилка генерації предметів', 'error');
                }
                
                // Тест 4: Серіалізація
                updateProgress('system', 80);
                const serialized = gameState.serialize();
                if (serialized && serialized.player && serialized.currentFloor) {
                    addResult('system', '✅ Серіалізація працює', 'success');
                } else {
                    addResult('system', '❌ Помилка серіалізації', 'error');
                }
                
                // Тест 5: localStorage
                updateProgress('system', 90);
                try {
                    localStorage.setItem('test_key', JSON.stringify({test: 'data'}));
                    const retrieved = JSON.parse(localStorage.getItem('test_key'));
                    localStorage.removeItem('test_key');
                    if (retrieved && retrieved.test === 'data') {
                        addResult('system', '✅ localStorage працює', 'success');
                    } else {
                        addResult('system', '❌ Помилка localStorage', 'error');
                    }
                } catch (error) {
                    addResult('system', `❌ localStorage недоступний: ${error.message}`, 'error');
                }
                
                updateProgress('system', 100);
                addResult('system', '🎉 Системний аналіз завершено', 'success');
                
            } catch (error) {
                addResult('system', `❌ Критична помилка: ${error.message}`, 'error');
                updateProgress('system', 100);
            }
        };

        // 2. Аналіз магазинів
        window.runShopAnalysis = async function() {
            const container = document.getElementById('shopResults');
            container.innerHTML = '';
            updateProgress('shop', 0);
            
            addResult('shop', '🏪 Початок аналізу магазинів...', 'info');
            
            try {
                const { ShopSystem } = await import('./js/shopSystem.js');
                const { GameState } = await import('./js/gameState.js');
                const { ContentGenerator } = await import('./js/contentGenerator.js');
                
                updateProgress('shop', 20);
                
                const gameState = new GameState();
                const generator = new ContentGenerator();
                const shopSystem = new ShopSystem(gameState, generator);
                
                addResult('shop', '✅ Система магазинів ініціалізована', 'success');
                
                // Тест магазинів
                const shopTypes = ['weaponsmith', 'armorsmith', 'magicVendor', 'generalStore', 'blackMarket'];
                
                for (let i = 0; i < shopTypes.length; i++) {
                    const shopType = shopTypes[i];
                    updateProgress('shop', 20 + (i + 1) * 15);
                    
                    const shopData = shopSystem.getShopData(shopType);
                    
                    if (shopData.locked && shopData.unlockLevel) {
                        addResult('shop', `⚠️ Магазин ${shopData.name} заблокований до рівня ${shopData.unlockLevel}`, 'warning');
                    } else if (shopData.items && shopData.items.length > 0) {
                        addResult('shop', `✅ Магазин ${shopData.name}: ${shopData.items.length} предметів`, 'success');
                        
                        // Перевірка цін
                        const item = shopData.items[0];
                        if (item.shopPrice && item.shopPrice > 0) {
                            addResult('shop', `✅ Ціни розраховуються: ${item.name} - ${item.shopPrice} золота`, 'success');
                        } else {
                            addResult('shop', `❌ Помилка розрахунку цін в ${shopData.name}`, 'error');
                        }
                        
                        // Перевірка назв українською
                        if (item.name && /[а-яіїєґ]/i.test(item.name)) {
                            addResult('shop', `✅ Назви українською: ${item.name}`, 'success');
                        } else {
                            addResult('shop', `⚠️ Можлива проблема з локалізацією: ${item.name}`, 'warning');
                        }
                    } else {
                        addResult('shop', `❌ Магазин ${shopData.name} порожній`, 'error');
                    }
                }
                
                updateProgress('shop', 100);
                addResult('shop', '🎉 Аналіз магазинів завершено', 'success');
                
            } catch (error) {
                addResult('shop', `❌ Критична помилка: ${error.message}`, 'error');
                updateProgress('shop', 100);
            }
        };

        // 3. Аналіз навичок
        window.runSkillAnalysis = async function() {
            const container = document.getElementById('skillResults');
            container.innerHTML = '';
            updateProgress('skill', 0);
            
            addResult('skill', '⚔️ Початок аналізу навичок...', 'info');
            
            try {
                const { SkillSystem } = await import('./js/skillSystem.js');
                const { GameState } = await import('./js/gameState.js');
                
                updateProgress('skill', 20);
                
                const gameState = new GameState();
                const skillSystem = new SkillSystem(gameState);
                
                addResult('skill', '✅ Система навичок ініціалізована', 'success');
                
                // Перевірка навичок
                const skillCount = Object.keys(skillSystem.skills).length;
                if (skillCount >= 8) {
                    addResult('skill', `✅ Кількість навичок: ${skillCount}`, 'success');
                } else {
                    addResult('skill', `⚠️ Недостатньо навичок: ${skillCount} (очікувалось 8+)`, 'warning');
                }
                
                updateProgress('skill', 40);
                
                // Перевірка дерев навичок
                const treeCount = Object.keys(skillSystem.skillTrees).length;
                if (treeCount >= 8) {
                    addResult('skill', `✅ Дерева навичок: ${treeCount}`, 'success');
                } else {
                    addResult('skill', `⚠️ Недостатньо дерев навичок: ${treeCount}`, 'warning');
                }
                
                updateProgress('skill', 60);
                
                // Тест нарахування досвіду
                const initialExp = skillSystem.skills.combat.experience;
                skillSystem.gainSkillExperience('combat', 50);
                const newExp = skillSystem.skills.combat.experience;
                
                if (newExp > initialExp) {
                    addResult('skill', `✅ Нарахування досвіду працює: +${newExp - initialExp}`, 'success');
                } else {
                    addResult('skill', '❌ Помилка нарахування досвіду', 'error');
                }
                
                updateProgress('skill', 80);
                
                // Тест очок навичок
                skillSystem.skillPoints = 10;
                const availableNodes = skillSystem.getAvailableSkillNodes('combat');
                if (availableNodes && availableNodes.length > 0) {
                    addResult('skill', `✅ Доступні вузли навичок: ${availableNodes.length}`, 'success');
                } else {
                    addResult('skill', '❌ Немає доступних вузлів навичок', 'error');
                }
                
                updateProgress('skill', 100);
                addResult('skill', '🎉 Аналіз навичок завершено', 'success');
                
            } catch (error) {
                addResult('skill', `❌ Критична помилка: ${error.message}`, 'error');
                updateProgress('skill', 100);
            }
        };

        // Ініціалізація
        updateOverallStats();
    </script>
</body>
</html>

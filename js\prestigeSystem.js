// Prestige system for game resets with bonuses
export class PrestigeSystem {
    constructor(gameState) {
        this.gameState = gameState;
        
        // Prestige data
        this.prestigeLevel = 0;
        this.prestigePoints = 0;
        this.totalPrestigePoints = 0;
        this.lifetimeStats = {
            totalLevelsGained: 0,
            totalFloorsCleared: 0,
            totalEnemiesKilled: 0,
            totalGoldEarned: 0,
            totalItemsFound: 0,
            totalPlayTime: 0,
            totalResets: 0
        };
        
        // Prestige bonuses
        this.bonuses = {
            expMultiplier: 1.0,
            goldMultiplier: 1.0,
            attackBonus: 0,
            defenseBonus: 0,
            healthBonus: 0,
            startingLevel: 1,
            skillExpMultiplier: 1.0,
            dropRateBonus: 0,
            criticalChanceBonus: 0,
            autoUnlocks: []
        };
        
        // Prestige upgrades
        this.upgrades = {
            expBoost: {
                name: 'Досвід +',
                description: 'Збільшує отримання досвіду на 20%',
                cost: 1,
                maxLevel: 10,
                currentLevel: 0,
                effect: (level) => ({ expMultiplier: 1 + level * 0.2 })
            },
            goldBoost: {
                name: 'Золото +',
                description: 'Збільшує отримання золота на 25%',
                cost: 1,
                maxLevel: 8,
                currentLevel: 0,
                effect: (level) => ({ goldMultiplier: 1 + level * 0.25 })
            },
            combatBoost: {
                name: 'Бойова міць',
                description: 'Збільшує атаку та захист на 5',
                cost: 2,
                maxLevel: 5,
                currentLevel: 0,
                effect: (level) => ({ 
                    attackBonus: level * 5,
                    defenseBonus: level * 5
                })
            },
            healthBoost: {
                name: 'Витривалість',
                description: 'Збільшує максимальне здоров\'я на 50',
                cost: 2,
                maxLevel: 6,
                currentLevel: 0,
                effect: (level) => ({ healthBonus: level * 50 })
            },
            startingLevel: {
                name: 'Досвідчений старт',
                description: 'Починати з вищого рівня',
                cost: 5,
                maxLevel: 3,
                currentLevel: 0,
                effect: (level) => ({ startingLevel: 1 + level * 2 })
            },
            skillBoost: {
                name: 'Майстерність',
                description: 'Збільшує отримання досвіду навичок на 50%',
                cost: 3,
                maxLevel: 4,
                currentLevel: 0,
                effect: (level) => ({ skillExpMultiplier: 1 + level * 0.5 })
            },
            luckBoost: {
                name: 'Удача',
                description: 'Збільшує шанс випадання предметів на 10%',
                cost: 4,
                maxLevel: 3,
                currentLevel: 0,
                effect: (level) => ({ dropRateBonus: level * 0.1 })
            },
            criticalBoost: {
                name: 'Точність',
                description: 'Збільшує шанс критичного удару на 5%',
                cost: 6,
                maxLevel: 2,
                currentLevel: 0,
                effect: (level) => ({ criticalChanceBonus: level * 0.05 })
            },
            autoFight: {
                name: 'Авто-бій',
                description: 'Автоматично вмикає авто-бій при старті',
                cost: 10,
                maxLevel: 1,
                currentLevel: 0,
                effect: (level) => ({ 
                    autoUnlocks: level > 0 ? ['autoFight'] : []
                })
            },
            autoExplore: {
                name: 'Авто-дослідження',
                description: 'Автоматично вмикає авто-дослідження при старті',
                cost: 15,
                maxLevel: 1,
                currentLevel: 0,
                effect: (level) => ({ 
                    autoUnlocks: level > 0 ? ['autoExplore'] : []
                })
            }
        };
        
        // Prestige milestones
        this.milestones = {
            firstPrestige: {
                name: 'Перше відродження',
                description: 'Виконайте перше відродження',
                completed: false,
                reward: { prestigePoints: 5 }
            },
            level50: {
                name: 'Рівень 50',
                description: 'Досягніть 50 рівня в одному прогоні',
                completed: false,
                reward: { prestigePoints: 3 }
            },
            floor25: {
                name: 'Глибинний дослідник',
                description: 'Досягніть 25 поверху',
                completed: false,
                reward: { prestigePoints: 4 }
            },
            gold10k: {
                name: 'Багатій',
                description: 'Накопичіть 10,000 золота за один прогон',
                completed: false,
                reward: { prestigePoints: 2 }
            },
            speedRun: {
                name: 'Швидкісний прогон',
                description: 'Досягніть 10 поверху за 30 хвилин',
                completed: false,
                reward: { prestigePoints: 6 }
            }
        };
    }
    
    // Check if prestige is available
    canPrestige() {
        return this.gameState.currentFloor >= 10 || this.gameState.player.level >= 25;
    }
    
    // Calculate prestige points for current run
    calculatePrestigePoints() {
        const player = this.gameState.player;
        const floor = this.gameState.currentFloor;
        
        let points = 0;
        
        // Points from level (1 point per 5 levels above 20)
        if (player.level > 20) {
            points += Math.floor((player.level - 20) / 5);
        }
        
        // Points from floor (1 point per 2 floors above 10)
        if (floor > 10) {
            points += Math.floor((floor - 10) / 2);
        }
        
        // Bonus points for achievements
        if (player.level >= 50) points += 5;
        if (floor >= 25) points += 8;
        if (this.gameState.player.gold >= 10000) points += 3;
        
        return Math.max(1, points); // Minimum 1 point
    }
    
    // Perform prestige reset
    performPrestige() {
        if (!this.canPrestige()) return false;
        
        // Calculate and award prestige points
        const pointsGained = this.calculatePrestigePoints();
        this.prestigePoints += pointsGained;
        this.totalPrestigePoints += pointsGained;
        this.prestigeLevel++;
        
        // Update lifetime stats
        this.updateLifetimeStats();
        
        // Check milestones
        this.checkMilestones();
        
        // Reset game state
        this.resetGameState();
        
        // Apply prestige bonuses
        this.applyPrestigeBonuses();
        
        this.gameState.addLogEntry(`🌟 ПРЕСТИЖ! Отримано ${pointsGained} очок престижу!`, 'prestige');
        this.gameState.addLogEntry(`Загальний рівень престижу: ${this.prestigeLevel}`, 'prestige');
        
        return true;
    }
    
    updateLifetimeStats() {
        const stats = this.gameState.stats;
        const player = this.gameState.player;
        
        this.lifetimeStats.totalLevelsGained += player.level;
        this.lifetimeStats.totalFloorsCleared += this.gameState.currentFloor;
        this.lifetimeStats.totalEnemiesKilled += stats.enemiesKilled;
        this.lifetimeStats.totalGoldEarned += stats.goldEarned;
        this.lifetimeStats.totalItemsFound += stats.itemsFound;
        this.lifetimeStats.totalPlayTime += stats.totalPlayTime;
        this.lifetimeStats.totalResets++;
    }
    
    resetGameState() {
        // Reset player to starting state
        this.gameState.player = {
            name: 'Герой',
            level: this.bonuses.startingLevel,
            hp: 100 + this.bonuses.healthBonus,
            maxHp: 100 + this.bonuses.healthBonus,
            attack: 10 + this.bonuses.attackBonus,
            defense: 5 + this.bonuses.defenseBonus,
            experience: 0,
            experienceToNext: 100,
            gold: 0,
            inventory: [],
            equipment: {
                weapon: null,
                armor: null,
                accessory: null
            }
        };
        
        // Reset location
        this.gameState.currentFloor = 1;
        this.gameState.currentRoom = 1;
        this.gameState.updateLocation();
        
        // Reset combat
        this.gameState.currentEnemy = null;
        this.gameState.isInCombat = false;
        
        // Reset stats (but keep lifetime stats)
        this.gameState.stats = {
            totalPlayTime: 0,
            enemiesKilled: 0,
            floorsCleared: 0,
            goldEarned: 0,
            itemsFound: 0,
            deathCount: 0
        };
        
        // Apply auto unlocks
        this.bonuses.autoUnlocks.forEach(unlock => {
            switch (unlock) {
                case 'autoFight':
                    this.gameState.autoFight = true;
                    break;
                case 'autoExplore':
                    this.gameState.autoProgress = true;
                    break;
            }
        });
    }
    
    // Purchase prestige upgrade
    purchaseUpgrade(upgradeKey) {
        const upgrade = this.upgrades[upgradeKey];
        if (!upgrade) return false;
        
        if (upgrade.currentLevel >= upgrade.maxLevel) return false;
        
        const cost = this.getUpgradeCost(upgradeKey);
        if (this.prestigePoints < cost) return false;
        
        this.prestigePoints -= cost;
        upgrade.currentLevel++;
        
        // Recalculate bonuses
        this.calculateBonuses();
        
        this.gameState.addLogEntry(`Куплено: ${upgrade.name} (Рівень ${upgrade.currentLevel})`, 'prestige');
        
        return true;
    }
    
    getUpgradeCost(upgradeKey) {
        const upgrade = this.upgrades[upgradeKey];
        return upgrade.cost * (upgrade.currentLevel + 1);
    }
    
    calculateBonuses() {
        // Reset bonuses
        this.bonuses = {
            expMultiplier: 1.0,
            goldMultiplier: 1.0,
            attackBonus: 0,
            defenseBonus: 0,
            healthBonus: 0,
            startingLevel: 1,
            skillExpMultiplier: 1.0,
            dropRateBonus: 0,
            criticalChanceBonus: 0,
            autoUnlocks: []
        };
        
        // Apply all upgrade effects
        Object.values(this.upgrades).forEach(upgrade => {
            if (upgrade.currentLevel > 0) {
                const effect = upgrade.effect(upgrade.currentLevel);
                Object.keys(effect).forEach(key => {
                    if (key === 'autoUnlocks') {
                        this.bonuses.autoUnlocks = this.bonuses.autoUnlocks.concat(effect[key]);
                    } else if (key.includes('Multiplier')) {
                        this.bonuses[key] *= effect[key];
                    } else {
                        this.bonuses[key] += effect[key];
                    }
                });
            }
        });
    }
    
    applyPrestigeBonuses() {
        this.calculateBonuses();
        
        // Apply bonuses to current game state
        const player = this.gameState.player;
        player.level = this.bonuses.startingLevel;
        player.maxHp = 100 + this.bonuses.healthBonus;
        player.hp = player.maxHp;
        player.attack = 10 + this.bonuses.attackBonus;
        player.defense = 5 + this.bonuses.defenseBonus;
    }
    
    checkMilestones() {
        const player = this.gameState.player;
        const floor = this.gameState.currentFloor;
        const stats = this.gameState.stats;
        
        Object.keys(this.milestones).forEach(key => {
            const milestone = this.milestones[key];
            if (milestone.completed) return;
            
            let completed = false;
            
            switch (key) {
                case 'firstPrestige':
                    completed = this.prestigeLevel >= 1;
                    break;
                case 'level50':
                    completed = player.level >= 50;
                    break;
                case 'floor25':
                    completed = floor >= 25;
                    break;
                case 'gold10k':
                    completed = player.gold >= 10000;
                    break;
                case 'speedRun':
                    completed = floor >= 10 && stats.totalPlayTime <= 1800000; // 30 minutes
                    break;
            }
            
            if (completed) {
                milestone.completed = true;
                this.prestigePoints += milestone.reward.prestigePoints;
                this.gameState.addLogEntry(`🏆 Віха досягнута: ${milestone.name}!`, 'prestige');
                this.gameState.addLogEntry(`+${milestone.reward.prestigePoints} очок престижу!`, 'prestige');
            }
        });
    }
    
    // Get prestige info for UI
    getPrestigeInfo() {
        return {
            level: this.prestigeLevel,
            points: this.prestigePoints,
            totalPoints: this.totalPrestigePoints,
            canPrestige: this.canPrestige(),
            pointsFromReset: this.calculatePrestigePoints(),
            bonuses: this.bonuses,
            lifetimeStats: this.lifetimeStats
        };
    }
    
    // Serialization
    serialize() {
        return {
            prestigeLevel: this.prestigeLevel,
            prestigePoints: this.prestigePoints,
            totalPrestigePoints: this.totalPrestigePoints,
            lifetimeStats: this.lifetimeStats,
            upgrades: Object.fromEntries(
                Object.entries(this.upgrades).map(([key, upgrade]) => [
                    key, 
                    { currentLevel: upgrade.currentLevel }
                ])
            ),
            milestones: this.milestones
        };
    }
    
    deserialize(data) {
        if (data.prestigeLevel !== undefined) this.prestigeLevel = data.prestigeLevel;
        if (data.prestigePoints !== undefined) this.prestigePoints = data.prestigePoints;
        if (data.totalPrestigePoints !== undefined) this.totalPrestigePoints = data.totalPrestigePoints;
        if (data.lifetimeStats) this.lifetimeStats = { ...this.lifetimeStats, ...data.lifetimeStats };
        if (data.milestones) this.milestones = { ...this.milestones, ...data.milestones };
        
        if (data.upgrades) {
            Object.entries(data.upgrades).forEach(([key, upgradeData]) => {
                if (this.upgrades[key]) {
                    this.upgrades[key].currentLevel = upgradeData.currentLevel || 0;
                }
            });
        }
        
        this.calculateBonuses();
    }
}

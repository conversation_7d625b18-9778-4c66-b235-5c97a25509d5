// Equipment system for character paper doll and equipment management
export class EquipmentSystem {
    constructor(gameState, renderer) {
        this.gameState = gameState;
        this.renderer = renderer;
        
        // Equipment slots
        this.equipmentSlots = {
            helmet: { name: 'Шолом', x: 150, y: 50, width: 60, height: 60 },
            armor: { name: 'Броня', x: 150, y: 120, width: 60, height: 80 },
            weapon: { name: '<PERSON><PERSON>р<PERSON><PERSON>', x: 80, y: 120, width: 60, height: 80 },
            shield: { name: 'Щит', x: 220, y: 120, width: 60, height: 80 },
            gloves: { name: 'Рукавиці', x: 80, y: 210, width: 50, height: 50 },
            boots: { name: 'Чоботи', x: 220, y: 210, width: 50, height: 50 },
            ring1: { name: 'Кільце 1', x: 50, y: 280, width: 40, height: 40 },
            ring2: { name: 'Кільце 2', x: 270, y: 280, width: 40, height: 40 },
            amulet: { name: 'Амулет', x: 150, y: 20, width: 60, height: 25 }
        };
        
        // Currently equipped items
        this.equippedItems = {
            helmet: null,
            armor: null,
            weapon: null,
            shield: null,
            gloves: null,
            boots: null,
            ring1: null,
            ring2: null,
            amulet: null
        };
        
        // Drag and drop state
        this.dragState = {
            isDragging: false,
            draggedItem: null,
            draggedFromSlot: null,
            draggedFromInventory: false,
            mouseX: 0,
            mouseY: 0
        };
        
        // Equipment comparison tooltip
        this.tooltip = {
            visible: false,
            item: null,
            x: 0,
            y: 0,
            comparison: null
        };
        
        // UI elements
        this.canvas = null;
        this.ctx = null;
        this.isVisible = false;
        
        this.initializeEquipment();
    }
    
    initializeEquipment() {
        // Load equipped items from game state
        if (this.gameState.player.equipment) {
            Object.keys(this.equippedItems).forEach(slot => {
                if (this.gameState.player.equipment[slot]) {
                    this.equippedItems[slot] = this.gameState.player.equipment[slot];
                }
            });
        }
    }
    
    // Create equipment interface
    createEquipmentInterface() {
        const equipmentPanel = document.createElement('div');
        equipmentPanel.id = 'equipmentPanel';
        equipmentPanel.className = 'equipment-panel';
        equipmentPanel.style.cssText = `
            position: fixed;
            top: 50px;
            right: 20px;
            width: 360px;
            height: 400px;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #00ff00;
            border-radius: 10px;
            padding: 10px;
            display: none;
            z-index: 1000;
            color: #ffffff;
            font-family: 'Courier New', monospace;
        `;
        
        // Title
        const title = document.createElement('h3');
        title.textContent = 'Екіпірування';
        title.style.cssText = `
            margin: 0 0 10px 0;
            text-align: center;
            color: #00ff00;
        `;
        equipmentPanel.appendChild(title);
        
        // Equipment canvas
        const canvas = document.createElement('canvas');
        canvas.width = 340;
        canvas.height = 340;
        canvas.style.cssText = `
            border: 1px solid #333;
            background: #111;
            cursor: pointer;
        `;
        
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        equipmentPanel.appendChild(canvas);
        
        // Close button
        const closeBtn = document.createElement('button');
        closeBtn.textContent = '×';
        closeBtn.style.cssText = `
            position: absolute;
            top: 5px;
            right: 10px;
            background: none;
            border: none;
            color: #ff0000;
            font-size: 20px;
            cursor: pointer;
        `;
        closeBtn.onclick = () => this.hideEquipmentInterface();
        equipmentPanel.appendChild(closeBtn);
        
        // Add event listeners
        this.setupEventListeners();
        
        document.body.appendChild(equipmentPanel);
        return equipmentPanel;
    }
    
    setupEventListeners() {
        if (!this.canvas) return;
        
        // Mouse events for drag and drop
        this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        this.canvas.addEventListener('mouseleave', (e) => this.handleMouseLeave(e));
        
        // Tooltip events
        this.canvas.addEventListener('mouseover', (e) => this.handleMouseOver(e));
        this.canvas.addEventListener('mouseout', (e) => this.handleMouseOut(e));
    }
    
    // Show equipment interface
    showEquipmentInterface() {
        let panel = document.getElementById('equipmentPanel');
        if (!panel) {
            panel = this.createEquipmentInterface();
        }
        
        panel.style.display = 'block';
        this.isVisible = true;
        this.render();
    }
    
    // Hide equipment interface
    hideEquipmentInterface() {
        const panel = document.getElementById('equipmentPanel');
        if (panel) {
            panel.style.display = 'none';
        }
        this.isVisible = false;
        this.tooltip.visible = false;
    }
    
    // Toggle equipment interface
    toggleEquipmentInterface() {
        if (this.isVisible) {
            this.hideEquipmentInterface();
        } else {
            this.showEquipmentInterface();
        }
    }
    
    // Render equipment interface
    render() {
        if (!this.ctx || !this.isVisible) return;
        
        // Clear canvas
        this.ctx.fillStyle = '#111111';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw character silhouette
        this.drawCharacterSilhouette();
        
        // Draw equipment slots
        this.drawEquipmentSlots();
        
        // Draw equipped items
        this.drawEquippedItems();
        
        // Draw dragged item
        if (this.dragState.isDragging) {
            this.drawDraggedItem();
        }
        
        // Draw tooltip
        if (this.tooltip.visible) {
            this.drawTooltip();
        }
    }
    
    drawCharacterSilhouette() {
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        
        // Draw character outline
        this.ctx.strokeStyle = '#333333';
        this.ctx.lineWidth = 2;
        
        // Head
        this.ctx.beginPath();
        this.ctx.arc(centerX, 80, 25, 0, Math.PI * 2);
        this.ctx.stroke();
        
        // Body
        this.ctx.beginPath();
        this.ctx.rect(centerX - 30, 105, 60, 100);
        this.ctx.stroke();
        
        // Arms
        this.ctx.beginPath();
        this.ctx.rect(centerX - 60, 120, 25, 80);
        this.ctx.rect(centerX + 35, 120, 25, 80);
        this.ctx.stroke();
        
        // Legs
        this.ctx.beginPath();
        this.ctx.rect(centerX - 25, 205, 20, 80);
        this.ctx.rect(centerX + 5, 205, 20, 80);
        this.ctx.stroke();
    }
    
    drawEquipmentSlots() {
        Object.entries(this.equipmentSlots).forEach(([slotName, slot]) => {
            // Draw slot background
            this.ctx.fillStyle = this.equippedItems[slotName] ? '#003300' : '#330000';
            this.ctx.fillRect(slot.x, slot.y, slot.width, slot.height);
            
            // Draw slot border
            this.ctx.strokeStyle = '#666666';
            this.ctx.lineWidth = 1;
            this.ctx.strokeRect(slot.x, slot.y, slot.width, slot.height);
            
            // Draw slot label
            this.ctx.fillStyle = '#cccccc';
            this.ctx.font = '10px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(slot.name, slot.x + slot.width / 2, slot.y - 5);
        });
    }
    
    drawEquippedItems() {
        Object.entries(this.equippedItems).forEach(([slotName, item]) => {
            if (!item) return;
            
            const slot = this.equipmentSlots[slotName];
            this.drawItemInSlot(item, slot);
        });
    }
    
    drawItemInSlot(item, slot) {
        if (!item) return;
        
        // Draw item background based on rarity
        const rarityColors = {
            common: '#ffffff',
            uncommon: '#00ff00',
            rare: '#0066ff',
            epic: '#9966ff',
            legendary: '#ff6600'
        };
        
        this.ctx.fillStyle = rarityColors[item.rarity] || '#ffffff';
        this.ctx.fillRect(slot.x + 2, slot.y + 2, slot.width - 4, slot.height - 4);
        
        // Draw item icon (simplified)
        this.ctx.fillStyle = '#000000';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(item.name.charAt(0), slot.x + slot.width / 2, slot.y + slot.height / 2 + 4);
        
        // Draw enhancement level
        if (item.enhancement > 0) {
            this.ctx.fillStyle = '#ffff00';
            this.ctx.font = '10px Arial';
            this.ctx.fillText(`+${item.enhancement}`, slot.x + slot.width - 10, slot.y + 12);
        }
    }
    
    drawDraggedItem() {
        if (!this.dragState.draggedItem) return;
        
        const item = this.dragState.draggedItem;
        const x = this.dragState.mouseX - 25;
        const y = this.dragState.mouseY - 25;
        
        // Draw semi-transparent item
        this.ctx.globalAlpha = 0.7;
        this.ctx.fillStyle = '#ffff00';
        this.ctx.fillRect(x, y, 50, 50);
        this.ctx.strokeStyle = '#ffffff';
        this.ctx.strokeRect(x, y, 50, 50);
        
        this.ctx.fillStyle = '#000000';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(item.name.charAt(0), x + 25, y + 30);
        
        this.ctx.globalAlpha = 1.0;
    }
    
    drawTooltip() {
        if (!this.tooltip.item) return;
        
        const item = this.tooltip.item;
        const x = this.tooltip.x;
        const y = this.tooltip.y;
        
        // Calculate tooltip size
        const lines = this.getTooltipLines(item);
        const lineHeight = 16;
        const padding = 8;
        const width = Math.max(...lines.map(line => this.ctx.measureText(line).width)) + padding * 2;
        const height = lines.length * lineHeight + padding * 2;
        
        // Draw tooltip background
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.9)';
        this.ctx.fillRect(x, y, width, height);
        
        // Draw tooltip border
        this.ctx.strokeStyle = '#ffff00';
        this.ctx.lineWidth = 1;
        this.ctx.strokeRect(x, y, width, height);
        
        // Draw tooltip text
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'left';
        
        lines.forEach((line, index) => {
            this.ctx.fillText(line, x + padding, y + padding + (index + 1) * lineHeight);
        });
    }
    
    getTooltipLines(item) {
        const lines = [];
        lines.push(item.name);
        lines.push(`Тип: ${item.type}`);
        lines.push(`Рідкість: ${item.rarity}`);
        
        if (item.enhancement > 0) {
            lines.push(`Покращення: +${item.enhancement}`);
        }
        
        // Add stats
        if (item.stats) {
            Object.entries(item.stats).forEach(([stat, value]) => {
                lines.push(`${stat}: ${value > 0 ? '+' : ''}${value}`);
            });
        }
        
        return lines;
    }

    // Mouse event handlers
    handleMouseDown(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        // Check if clicking on equipped item
        const clickedSlot = this.getSlotAtPosition(x, y);
        if (clickedSlot && this.equippedItems[clickedSlot]) {
            this.startDrag(this.equippedItems[clickedSlot], clickedSlot, false);
        }
    }

    handleMouseMove(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        this.dragState.mouseX = x;
        this.dragState.mouseY = y;

        if (this.dragState.isDragging) {
            this.render();
        } else {
            // Handle tooltip
            const hoveredSlot = this.getSlotAtPosition(x, y);
            if (hoveredSlot && this.equippedItems[hoveredSlot]) {
                this.showTooltip(this.equippedItems[hoveredSlot], x, y);
            } else {
                this.hideTooltip();
            }
        }
    }

    handleMouseUp(e) {
        if (!this.dragState.isDragging) return;

        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        const targetSlot = this.getSlotAtPosition(x, y);

        if (targetSlot) {
            this.equipItem(this.dragState.draggedItem, targetSlot);
        }

        this.endDrag();
    }

    handleMouseLeave(e) {
        this.endDrag();
        this.hideTooltip();
    }

    handleMouseOver(e) {
        // Handle tooltip showing
    }

    handleMouseOut(e) {
        this.hideTooltip();
    }

    // Drag and drop methods
    startDrag(item, fromSlot, fromInventory) {
        this.dragState.isDragging = true;
        this.dragState.draggedItem = item;
        this.dragState.draggedFromSlot = fromSlot;
        this.dragState.draggedFromInventory = fromInventory;

        // Remove item from current slot if dragging from equipment
        if (!fromInventory && fromSlot) {
            this.equippedItems[fromSlot] = null;
        }
    }

    endDrag() {
        // If drag was cancelled, return item to original slot
        if (this.dragState.isDragging && !this.dragState.draggedFromInventory && this.dragState.draggedFromSlot) {
            this.equippedItems[this.dragState.draggedFromSlot] = this.dragState.draggedItem;
        }

        this.dragState.isDragging = false;
        this.dragState.draggedItem = null;
        this.dragState.draggedFromSlot = null;
        this.dragState.draggedFromInventory = false;

        this.render();
    }

    // Equipment methods
    equipItem(item, slot) {
        if (!this.canEquipItemInSlot(item, slot)) {
            return false;
        }

        // Unequip current item if any
        const currentItem = this.equippedItems[slot];
        if (currentItem) {
            this.unequipItem(slot);
        }

        // Equip new item
        this.equippedItems[slot] = item;

        // Update game state
        if (!this.gameState.player.equipment) {
            this.gameState.player.equipment = {};
        }
        this.gameState.player.equipment[slot] = item;

        // Remove from inventory if dragged from there
        if (this.dragState.draggedFromInventory) {
            this.removeItemFromInventory(item);
        }

        // Apply item stats
        this.applyItemStats(item, true);

        this.gameState.addLogEntry(`Екіпіровано: ${item.name}`, 'equipment');
        this.gameState.needsUIUpdate = true;

        return true;
    }

    unequipItem(slot) {
        const item = this.equippedItems[slot];
        if (!item) return false;

        // Add to inventory
        this.addItemToInventory(item);

        // Remove from equipment
        this.equippedItems[slot] = null;
        if (this.gameState.player.equipment) {
            this.gameState.player.equipment[slot] = null;
        }

        // Remove item stats
        this.applyItemStats(item, false);

        this.gameState.addLogEntry(`Знято: ${item.name}`, 'equipment');
        this.gameState.needsUIUpdate = true;

        return true;
    }

    canEquipItemInSlot(item, slot) {
        // Check if item type matches slot
        const slotItemTypes = {
            helmet: ['helmet', 'hat'],
            armor: ['armor', 'robe'],
            weapon: ['sword', 'axe', 'bow', 'staff', 'dagger'],
            shield: ['shield'],
            gloves: ['gloves', 'gauntlets'],
            boots: ['boots', 'shoes'],
            ring1: ['ring'],
            ring2: ['ring'],
            amulet: ['amulet', 'necklace']
        };

        return slotItemTypes[slot] && slotItemTypes[slot].includes(item.type);
    }

    // Utility methods
    getSlotAtPosition(x, y) {
        for (const [slotName, slot] of Object.entries(this.equipmentSlots)) {
            if (x >= slot.x && x <= slot.x + slot.width &&
                y >= slot.y && y <= slot.y + slot.height) {
                return slotName;
            }
        }
        return null;
    }

    showTooltip(item, x, y) {
        this.tooltip.visible = true;
        this.tooltip.item = item;
        this.tooltip.x = Math.min(x, this.canvas.width - 200);
        this.tooltip.y = Math.max(y - 100, 0);
        this.render();
    }

    hideTooltip() {
        this.tooltip.visible = false;
        this.render();
    }

    // Integration with game systems
    addItemToInventory(item) {
        if (this.gameState.addItemToInventory) {
            this.gameState.addItemToInventory(item);
        }
    }

    removeItemFromInventory(item) {
        if (this.gameState.removeItemFromInventory) {
            this.gameState.removeItemFromInventory(item);
        }
    }

    applyItemStats(item, equip) {
        if (!item.stats) return;

        const multiplier = equip ? 1 : -1;

        Object.entries(item.stats).forEach(([stat, value]) => {
            switch (stat) {
                case 'attack':
                    this.gameState.player.attack += value * multiplier;
                    break;
                case 'defense':
                    this.gameState.player.defense += value * multiplier;
                    break;
                case 'health':
                    this.gameState.player.maxHp += value * multiplier;
                    if (equip) {
                        this.gameState.player.hp += value;
                    }
                    break;
                case 'mana':
                    this.gameState.player.maxMp += value * multiplier;
                    if (equip) {
                        this.gameState.player.mp += value;
                    }
                    break;
            }
        });

        // Ensure stats don't go below minimums
        this.gameState.player.attack = Math.max(1, this.gameState.player.attack);
        this.gameState.player.defense = Math.max(0, this.gameState.player.defense);
        this.gameState.player.maxHp = Math.max(1, this.gameState.player.maxHp);
        this.gameState.player.hp = Math.min(this.gameState.player.hp, this.gameState.player.maxHp);
    }

    // Get total equipment stats
    getTotalEquipmentStats() {
        const totalStats = {
            attack: 0,
            defense: 0,
            health: 0,
            mana: 0
        };

        Object.values(this.equippedItems).forEach(item => {
            if (item && item.stats) {
                Object.entries(item.stats).forEach(([stat, value]) => {
                    if (totalStats.hasOwnProperty(stat)) {
                        totalStats[stat] += value;
                    }
                });
            }
        });

        return totalStats;
    }

    // Serialization
    serialize() {
        return {
            equippedItems: this.equippedItems
        };
    }

    deserialize(data) {
        if (data.equippedItems) {
            this.equippedItems = { ...this.equippedItems, ...data.equippedItems };
        }
    }
}

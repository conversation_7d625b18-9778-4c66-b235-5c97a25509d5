// Save/Load system with offline progress
export class SaveManager {
    constructor(gameState) {
        this.gameState = gameState;
        this.saveKey = 'idleRoguelikeSave';
        this.settingsKey = 'idleRoguelikeSettings';
        this.lastSaveTime = Date.now();
        this.autoSaveInterval = 30000; // Auto-save every 30 seconds
        
        // Compression settings
        this.compressionEnabled = true;
        
        // Backup system
        this.maxBackups = 3;
        this.backupInterval = 300000; // Create backup every 5 minutes
        this.lastBackupTime = Date.now();
    }
    
    // Main save/load methods
    saveGame() {
        try {
            const saveData = this.createSaveData();
            const serializedData = JSON.stringify(saveData);
            
            // Compress if enabled
            const finalData = this.compressionEnabled ? 
                this.compressData(serializedData) : serializedData;
            
            localStorage.setItem(this.saveKey, finalData);
            this.lastSaveTime = Date.now();
            
            // Create backup if needed
            if (Date.now() - this.lastBackupTime > this.backupInterval) {
                this.createBackup(finalData);
            }
            
            console.log('Game saved successfully');
            return true;
        } catch (error) {
            console.error('Failed to save game:', error);
            this.gameState.addLogEntry('Помилка збереження гри!', 'error');
            return false;
        }
    }
    
    loadGame() {
        try {
            const savedData = localStorage.getItem(this.saveKey);
            if (!savedData) {
                console.log('No save data found, starting new game');
                return false;
            }
            
            // Decompress if needed
            const decompressedData = this.isCompressed(savedData) ? 
                this.decompressData(savedData) : savedData;
            
            const saveData = JSON.parse(decompressedData);
            
            // Validate save data
            if (!this.validateSaveData(saveData)) {
                console.error('Invalid save data');
                return false;
            }
            
            // Load data into game state
            this.applySaveData(saveData);
            
            // Calculate offline progress
            this.calculateOfflineProgress();
            
            console.log('Game loaded successfully');
            this.gameState.addLogEntry('Гру завантажено!', 'system');
            return true;
        } catch (error) {
            console.error('Failed to load game:', error);
            this.gameState.addLogEntry('Помилка завантаження гри!', 'error');
            return false;
        }
    }
    
    createSaveData() {
        return {
            version: '1.0.0',
            timestamp: Date.now(),
            gameState: this.gameState.serialize(),
            settings: this.getGameSettings(),
            statistics: this.getExtendedStatistics()
        };
    }
    
    applySaveData(saveData) {
        // Apply game state
        if (saveData.gameState) {
            this.gameState.deserialize(saveData.gameState);
        }
        
        // Apply settings
        if (saveData.settings) {
            this.applyGameSettings(saveData.settings);
        }
        
        // Update last save time
        if (saveData.timestamp) {
            this.lastSaveTime = saveData.timestamp;
        }
    }
    
    validateSaveData(saveData) {
        // Check required fields
        if (!saveData.version || !saveData.timestamp || !saveData.gameState) {
            return false;
        }
        
        // Check version compatibility
        if (saveData.version !== '1.0.0') {
            console.warn('Save data version mismatch, attempting migration');
            return this.migrateSaveData(saveData);
        }
        
        // Validate game state structure
        const gs = saveData.gameState;
        if (!gs.player || !gs.currentFloor || !gs.stats) {
            return false;
        }
        
        return true;
    }
    
    migrateSaveData(saveData) {
        // Handle save data migration between versions
        try {
            // Add migration logic here for future versions
            console.log('Save data migration completed');
            return true;
        } catch (error) {
            console.error('Save data migration failed:', error);
            return false;
        }
    }
    
    // Offline progress calculation
    calculateOfflineProgress() {
        const now = Date.now();
        const offlineTime = now - this.lastSaveTime;
        
        // Only calculate if offline for more than 1 minute
        if (offlineTime < 60000) return;
        
        const hours = offlineTime / (1000 * 60 * 60);
        const maxOfflineHours = 24; // Cap at 24 hours
        const actualHours = Math.min(hours, maxOfflineHours);
        
        if (actualHours > 0.1) { // More than 6 minutes
            const progress = this.simulateOfflineProgress(actualHours);
            this.showOfflineProgressReport(progress);
        }
    }
    
    simulateOfflineProgress(hours) {
        const player = this.gameState.player;
        const currentFloor = this.gameState.currentFloor;
        
        // Calculate base rates (per hour)
        const baseExpPerHour = 50 + currentFloor * 10;
        const baseGoldPerHour = 20 + currentFloor * 5;
        const baseRoomsPerHour = 12; // 5 minutes per room
        
        // Apply player level multipliers
        const levelMultiplier = 1 + (player.level - 1) * 0.1;
        
        // Calculate total offline gains
        const expGained = Math.floor(baseExpPerHour * hours * levelMultiplier);
        const goldGained = Math.floor(baseGoldPerHour * hours * levelMultiplier);
        const roomsExplored = Math.floor(baseRoomsPerHour * hours);
        const enemiesKilled = Math.floor(roomsExplored * 0.7); // 70% combat rate
        
        // Apply gains
        this.gameState.gainExperience(expGained);
        this.gameState.gainGold(goldGained);
        
        // Update statistics
        this.gameState.stats.enemiesKilled += enemiesKilled;
        this.gameState.stats.totalPlayTime += hours * 3600000; // Convert to milliseconds
        
        // Generate some random items
        const itemsFound = Math.floor(hours * 0.5); // 0.5 items per hour
        for (let i = 0; i < itemsFound; i++) {
            // Simple item generation for offline progress
            const item = {
                name: `Знайдений предмет ${i + 1}`,
                type: Math.random() < 0.5 ? 'weapon' : 'armor',
                stats: {
                    attack: Math.random() < 0.5 ? Math.floor(5 + currentFloor * 2) : 0,
                    defense: Math.random() < 0.5 ? Math.floor(3 + currentFloor * 1.5) : 0
                },
                value: Math.floor(10 + currentFloor * 3)
            };
            
            this.gameState.addItemToInventory(item);
        }
        
        return {
            timeAway: hours,
            expGained,
            goldGained,
            roomsExplored,
            enemiesKilled,
            itemsFound
        };
    }
    
    showOfflineProgressReport(progress) {
        const hours = Math.floor(progress.timeAway);
        const minutes = Math.floor((progress.timeAway - hours) * 60);
        
        let report = `Ви були відсутні ${hours}г ${minutes}хв\n`;
        report += `Досліджено кімнат: ${progress.roomsExplored}\n`;
        report += `Переможено ворогів: ${progress.enemiesKilled}\n`;
        report += `Отримано досвіду: ${progress.expGained}\n`;
        report += `Отримано золота: ${progress.goldGained}\n`;
        report += `Знайдено предметів: ${progress.itemsFound}`;
        
        // Add to game log
        this.gameState.addLogEntry('=== ОФЛАЙН ПРОГРЕС ===', 'system');
        report.split('\n').forEach(line => {
            if (line.trim()) {
                this.gameState.addLogEntry(line, 'system');
            }
        });
        this.gameState.addLogEntry('=====================', 'system');
        
        // Show popup if available
        if (typeof window !== 'undefined' && window.alert) {
            alert('Офлайн прогрес:\n' + report);
        }
    }
    
    // Settings management
    getGameSettings() {
        return {
            masterVolume: 0.5,
            musicVolume: 0.5,
            sfxVolume: 0.7,
            autoSave: true,
            showDamageNumbers: true,
            fastAnimations: false
        };
    }
    
    applyGameSettings(settings) {
        // Apply settings to game systems
        // This would integrate with audio manager, UI, etc.
        console.log('Applied game settings:', settings);
    }
    
    saveSettings(settings) {
        try {
            localStorage.setItem(this.settingsKey, JSON.stringify(settings));
            return true;
        } catch (error) {
            console.error('Failed to save settings:', error);
            return false;
        }
    }
    
    loadSettings() {
        try {
            const settings = localStorage.getItem(this.settingsKey);
            return settings ? JSON.parse(settings) : this.getGameSettings();
        } catch (error) {
            console.error('Failed to load settings:', error);
            return this.getGameSettings();
        }
    }
    
    // Backup system
    createBackup(saveData) {
        try {
            const backupKey = `${this.saveKey}_backup_${Date.now()}`;
            localStorage.setItem(backupKey, saveData);
            this.lastBackupTime = Date.now();
            
            // Clean old backups
            this.cleanOldBackups();
            
            console.log('Backup created:', backupKey);
        } catch (error) {
            console.error('Failed to create backup:', error);
        }
    }
    
    cleanOldBackups() {
        try {
            const backupKeys = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(`${this.saveKey}_backup_`)) {
                    const timestamp = parseInt(key.split('_').pop());
                    backupKeys.push({ key, timestamp });
                }
            }
            
            // Sort by timestamp (newest first)
            backupKeys.sort((a, b) => b.timestamp - a.timestamp);
            
            // Remove old backups
            for (let i = this.maxBackups; i < backupKeys.length; i++) {
                localStorage.removeItem(backupKeys[i].key);
            }
        } catch (error) {
            console.error('Failed to clean old backups:', error);
        }
    }
    
    getBackupList() {
        const backups = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(`${this.saveKey}_backup_`)) {
                const timestamp = parseInt(key.split('_').pop());
                backups.push({
                    key,
                    timestamp,
                    date: new Date(timestamp).toLocaleString()
                });
            }
        }
        return backups.sort((a, b) => b.timestamp - a.timestamp);
    }
    
    restoreFromBackup(backupKey) {
        try {
            const backupData = localStorage.getItem(backupKey);
            if (backupData) {
                localStorage.setItem(this.saveKey, backupData);
                this.loadGame();
                this.gameState.addLogEntry('Відновлено з резервної копії', 'system');
                return true;
            }
        } catch (error) {
            console.error('Failed to restore from backup:', error);
        }
        return false;
    }
    
    // Data compression (simple base64 for now)
    compressData(data) {
        try {
            return btoa(data);
        } catch (error) {
            console.warn('Compression failed, using uncompressed data');
            return data;
        }
    }
    
    decompressData(data) {
        try {
            return atob(data);
        } catch (error) {
            console.warn('Decompression failed, assuming uncompressed data');
            return data;
        }
    }
    
    isCompressed(data) {
        // Simple check - compressed data should be base64
        try {
            atob(data);
            return true;
        } catch {
            return false;
        }
    }
    
    // Extended statistics
    getExtendedStatistics() {
        return {
            ...this.gameState.stats,
            saveCount: 1,
            lastSaveTime: Date.now(),
            gameVersion: '1.0.0'
        };
    }
    
    // Utility methods
    exportSave() {
        const saveData = this.createSaveData();
        const exportString = JSON.stringify(saveData, null, 2);
        
        // Create download link
        const blob = new Blob([exportString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `idle_roguelike_save_${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }
    
    importSave(fileContent) {
        try {
            const saveData = JSON.parse(fileContent);
            if (this.validateSaveData(saveData)) {
                this.applySaveData(saveData);
                this.saveGame(); // Save the imported data
                this.gameState.addLogEntry('Збереження імпортовано!', 'system');
                return true;
            }
        } catch (error) {
            console.error('Failed to import save:', error);
        }
        return false;
    }
    
    deleteSave() {
        try {
            localStorage.removeItem(this.saveKey);
            localStorage.removeItem(this.settingsKey);
            
            // Clean all backups
            const backups = this.getBackupList();
            backups.forEach(backup => {
                localStorage.removeItem(backup.key);
            });
            
            console.log('Save data deleted');
            return true;
        } catch (error) {
            console.error('Failed to delete save:', error);
            return false;
        }
    }
}

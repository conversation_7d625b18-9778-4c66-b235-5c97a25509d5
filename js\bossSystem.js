// Boss system with special encounters
export class BossSystem {
    constructor(gameState, contentGenerator) {
        this.gameState = gameState;
        this.contentGenerator = contentGenerator;
        
        // Boss templates
        this.bossTemplates = {
            goblinKing: {
                name: 'Король Гоблінів',
                type: 'orc',
                description: 'Могутній правитель гоблінських племен',
                hpMultiplier: 5,
                attackMultiplier: 2,
                defenseMultiplier: 1.5,
                abilities: ['summonMinions', 'rage', 'heal'],
                lootMultiplier: 3,
                expMultiplier: 5,
                goldMultiplier: 4
            },
            ancientLich: {
                name: 'Древній Ліч',
                type: 'skeleton',
                description: 'Нежить, що володіє темною магією',
                hpMultiplier: 4,
                attackMultiplier: 3,
                defenseMultiplier: 1,
                abilities: ['darkMagic', 'lifeDrain', 'curse'],
                lootMultiplier: 4,
                expMultiplier: 6,
                goldMultiplier: 3
            },
            dragonLord: {
                name: 'Володар Драконів',
                type: 'orc',
                description: 'Величезний дракон з вогняним диханням',
                hpMultiplier: 8,
                attackMultiplier: 2.5,
                defenseMultiplier: 2,
                abilities: ['fireBreath', 'fly', 'roar'],
                lootMultiplier: 5,
                expMultiplier: 8,
                goldMultiplier: 6
            },
            voidHorror: {
                name: 'Жах Порожнечі',
                type: 'skeleton',
                description: 'Істота з іншого виміру',
                hpMultiplier: 6,
                attackMultiplier: 4,
                defenseMultiplier: 0.5,
                abilities: ['voidStrike', 'teleport', 'madness'],
                lootMultiplier: 6,
                expMultiplier: 10,
                goldMultiplier: 5
            },
            crystalGolem: {
                name: 'Кристалевий Голем',
                type: 'orc',
                description: 'Живий кристал неймовірної міцності',
                hpMultiplier: 10,
                attackMultiplier: 1.5,
                defenseMultiplier: 3,
                abilities: ['crystalSpikes', 'reflect', 'regenerate'],
                lootMultiplier: 4,
                expMultiplier: 7,
                goldMultiplier: 8
            }
        };
        
        // Boss abilities
        this.abilities = {
            summonMinions: {
                name: 'Призов міньйонів',
                description: 'Призиває допоміжних ворогів',
                cooldown: 5000,
                effect: this.summonMinions.bind(this)
            },
            rage: {
                name: 'Лють',
                description: 'Збільшує атаку на 50%',
                cooldown: 8000,
                effect: this.rage.bind(this)
            },
            heal: {
                name: 'Лікування',
                description: 'Відновлює 25% здоров\'я',
                cooldown: 10000,
                effect: this.heal.bind(this)
            },
            darkMagic: {
                name: 'Темна магія',
                description: 'Завдає магічної шкоди',
                cooldown: 4000,
                effect: this.darkMagic.bind(this)
            },
            lifeDrain: {
                name: 'Висмоктування життя',
                description: 'Краде здоров\'я у гравця',
                cooldown: 6000,
                effect: this.lifeDrain.bind(this)
            },
            curse: {
                name: 'Прокляття',
                description: 'Зменшує характеристики гравця',
                cooldown: 12000,
                effect: this.curse.bind(this)
            },
            fireBreath: {
                name: 'Вогняне дихання',
                description: 'Потужна вогняна атака',
                cooldown: 7000,
                effect: this.fireBreath.bind(this)
            },
            fly: {
                name: 'Політ',
                description: 'Стає невразливим на 3 секунди',
                cooldown: 15000,
                effect: this.fly.bind(this)
            },
            roar: {
                name: 'Рик',
                description: 'Оглушує гравця',
                cooldown: 10000,
                effect: this.roar.bind(this)
            },
            voidStrike: {
                name: 'Удар порожнечі',
                description: 'Ігнорує захист',
                cooldown: 5000,
                effect: this.voidStrike.bind(this)
            },
            teleport: {
                name: 'Телепортація',
                description: 'Уникає наступної атаки',
                cooldown: 8000,
                effect: this.teleport.bind(this)
            },
            madness: {
                name: 'Божевілля',
                description: 'Плутає управління гравця',
                cooldown: 20000,
                effect: this.madness.bind(this)
            },
            crystalSpikes: {
                name: 'Кристалеві шипи',
                description: 'Атакує кілька разів',
                cooldown: 6000,
                effect: this.crystalSpikes.bind(this)
            },
            reflect: {
                name: 'Відбиття',
                description: 'Відбиває частину шкоди',
                cooldown: 12000,
                effect: this.reflect.bind(this)
            },
            regenerate: {
                name: 'Регенерація',
                description: 'Повільно відновлює здоров\'я',
                cooldown: 15000,
                effect: this.regenerate.bind(this)
            }
        };
        
        // Current boss state
        this.currentBoss = null;
        this.bossAbilityCooldowns = {};
        this.bossEffects = {};
    }
    
    // Generate boss for specific floor
    generateBoss(floor) {
        const bossKeys = Object.keys(this.bossTemplates);
        const templateKey = bossKeys[floor % bossKeys.length];
        const template = this.bossTemplates[templateKey];
        
        // Base stats for floor
        const baseHp = 50 + floor * 25;
        const baseAttack = 15 + floor * 5;
        const baseDefense = 5 + floor * 2;
        
        const boss = {
            name: template.name,
            type: template.type,
            description: template.description,
            hp: Math.floor(baseHp * template.hpMultiplier),
            maxHp: Math.floor(baseHp * template.hpMultiplier),
            attack: Math.floor(baseAttack * template.attackMultiplier),
            defense: Math.floor(baseDefense * template.defenseMultiplier),
            abilities: [...template.abilities],
            expReward: Math.floor((50 + floor * 15) * template.expMultiplier),
            goldReward: Math.floor((25 + floor * 8) * template.goldMultiplier),
            dropChance: 0.8,
            loot: this.generateBossLoot(floor, template.lootMultiplier),
            isBoss: true,
            isInCombat: false,
            phase: 1,
            maxPhases: 3
        };
        
        // Initialize ability cooldowns
        boss.abilities.forEach(abilityKey => {
            this.bossAbilityCooldowns[abilityKey] = 0;
        });
        
        return boss;
    }
    
    generateBossLoot(floor, multiplier) {
        const items = [];
        const itemCount = Math.floor(2 * multiplier);
        
        for (let i = 0; i < itemCount; i++) {
            const item = this.contentGenerator.generateItem(floor + 2); // Better items
            item.rarity = this.upgradeBossItemRarity(item.rarity);
            items.push(item);
        }
        
        return items;
    }
    
    upgradeBossItemRarity(currentRarity) {
        const rarities = ['common', 'uncommon', 'rare', 'epic', 'legendary'];
        const currentIndex = rarities.indexOf(currentRarity);
        const newIndex = Math.min(rarities.length - 1, currentIndex + 1);
        return rarities[newIndex];
    }
    
    // Boss AI and abilities
    updateBoss(deltaTime) {
        if (!this.currentBoss || !this.gameState.isInCombat) return;
        
        // Update cooldowns
        Object.keys(this.bossAbilityCooldowns).forEach(ability => {
            if (this.bossAbilityCooldowns[ability] > 0) {
                this.bossAbilityCooldowns[ability] -= deltaTime;
            }
        });
        
        // Update effects
        Object.keys(this.bossEffects).forEach(effect => {
            if (this.bossEffects[effect].duration > 0) {
                this.bossEffects[effect].duration -= deltaTime;
            } else {
                delete this.bossEffects[effect];
            }
        });
        
        // Check for phase transitions
        this.checkPhaseTransition();
        
        // Use abilities
        this.tryUseAbility();
    }
    
    checkPhaseTransition() {
        const boss = this.currentBoss;
        const healthPercent = boss.hp / boss.maxHp;
        const newPhase = Math.ceil((1 - healthPercent) * boss.maxPhases);
        
        if (newPhase > boss.phase) {
            boss.phase = newPhase;
            this.onPhaseTransition(newPhase);
        }
    }
    
    onPhaseTransition(phase) {
        this.gameState.addLogEntry(`${this.currentBoss.name} переходить у фазу ${phase}!`, 'combat');
        
        // Phase-specific effects
        switch (phase) {
            case 2:
                this.currentBoss.attack = Math.floor(this.currentBoss.attack * 1.2);
                this.gameState.addLogEntry('Босс стає агресивнішим!', 'combat');
                break;
            case 3:
                this.currentBoss.attack = Math.floor(this.currentBoss.attack * 1.5);
                this.gameState.addLogEntry('Босс впадає в лють!', 'combat');
                break;
        }
    }
    
    tryUseAbility() {
        const boss = this.currentBoss;
        const availableAbilities = boss.abilities.filter(ability => 
            this.bossAbilityCooldowns[ability] <= 0
        );
        
        if (availableAbilities.length > 0 && Math.random() < 0.3) {
            const abilityKey = availableAbilities[Math.floor(Math.random() * availableAbilities.length)];
            this.useAbility(abilityKey);
        }
    }
    
    useAbility(abilityKey) {
        const ability = this.abilities[abilityKey];
        if (!ability) return;
        
        this.gameState.addLogEntry(`${this.currentBoss.name} використовує ${ability.name}!`, 'combat');
        ability.effect();
        this.bossAbilityCooldowns[abilityKey] = ability.cooldown;
    }
    
    // Ability implementations
    summonMinions() {
        const minionCount = 2;
        this.gameState.addLogEntry(`Призвано ${minionCount} міньйонів!`, 'combat');
        // Add minions to combat (simplified)
        this.currentBoss.hp += 20; // Temporary effect
    }
    
    rage() {
        this.bossEffects.rage = { duration: 5000, attackBonus: 0.5 };
        this.gameState.addLogEntry('Босс впадає в лють!', 'combat');
    }
    
    heal() {
        const healAmount = Math.floor(this.currentBoss.maxHp * 0.25);
        this.currentBoss.hp = Math.min(this.currentBoss.maxHp, this.currentBoss.hp + healAmount);
        this.gameState.addLogEntry(`Босс відновив ${healAmount} здоров'я!`, 'combat');
    }
    
    darkMagic() {
        const damage = Math.floor(this.currentBoss.attack * 1.5);
        this.gameState.player.hp -= damage;
        this.gameState.addLogEntry(`Темна магія завдала ${damage} шкоди!`, 'combat');
    }
    
    lifeDrain() {
        const drainAmount = Math.floor(this.gameState.player.maxHp * 0.15);
        this.gameState.player.hp -= drainAmount;
        this.currentBoss.hp = Math.min(this.currentBoss.maxHp, this.currentBoss.hp + drainAmount);
        this.gameState.addLogEntry(`Босс висмоктав ${drainAmount} життя!`, 'combat');
    }
    
    curse() {
        this.bossEffects.curse = { duration: 10000, statReduction: 0.3 };
        this.gameState.addLogEntry('Ви прокляті! Характеристики знижені!', 'combat');
    }
    
    fireBreath() {
        const damage = Math.floor(this.currentBoss.attack * 2);
        this.gameState.player.hp -= damage;
        this.gameState.addLogEntry(`Вогняне дихання завдало ${damage} шкоди!`, 'combat');
    }
    
    fly() {
        this.bossEffects.flying = { duration: 3000 };
        this.gameState.addLogEntry('Босс злітає у повітря!', 'combat');
    }
    
    roar() {
        this.bossEffects.stunned = { duration: 2000 };
        this.gameState.addLogEntry('Ви оглушені рикою!', 'combat');
    }
    
    voidStrike() {
        const damage = this.currentBoss.attack; // Ignores defense
        this.gameState.player.hp -= damage;
        this.gameState.addLogEntry(`Удар порожнечі завдав ${damage} шкоди!`, 'combat');
    }
    
    teleport() {
        this.bossEffects.dodging = { duration: 1000 };
        this.gameState.addLogEntry('Босс телепортується!', 'combat');
    }
    
    madness() {
        this.bossEffects.madness = { duration: 5000 };
        this.gameState.addLogEntry('Ви охоплені божевіллям!', 'combat');
    }
    
    crystalSpikes() {
        const spikeCount = 3;
        for (let i = 0; i < spikeCount; i++) {
            setTimeout(() => {
                const damage = Math.floor(this.currentBoss.attack * 0.7);
                this.gameState.player.hp -= damage;
                this.gameState.addLogEntry(`Кристалевий шип завдав ${damage} шкоди!`, 'combat');
            }, i * 500);
        }
    }
    
    reflect() {
        this.bossEffects.reflecting = { duration: 8000, reflectPercent: 0.3 };
        this.gameState.addLogEntry('Босс відбиває атаки!', 'combat');
    }
    
    regenerate() {
        this.bossEffects.regenerating = { duration: 10000, regenRate: 0.02 };
        this.gameState.addLogEntry('Босс починає регенерацію!', 'combat');
    }
    
    // Check if boss should spawn
    shouldSpawnBoss(floor, room) {
        // Boss every 5 floors on last room
        return floor % 5 === 0 && room === this.gameState.maxRoomsPerFloor;
    }
    
    // Start boss fight
    startBossFight(floor) {
        this.currentBoss = this.generateBoss(floor);
        this.gameState.startCombat(this.currentBoss);
        this.gameState.addLogEntry(`⚔️ БОСС: ${this.currentBoss.name}!`, 'boss');
        this.gameState.addLogEntry(this.currentBoss.description, 'boss');
    }
    
    // End boss fight
    endBossFight(victory) {
        if (victory) {
            this.gameState.stats.bossesKilled = (this.gameState.stats.bossesKilled || 0) + 1;
            
            // Award boss loot
            if (this.currentBoss.loot) {
                this.currentBoss.loot.forEach(item => {
                    this.gameState.addItemToInventory(item);
                    this.gameState.addLogEntry(`Отримано: ${item.name}!`, 'loot');
                });
            }
        }
        
        this.currentBoss = null;
        this.bossAbilityCooldowns = {};
        this.bossEffects = {};
    }
    
    // Get current boss effects for UI
    getBossEffects() {
        return this.bossEffects;
    }
    
    // Apply boss effects to combat
    modifyPlayerAttack(damage) {
        if (this.bossEffects.reflecting) {
            const reflectedDamage = Math.floor(damage * this.bossEffects.reflecting.reflectPercent);
            this.gameState.player.hp -= reflectedDamage;
            this.gameState.addLogEntry(`Відбито ${reflectedDamage} шкоди!`, 'combat');
        }
        
        if (this.bossEffects.flying || this.bossEffects.dodging) {
            this.gameState.addLogEntry('Атака не влучила!', 'combat');
            return 0;
        }
        
        return damage;
    }
    
    modifyBossAttack(damage) {
        let finalDamage = damage;
        
        if (this.bossEffects.rage) {
            finalDamage = Math.floor(finalDamage * (1 + this.bossEffects.rage.attackBonus));
        }
        
        return finalDamage;
    }
}

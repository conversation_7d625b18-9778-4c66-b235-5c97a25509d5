<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Render Fix Test - Idle Roguelike Adventure</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            background: #000;
            color: #00ff00;
        }

        .test-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
        }

        .test-canvas {
            border: 2px solid #00ff00;
            border-radius: 8px;
            margin: 10px;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        button {
            background: #003300;
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 10px 20px;
            cursor: pointer;
            margin: 5px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }

        button:hover {
            background: #006600;
            box-shadow: 0 0 10px #00ff00;
        }

        .info {
            text-align: center;
            margin: 20px 0;
            color: #cccccc;
        }

        .log {
            background: #111;
            border: 1px solid #333;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔧 Render Fix Test - Перевірка виправлень рендерингу</h1>
    
    <div class="info">
        Тестування виправлень: дублювання персонажів та біомних фонів
    </div>

    <div class="controls">
        <button onclick="startTest()">Запустити тест</button>
        <button onclick="stopTest()">Зупинити</button>
        <button onclick="changeFloor()">Змінити поверх</button>
        <button onclick="toggleCombat()">Переключити бій</button>
        <button onclick="clearLog()">Очистити лог</button>
    </div>

    <div class="test-container">
        <canvas id="testCanvas" width="600" height="400" class="test-canvas"></canvas>
    </div>

    <div class="log" id="logContainer">
        <div>Лог тестування:</div>
    </div>

    <script type="module">
        let renderer = null;
        let gameState = null;
        let animationId = null;
        let currentFloor = 1;
        let inCombat = false;

        function log(message) {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        async function initTest() {
            try {
                log('Ініціалізація тесту...');
                
                const { Renderer } = await import('./js/renderer.js');
                
                const canvas = document.getElementById('testCanvas');
                const ctx = canvas.getContext('2d');
                renderer = new Renderer(canvas, ctx);
                
                // Create mock game state
                gameState = {
                    currentFloor: currentFloor,
                    currentRoom: 1,
                    currentLocation: { name: 'Тестова локація' },
                    inCombat: inCombat,
                    player: {
                        name: 'Тестовий герой',
                        level: 10,
                        hp: 75,
                        maxHp: 100,
                        equipment: {
                            weapon: { name: 'Меч' },
                            shield: { name: 'Щит' }
                        }
                    },
                    currentEnemy: inCombat ? {
                        name: 'Тестовий гоблін',
                        type: 'goblin',
                        level: 5,
                        hp: 30,
                        maxHp: 50
                    } : null,
                    effects: []
                };

                log('Тест ініціалізовано успішно');
                log(`Поточний поверх: ${currentFloor}`);
                log(`Бій: ${inCombat ? 'Так' : 'Ні'}`);
                
            } catch (error) {
                log(`Помилка ініціалізації: ${error.message}`);
                console.error('Failed to initialize test:', error);
            }
        }

        function animate() {
            if (!renderer || !gameState) return;

            try {
                // Clear with biome background
                renderer.clear(gameState);
                
                // Render world with characters
                renderer.renderWorld(gameState);
                
                // Render effects
                renderer.renderEffects(gameState.effects);
                
            } catch (error) {
                log(`Помилка рендерингу: ${error.message}`);
                console.error('Render error:', error);
            }

            animationId = requestAnimationFrame(animate);
        }

        window.startTest = function() {
            if (!renderer) {
                initTest().then(() => {
                    animate();
                    log('Тест запущено');
                });
            } else {
                animate();
                log('Тест відновлено');
            }
        };

        window.stopTest = function() {
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
                log('Тест зупинено');
            }
        };

        window.changeFloor = function() {
            currentFloor = (currentFloor % 60) + 1;
            if (gameState) {
                gameState.currentFloor = currentFloor;
                log(`Змінено поверх на: ${currentFloor}`);
                
                // Determine biome
                let biomeName = 'Печера';
                if (currentFloor > 50) biomeName = 'Безодня';
                else if (currentFloor > 35) biomeName = 'Вулкан';
                else if (currentFloor > 25) biomeName = 'Льодовики';
                else if (currentFloor > 15) biomeName = 'Пустеля';
                else if (currentFloor > 5) biomeName = 'Ліс';
                
                log(`Біом: ${biomeName}`);
            }
        };

        window.toggleCombat = function() {
            inCombat = !inCombat;
            if (gameState) {
                gameState.inCombat = inCombat;
                gameState.currentEnemy = inCombat ? {
                    name: 'Тестовий гоблін',
                    type: 'goblin',
                    level: 5,
                    hp: 30,
                    maxHp: 50
                } : null;
                log(`Бій: ${inCombat ? 'Увімкнено' : 'Вимкнено'}`);
            }
        };

        window.clearLog = function() {
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML = '<div>Лог тестування:</div>';
        };

        // Auto-start test
        window.addEventListener('load', () => {
            log('Сторінка завантажена');
            setTimeout(startTest, 1000);
        });

        // Override console.log to capture renderer logs
        const originalLog = console.log;
        console.log = function(...args) {
            if (args[0] && args[0].includes('[Renderer]')) {
                log(args.join(' '));
            }
            originalLog.apply(console, args);
        };

        // Override console.error to capture errors
        const originalError = console.error;
        console.error = function(...args) {
            if (args[0] && args[0].includes('[Renderer]')) {
                log(`ERROR: ${args.join(' ')}`);
            }
            originalError.apply(console, args);
        };
    </script>
</body>
</html>

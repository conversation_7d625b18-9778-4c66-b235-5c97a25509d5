<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Idle Roguelike</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
        }
        .debug-panel {
            border: 1px solid #00ff00;
            margin: 10px 0;
            padding: 10px;
        }
        button {
            background: #003300;
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 8px 16px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #006600;
        }
        .error { color: #ff0000; }
        .success { color: #00ff00; }
        .warning { color: #ffff00; }
    </style>
</head>
<body>
    <h1>Debug Console - Idle Roguelike</h1>
    
    <div class="debug-panel">
        <h3>Швидкі тести</h3>
        <button onclick="testBasicSystems()">Тест основних систем</button>
        <button onclick="testAudio()">Тест аудіо</button>
        <button onclick="testParticles()">Тест частинок</button>
        <button onclick="clearConsole()">Очистити консоль</button>
    </div>
    
    <div class="debug-panel">
        <h3>Консоль</h3>
        <div id="console" style="height: 300px; overflow-y: auto; background: #000; padding: 10px; border: 1px solid #333;"></div>
    </div>
    
    <div class="debug-panel">
        <h3>Запуск гри</h3>
        <button onclick="window.location.href='index.html'">Запустити повну гру</button>
        <button onclick="window.location.href='test.html'">Модульні тести</button>
    </div>

    <script type="module">
        let consoleDiv = document.getElementById('console');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            consoleDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        window.log = log;
        
        window.clearConsole = function() {
            consoleDiv.innerHTML = '';
        };
        
        window.testBasicSystems = async function() {
            log('Початок тестування основних систем...', 'info');
            
            try {
                const { GameState } = await import('./js/gameState.js');
                const gameState = new GameState();
                log('✓ GameState створено успішно', 'success');
                
                const { ContentGenerator } = await import('./js/contentGenerator.js');
                const generator = new ContentGenerator();
                log('✓ ContentGenerator створено успішно', 'success');
                
                const enemy = generator.generateEnemy(1, 1);
                log(`✓ Згенеровано ворога: ${enemy.name}`, 'success');
                
                const item = generator.generateItem(1);
                log(`✓ Згенеровано предмет: ${item.name}`, 'success');
                
                log('Всі основні системи працюють!', 'success');
                
            } catch (error) {
                log(`✗ Помилка: ${error.message}`, 'error');
                console.error(error);
            }
        };
        
        window.testAudio = async function() {
            log('Тестування аудіо системи...', 'info');
            
            try {
                const { AudioManager } = await import('./js/audio.js');
                const audio = new AudioManager();
                log('✓ AudioManager створено', 'success');
                
                await audio.init();
                log('✓ Аудіо ініціалізовано', 'success');
                
                audio.playAttackSound();
                log('✓ Звук атаки відтворено', 'success');
                
                setTimeout(() => {
                    audio.playLevelUpSound();
                    log('✓ Звук підвищення рівня відтворено', 'success');
                }, 1000);
                
            } catch (error) {
                log(`✗ Помилка аудіо: ${error.message}`, 'error');
                console.error(error);
            }
        };
        
        window.testParticles = async function() {
            log('Тестування системи частинок...', 'info');

            try {
                // Створюємо тестовий canvas
                const canvas = document.createElement('canvas');
                canvas.width = 400;
                canvas.height = 300;
                canvas.style.border = '1px solid #00ff00';
                canvas.style.background = '#000';

                const ctx = canvas.getContext('2d');

                const { Renderer } = await import('./js/renderer.js');
                const renderer = new Renderer(canvas, ctx);
                log('✓ Renderer створено', 'success');

                // Додаємо canvas до сторінки
                const panel = document.createElement('div');
                panel.className = 'debug-panel';
                panel.innerHTML = '<h3>Тест частинок</h3>';
                panel.appendChild(canvas);
                document.body.appendChild(panel);

                // Тестуємо частинки
                renderer.createDamageParticles(200, 150, 25, 'damage');
                renderer.createLevelUpParticles(100, 100);
                renderer.createGoldParticles(300, 200, 50);

                log('✓ Частинки створено', 'success');

                // Анімація
                function animate() {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    renderer.updateParticles(16);
                    renderer.renderEffects([]);
                    requestAnimationFrame(animate);
                }
                animate();

                log('✓ Анімація частинок запущена', 'success');

            } catch (error) {
                log(`✗ Помилка частинок: ${error.message}`, 'error');
                console.error(error);
            }
        };

        window.testEnhancements = async function() {
            log('Тестування покращень гри...', 'info');

            try {
                // Тест системи кешування спрайтів
                const { SpriteCache, PerformanceMonitor } = await import('./js/spriteCache.js');
                const spriteCache = new SpriteCache();
                const perfMonitor = new PerformanceMonitor();
                log('✓ Системи продуктивності створено', 'success');

                // Тест розширеної системи навичок
                const { SkillSystem } = await import('./js/skillSystem.js');
                const { GameState } = await import('./js/gameState.js');
                const gameState = new GameState();
                const skillSystem = new SkillSystem(gameState);

                log(`✓ Система навичок: ${Object.keys(skillSystem.skills).length} навичок`, 'success');
                log(`✓ Дерева навичок: ${Object.keys(skillSystem.skillTrees).length} категорій`, 'success');

                // Тест нових навичок
                const newSkills = ['crafting', 'leadership', 'arcaneKnowledge'];
                newSkills.forEach(skill => {
                    if (skillSystem.skills[skill]) {
                        log(`✓ Нова навичка: ${skillSystem.skills[skill].name}`, 'success');
                    }
                });

                // Тест системи передумов
                skillSystem.skillPoints = 100;
                const combatNodes = skillSystem.getAvailableSkillNodes('combat');
                log(`✓ Доступні вузли бою: ${combatNodes.length}`, 'success');

                // Тест покупки навички
                const purchased = skillSystem.purchaseSkillNode('combat', 'power_strike');
                log(`✓ Покупка навички: ${purchased ? 'успішно' : 'неуспішно'}`, purchased ? 'success' : 'warning');

                // Тест системи бою з вимогами
                log(`✓ Система бою: ${gameState.roomCombatRequired ? 'вимагає бій' : 'вільний прохід'}`, 'success');
                log(`✓ Автобій: ${gameState.autoFight ? 'увімкнено' : 'вимкнено'}`, 'success');

                // Статистика продуктивності
                const cacheStats = spriteCache.getStats();
                log(`✓ Кеш спрайтів: ${cacheStats.cacheSize}/${cacheStats.maxCacheSize}`, 'success');

                const perfStats = perfMonitor.getStats();
                log(`✓ Моніторинг: FPS ${perfStats.currentFPS}, оптимізація ${perfStats.optimizationLevel}`, 'success');

                log('Всі покращення працюють!', 'success');

            } catch (error) {
                log(`✗ Помилка покращень: ${error.message}`, 'error');
                console.error(error);
            }
        };
        
        // Автоматичний тест при завантаженні
        log('Debug консоль готова', 'info');
        log('Натисніть кнопки для тестування різних систем', 'info');
    </script>
</body>
</html>

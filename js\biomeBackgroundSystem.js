// Biome background system with dynamic environments
export class BiomeBackgroundSystem {
    constructor(canvas, ctx) {
        this.canvas = canvas;
        this.ctx = ctx;
        this.currentBiome = 'cave';
        this.particles = [];
        this.maxParticles = 30;
        
        // Biome configurations
        this.biomes = {
            cave: {
                name: 'Печера',
                background: 'linear-gradient(135deg, #2c1810, #1a0f08)',
                particleType: 'dust',
                particleCount: 10,
                particleColor: '#8b7355'
            },
            forest: {
                name: 'Ліс',
                background: 'linear-gradient(135deg, #1a3d1a, #0d2b0d)',
                particleType: 'leaves',
                particleCount: 10,
                particleColor: '#4d7c0f'
            },
            desert: {
                name: 'Пустеля',
                background: 'linear-gradient(135deg, #8b4513, #654321)',
                particleType: 'sand',
                particleCount: 15,
                particleColor: '#daa520'
            },
            glacier: {
                name: 'Льодовики',
                background: 'linear-gradient(135deg, #4682b4, #1e3a8a)',
                particleType: 'snow',
                particleCount: 20,
                particleColor: '#ffffff'
            },
            volcano: {
                name: 'Вулкан',
                background: 'linear-gradient(135deg, #8b0000, #2d0000)',
                particleType: 'sparks',
                particleCount: 15,
                particleColor: '#ff4500'
            },
            abyss: {
                name: 'Безодня',
                background: 'linear-gradient(135deg, #000000, #1a0033)',
                particleType: 'void',
                particleCount: 8,
                particleColor: '#9966ff'
            }
        };
        
        this.initializeParticles();
    }
    
    // Get biome based on floor
    getBiomeForFloor(floor) {
        if (floor <= 10) return 'cave';
        if (floor <= 20) return 'forest';
        if (floor <= 30) return 'desert';
        if (floor <= 40) return 'glacier';
        if (floor <= 50) return 'volcano';
        return 'abyss';
    }
    
    // Update biome background
    updateBiomeBackground(floor) {
        const newBiome = this.getBiomeForFloor(floor);
        
        if (newBiome !== this.currentBiome) {
            this.currentBiome = newBiome;
            this.updateCSSBackground();
            this.initializeParticles();
            
            // Trigger biome change event
            this.onBiomeChange(newBiome);
        }
    }
    
    // Update CSS background
    updateCSSBackground() {
        const biome = this.biomes[this.currentBiome];
        if (biome) {
            document.body.style.background = biome.background;
            
            // Update CSS custom properties
            document.documentElement.style.setProperty('--biome-bg', biome.background);
            document.documentElement.style.setProperty('--biome-particle-color', biome.particleColor);
        }
    }
    
    // Initialize particles for current biome
    initializeParticles() {
        this.particles = [];
        const biome = this.biomes[this.currentBiome];
        
        if (!biome) return;
        
        for (let i = 0; i < biome.particleCount; i++) {
            this.particles.push(this.createParticle(biome));
        }
    }
    
    // Create particle based on biome type
    createParticle(biome) {
        const particle = {
            x: Math.random() * this.canvas.width,
            y: Math.random() * this.canvas.height,
            size: Math.random() * 3 + 1,
            color: biome.particleColor,
            opacity: Math.random() * 0.5 + 0.3,
            type: biome.particleType
        };
        
        switch (biome.particleType) {
            case 'dust':
                particle.vx = (Math.random() - 0.5) * 0.5;
                particle.vy = Math.random() * 0.3 + 0.1;
                particle.life = Math.random() * 1000 + 2000;
                break;
                
            case 'leaves':
                particle.vx = (Math.random() - 0.5) * 1;
                particle.vy = Math.random() * 0.5 + 0.2;
                particle.rotation = Math.random() * Math.PI * 2;
                particle.rotationSpeed = (Math.random() - 0.5) * 0.02;
                particle.life = Math.random() * 2000 + 3000;
                break;
                
            case 'sand':
                particle.vx = Math.random() * 2 - 1;
                particle.vy = Math.random() * 0.8 + 0.2;
                particle.life = Math.random() * 1500 + 2000;
                break;
                
            case 'snow':
                particle.vx = (Math.random() - 0.5) * 0.8;
                particle.vy = Math.random() * 1 + 0.5;
                particle.size = Math.random() * 4 + 2;
                particle.life = Math.random() * 3000 + 4000;
                break;
                
            case 'sparks':
                particle.vx = (Math.random() - 0.5) * 3;
                particle.vy = -Math.random() * 2 - 1;
                particle.gravity = 0.05;
                particle.life = Math.random() * 800 + 500;
                particle.color = ['#ff4500', '#ff6600', '#ffaa00'][Math.floor(Math.random() * 3)];
                break;
                
            case 'void':
                particle.vx = (Math.random() - 0.5) * 0.3;
                particle.vy = (Math.random() - 0.5) * 0.3;
                particle.pulse = Math.random() * Math.PI * 2;
                particle.pulseSpeed = 0.02 + Math.random() * 0.02;
                particle.life = Math.random() * 5000 + 5000;
                break;
        }
        
        particle.maxLife = particle.life;
        return particle;
    }
    
    // Update particles
    updateParticles(deltaTime) {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            
            // Update position
            particle.x += particle.vx * deltaTime / 16;
            particle.y += particle.vy * deltaTime / 16;
            
            // Apply gravity for sparks
            if (particle.gravity) {
                particle.vy += particle.gravity * deltaTime / 16;
            }
            
            // Update rotation for leaves
            if (particle.rotation !== undefined) {
                particle.rotation += particle.rotationSpeed * deltaTime / 16;
            }
            
            // Update pulse for void particles
            if (particle.pulse !== undefined) {
                particle.pulse += particle.pulseSpeed * deltaTime / 16;
                particle.opacity = 0.3 + Math.sin(particle.pulse) * 0.2;
            }
            
            // Update life
            particle.life -= deltaTime;
            
            // Remove dead particles or particles off screen
            if (particle.life <= 0 || 
                particle.x < -10 || particle.x > this.canvas.width + 10 ||
                particle.y < -10 || particle.y > this.canvas.height + 10) {
                
                this.particles.splice(i, 1);
                
                // Replace with new particle
                const biome = this.biomes[this.currentBiome];
                if (biome && this.particles.length < biome.particleCount) {
                    this.particles.push(this.createParticle(biome));
                }
            }
        }
    }
    
    // Render particles
    renderParticles() {
        this.ctx.save();
        
        this.particles.forEach(particle => {
            this.ctx.globalAlpha = particle.opacity * (particle.life / particle.maxLife);
            this.ctx.fillStyle = particle.color;
            
            this.ctx.save();
            this.ctx.translate(particle.x, particle.y);
            
            if (particle.rotation !== undefined) {
                this.ctx.rotate(particle.rotation);
            }
            
            switch (particle.type) {
                case 'dust':
                case 'sand':
                    this.ctx.fillRect(-particle.size/2, -particle.size/2, particle.size, particle.size);
                    break;
                    
                case 'leaves':
                    this.ctx.beginPath();
                    this.ctx.ellipse(0, 0, particle.size, particle.size * 0.6, 0, 0, Math.PI * 2);
                    this.ctx.fill();
                    break;
                    
                case 'snow':
                    this.ctx.beginPath();
                    this.ctx.arc(0, 0, particle.size, 0, Math.PI * 2);
                    this.ctx.fill();
                    break;
                    
                case 'sparks':
                    this.ctx.beginPath();
                    this.ctx.arc(0, 0, particle.size, 0, Math.PI * 2);
                    this.ctx.fill();
                    // Add glow effect
                    this.ctx.shadowBlur = 5;
                    this.ctx.shadowColor = particle.color;
                    this.ctx.fill();
                    this.ctx.shadowBlur = 0;
                    break;
                    
                case 'void':
                    this.ctx.beginPath();
                    this.ctx.arc(0, 0, particle.size, 0, Math.PI * 2);
                    this.ctx.fill();
                    // Add void glow
                    this.ctx.shadowBlur = 8;
                    this.ctx.shadowColor = particle.color;
                    this.ctx.fill();
                    this.ctx.shadowBlur = 0;
                    break;
            }
            
            this.ctx.restore();
        });
        
        this.ctx.restore();
    }
    
    // Update system
    update(deltaTime) {
        this.updateParticles(deltaTime);
    }
    
    // Render system
    render() {
        this.renderParticles();
    }
    
    // Biome change callback
    onBiomeChange(newBiome) {
        const biome = this.biomes[newBiome];
        if (biome) {
            console.log(`Biome changed to: ${biome.name}`);
            
            // Trigger UI update if available
            if (window.game && window.game.ui) {
                window.game.ui.showNotification(`Увійшли в біом: ${biome.name}`, 'info', 3000);
            }
        }
    }
    
    // Get current biome info
    getCurrentBiomeInfo() {
        return this.biomes[this.currentBiome];
    }
    
    // Resize handler
    handleResize(width, height) {
        this.canvas.width = width;
        this.canvas.height = height;
        
        // Reinitialize particles for new canvas size
        this.initializeParticles();
    }
}

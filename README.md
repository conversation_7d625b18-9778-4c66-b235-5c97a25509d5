# Idle Roguelike Adventure

Повноцінна браузерна гра в жанрі idle-roguelike з процедурною генерацією всіх елементів.

## Особливості

### 🎮 Ігровий процес
- **Idle механіка**: Гра продовжується навіть коли ви не граєте
- **Процедурна генерація**: Унікальні рівні, вороги та предмети
- **Автоматизація**: Автоматичні бої, збір ресурсів та прогрес
- **Нескінченний контент**: Безмежні підземелля для дослідження

### 🎨 Графіка
- **Canvas 2D рендеринг**: Вся графіка генерується програмно
- **Процедурні спрайти**: Герої, вороги та предмети створюються з примітивів
- **Анімації**: Плавні анімації та ефекти
- **Адаптивний дизайн**: Працює на ПК та мобільних пристроях

### 🎵 Аудіо
- **Web Audio API**: Музика та звуки генеруються програмно
- **Адаптивна музика**: Темп змінюється залежно від ігрової ситуації
- **Звукові ефекти**: Унікальні звуки для різних дій

### 💾 Збереження
- **localStorage**: Автоматичне збереження прогресу
- **Офлайн прогрес**: Розрахунок прогресу під час відсутності
- **Резервні копії**: Автоматичне створення бекапів

## Як грати

### Основи
1. **Дослідження**: Переміщайтеся між кімнатами та поверхами
2. **Бої**: Зустрічайте ворогів та перемагайте їх
3. **Лут**: Збирайте золото та предмети
4. **Прогрес**: Підвищуйте рівень та покращуйте екіпірування

### Управління
- **Пробіл**: Увімкнути/вимкнути авто-бій
- **Enter**: Перейти до наступної кімнати
- **Escape**: Відкрити меню гри
- **Клік по предмету**: Екіпірувати або використати

### Автоматизація
- **Авто-бій**: Автоматичні атаки під час бою
- **Авто-збір**: Автоматичний збір ресурсів
- **Авто-екіпірування**: Автоматичне екіпірування кращих предметів

## Технічні деталі

### Архітектура
```
js/
├── main.js              # Головний файл гри
├── gameState.js         # Управління станом гри
├── renderer.js          # Система рендерингу
├── audio.js             # Генерація аудіо
├── contentGenerator.js  # Процедурна генерація
├── idleManager.js       # Idle механіка
├── saveManager.js       # Збереження/завантаження
└── ui.js               # Інтерфейс користувача
```

### Модульна система
- **ES6 модулі**: Чистий JavaScript без зовнішніх залежностей
- **Слабка зв'язаність**: Кожен модуль має чітку відповідальність
- **Розширюваність**: Легко додавати нові функції

### Процедурна генерація
- **Детермінована**: Використання seed для консистентності
- **Масштабована**: Складність зростає з прогресом
- **Різноманітна**: Тисячі унікальних комбінацій

## Запуск гри

### Швидкий запуск
**Windows:**
```bash
start.bat
```

**Linux/Mac:**
```bash
./start.sh
```

### Ручний запуск
1. Клонуйте репозиторій
2. Запустіть HTTP сервер:
   ```bash
   python -m http.server 8000
   ```
3. Відкрийте http://localhost:8000

### Тестування
- Відкрийте `test.html` для тестування модулів
- Перевірте консоль браузера на помилки

## Розробка

### Додавання нових функцій
1. Створіть новий модуль у папці `js/`
2. Імпортуйте у `main.js`
3. Інтегруйте з існуючими системами

### Налагодження
- Використовуйте `console.log()` для відстеження
- Перевіряйте `localStorage` для збережених даних
- Тестуйте на різних пристроях

## Майбутні покращення

### Планується
- [ ] Система гільдій та мультиплеєр
- [ ] Більше типів ворогів та боссів
- [ ] Система крафтингу
- [ ] Досягнення та рейтинги
- [ ] Покращена графіка та ефекти

### Можливі розширення
- WebGL рендеринг для кращої продуктивності
- Більш складна музична система
- Система модів та користувацького контенту

## Ліцензія

MIT License - використовуйте та модифікуйте як завгодно!

## Автор

Створено за допомогою Augment Agent - AI асистента для розробки.

---

**Насолоджуйтеся грою! 🎮**

# Idle Roguelike Adventure

Повноцінна браузерна гра в жанрі idle-roguelike з процедурною генерацією всіх елементів.

## Особливості

### 🎮 Ігровий процес
- **Idle механіка**: Гра продовжується навіть коли ви не граєте
- **Процедурна генерація**: Унікальні рівні, вороги та предмети
- **Автоматизація**: Автоматичні бої, збір ресурсів та прогрес
- **Нескінченний контент**: Безмежні підземелля для дослідження
- **Система навичок**: 5 різних навичок з унікальними бонусами
- **Босси**: Епічні битви з могутніми ворогами кожні 5 поверхів
- **Престиж**: Перезапуск гри з постійними покращеннями
- **Досягнення**: Система винагород за особливі досягнення

### 🎨 Графіка
- **Canvas 2D рендеринг**: Вся графіка генерується програмно
- **Процедурні спрайти**: Герої, вороги та предмети створюються з примітивів
- **Система частинок**: Візуальні ефекти для боїв, підвищень рівня та магії
- **Анімації**: Плавні анімації та ефекти з підтримкою зменшеного руху
- **Адаптивний дизайн**: Працює на ПК та мобільних пристроях

### 🎵 Аудіо
- **Web Audio API**: Музика та звуки генеруються програмно
- **Адаптивна музика**: Різні треки для різних ситуацій (дослідження, бій, босси)
- **Звукові ефекти**: Унікальні звуки для різних дій та рідкості предметів
- **Процедурні мелодії**: Музика генерується на основі ігрового стану

### 💾 Збереження
- **localStorage**: Автоматичне збереження прогресу
- **Офлайн прогрес**: Розрахунок прогресу під час відсутності
- **Резервні копії**: Автоматичне створення бекапів

## Як грати

### Основи
1. **Дослідження**: Переміщайтеся між кімнатами та поверхами
2. **Бої**: Зустрічайте ворогів та перемагайте їх
3. **Лут**: Збирайте золото та предмети
4. **Прогрес**: Підвищуйте рівень та покращуйте екіпірування

### Управління
- **Пробіл**: Увімкнути/вимкнути авто-бій
- **Enter**: Перейти до наступної кімнати
- **Escape**: Відкрити меню гри
- **Клік по предмету**: Екіпірувати або використати
- **Клік по досягненню**: Переглянути всі досягнення
- **Налаштування**: Повний контроль над аудіо та візуальними ефектами

### Автоматизація
- **Авто-бій**: Автоматичні атаки під час бою
- **Авто-збір**: Автоматичний збір ресурсів
- **Авто-екіпірування**: Автоматичне екіпірування кращих предметів
- **Авто-дослідження**: Автоматичне переміщення між кімнатами

### Прогресія
- **Система навичок**: Бій, Дослідження, Виживання, Удача, Магія
- **Престиж**: Перезапуск з постійними бонусами
- **Досягнення**: Винагороди за особливі досягнення
- **Босси**: Епічні битви з унікальними здібностями

## Технічні деталі

### Архітектура
```
js/
├── main.js              # Головний файл гри
├── gameState.js         # Управління станом гри
├── renderer.js          # Система рендерингу
├── audio.js             # Генерація аудіо
├── contentGenerator.js  # Процедурна генерація
├── idleManager.js       # Idle механіка
├── saveManager.js       # Збереження/завантаження
├── ui.js               # Інтерфейс користувача
├── skillSystem.js       # Система навичок та досягнень
├── bossSystem.js        # Система боссів
├── prestigeSystem.js    # Система престижу
├── particleSystem.js    # Система частинок
└── settingsManager.js   # Налаштування та статистика
```

### Модульна система
- **ES6 модулі**: Чистий JavaScript без зовнішніх залежностей
- **Слабка зв'язаність**: Кожен модуль має чітку відповідальність
- **Розширюваність**: Легко додавати нові функції

### Процедурна генерація
- **Детермінована**: Використання seed для консистентності
- **Масштабована**: Складність зростає з прогресом
- **Різноманітна**: Тисячі унікальних комбінацій

## Запуск гри

### Швидкий запуск
**Windows:**
```bash
start.bat
```

**Linux/Mac:**
```bash
./start.sh
```

### Ручний запуск
1. Клонуйте репозиторій
2. Запустіть HTTP сервер:
   ```bash
   python -m http.server 8000
   ```
3. Відкрийте http://localhost:8000

### Тестування
- Відкрийте `test.html` для тестування модулів
- Перевірте консоль браузера на помилки

## Розробка

### Додавання нових функцій
1. Створіть новий модуль у папці `js/`
2. Імпортуйте у `main.js`
3. Інтегруйте з існуючими системами

### Налагодження
- Використовуйте `console.log()` для відстеження
- Перевіряйте `localStorage` для збережених даних
- Тестуйте на різних пристроях

## Майбутні покращення

### Планується
- [ ] Система гільдій та мультиплеєр
- [ ] Більше типів ворогів та боссів
- [ ] Система крафтингу
- [ ] Досягнення та рейтинги
- [ ] Покращена графіка та ефекти

### Можливі розширення
- WebGL рендеринг для кращої продуктивності
- Більш складна музична система
- Система модів та користувацького контенту

## Ліцензія

MIT License - використовуйте та модифікуйте як завгодно!

## Автор

Створено за допомогою Augment Agent - AI асистента для розробки.

---

**Насолоджуйтеся грою! 🎮**

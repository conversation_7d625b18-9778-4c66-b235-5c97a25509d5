// Idle game mechanics manager
export class IdleManager {
    constructor(gameState, contentGenerator) {
        this.gameState = gameState;
        this.contentGenerator = contentGenerator;
        
        // Timing
        this.lastUpdate = Date.now();
        this.combatTimer = 0;
        this.combatInterval = 2000; // 2 seconds between attacks
        this.explorationTimer = 0;
        this.explorationInterval = 5000; // 5 seconds to explore room
        
        // Auto settings
        this.autoFight = false;
        this.autoExplore = false;
        this.autoLoot = true;
        this.autoHeal = true;
        
        // Efficiency multipliers
        this.combatSpeedMultiplier = 1.0;
        this.explorationSpeedMultiplier = 1.0;
        this.lootMultiplier = 1.0;
        
        // Statistics
        this.idleStats = {
            totalIdleTime: 0,
            autoFightsWon: 0,
            autoRoomsExplored: 0,
            autoItemsCollected: 0
        };
    }
    
    update(deltaTime) {
        this.gameState.stats.totalPlayTime += deltaTime;
        
        // Update effects
        this.gameState.updateEffects(deltaTime);
        
        // Auto healing
        if (this.autoHeal && this.gameState.player.hp < this.gameState.player.maxHp * 0.5) {
            this.tryAutoHeal();
        }
        
        // Combat management
        if (this.gameState.isInCombat) {
            this.updateCombat(deltaTime);
        } else {
            // Exploration management
            if (this.autoExplore) {
                this.updateExploration(deltaTime);
            }
            
            // Check for random encounters
            this.checkForEncounters();
        }
        
        // Auto progression
        this.updateAutoProgression(deltaTime);
    }
    
    updateCombat(deltaTime) {
        if (!this.gameState.currentEnemy) return;
        
        this.combatTimer += deltaTime;
        
        if (this.combatTimer >= this.combatInterval / this.combatSpeedMultiplier) {
            this.combatTimer = 0;
            
            if (this.autoFight) {
                this.performAutoCombat();
            }
        }
    }
    
    performAutoCombat() {
        if (!this.gameState.currentEnemy || !this.gameState.isInCombat) return;
        
        // Player attacks first
        this.gameState.playerAttack();
        
        // If enemy is still alive, it attacks back
        if (this.gameState.currentEnemy && this.gameState.currentEnemy.hp > 0) {
            setTimeout(() => {
                if (this.gameState.currentEnemy && this.gameState.isInCombat) {
                    this.gameState.enemyAttack();
                }
            }, 500);
        } else {
            // Enemy defeated
            this.idleStats.autoFightsWon++;

            // Award combat skill experience
            if (window.game && window.game.skillSystem) {
                window.game.skillSystem.gainSkillExperience('combat', 10);
            }
        }
    }
    
    updateExploration(deltaTime) {
        if (this.gameState.isInCombat) return;
        
        this.explorationTimer += deltaTime;
        
        if (this.explorationTimer >= this.explorationInterval / this.explorationSpeedMultiplier) {
            this.explorationTimer = 0;
            this.autoExploreRoom();
        }
    }
    
    autoExploreRoom() {
        // Check for boss first
        if (this.contentGenerator.shouldSpawnBoss(
            this.gameState.currentFloor,
            this.gameState.currentRoom,
            this.gameState.maxRoomsPerFloor
        )) {
            // Boss fight
            if (window.game && window.game.bossSystem) {
                window.game.bossSystem.startBossFight(this.gameState.currentFloor);
                return;
            }
        }

        // Generate random event for current room
        const event = this.contentGenerator.generateRandomEvent(
            this.gameState.currentFloor,
            this.gameState.currentRoom
        );

        this.processEvent(event);

        // Move to next room after processing event
        setTimeout(() => {
            if (!this.gameState.isInCombat) {
                this.gameState.nextRoom();
                this.idleStats.autoRoomsExplored++;

                // Award exploration skill experience
                if (window.game && window.game.skillSystem) {
                    window.game.skillSystem.gainSkillExperience('exploration', 5);
                }
            }
        }, 1000);
    }
    
    processEvent(event) {
        switch (event.type) {
            case 'combat':
                this.gameState.startCombat(event.enemy);
                break;
                
            case 'treasure':
                this.collectTreasure(event);
                break;
                
            case 'healing':
                this.gameState.healPlayer(event.amount);
                break;
                
            case 'shrine':
                this.activateShrine(event);
                break;
                
            case 'merchant':
                this.encounterMerchant(event);
                break;
                
            case 'trap':
                this.triggerTrap(event);
                break;
        }
    }
    
    collectTreasure(treasure) {
        if (treasure.gold) {
            const goldAmount = Math.floor(treasure.gold * this.lootMultiplier);
            this.gameState.gainGold(goldAmount);
        }
        
        if (treasure.item && this.autoLoot) {
            if (this.gameState.addItemToInventory(treasure.item)) {
                this.idleStats.autoItemsCollected++;
                
                // Auto-equip if better than current equipment
                this.tryAutoEquip(treasure.item);
            }
        }
    }
    
    tryAutoEquip(item) {
        if (!item.stats) return;
        
        let shouldEquip = false;
        let slot = null;
        
        if (item.type === 'weapon') {
            slot = 'weapon';
            const currentWeapon = this.gameState.player.equipment.weapon;
            
            if (!currentWeapon || (item.stats.attack || 0) > (currentWeapon.stats.attack || 0)) {
                shouldEquip = true;
            }
        } else if (item.type === 'armor') {
            slot = 'armor';
            const currentArmor = this.gameState.player.equipment.armor;
            
            if (!currentArmor || (item.stats.defense || 0) > (currentArmor.stats.defense || 0)) {
                shouldEquip = true;
            }
        }
        
        if (shouldEquip && slot) {
            // Remove from inventory
            const itemIndex = this.gameState.player.inventory.indexOf(item);
            if (itemIndex !== -1) {
                this.gameState.removeItemFromInventory(itemIndex);
                this.gameState.equipItem(item, slot);
                this.gameState.addLogEntry(`Автоматично екіпірував ${item.name}`, 'loot');
            }
        }
    }
    
    activateShrine(shrine) {
        const effect = shrine.effect;
        
        if (effect.type === 'stat_boost') {
            switch (effect.stat) {
                case 'attack':
                    this.gameState.player.attack += effect.amount;
                    break;
                case 'defense':
                    this.gameState.player.defense += effect.amount;
                    break;
                case 'maxHp':
                    this.gameState.player.maxHp += effect.amount;
                    this.gameState.player.hp += effect.amount; // Also heal
                    break;
            }
            
            this.gameState.addLogEntry(`${shrine.name}: +${effect.amount} ${effect.stat}`, 'level');
        }
    }
    
    encounterMerchant(merchant) {
        // Auto-buy items if they're better and player has gold
        if (merchant.items && this.autoLoot) {
            merchant.items.forEach(item => {
                if (this.shouldBuyItem(item) && this.gameState.spendGold(item.value)) {
                    this.gameState.addItemToInventory(item);
                    this.tryAutoEquip(item);
                    this.gameState.addLogEntry(`Купив ${item.name} за ${item.value} золота`, 'loot');
                }
            });
        }
    }
    
    shouldBuyItem(item) {
        if (this.gameState.player.gold < item.value) return false;
        
        // Only buy if significantly better than current equipment
        if (item.type === 'weapon') {
            const current = this.gameState.player.equipment.weapon;
            return !current || (item.stats.attack || 0) > (current.stats.attack || 0) * 1.2;
        } else if (item.type === 'armor') {
            const current = this.gameState.player.equipment.armor;
            return !current || (item.stats.defense || 0) > (current.stats.defense || 0) * 1.2;
        }
        
        return false;
    }
    
    triggerTrap(trap) {
        this.gameState.player.hp -= trap.damage;
        this.gameState.addLogEntry(`Пастка завдала ${trap.damage} шкоди!`, 'combat');
        
        if (this.gameState.player.hp <= 0) {
            this.gameState.endCombat(false);
        }
    }
    
    tryAutoHeal() {
        // Look for healing potions in inventory
        const healingPotions = this.gameState.player.inventory.filter(item => 
            item.name.toLowerCase().includes('зілля') || 
            item.type === 'potion'
        );
        
        if (healingPotions.length > 0) {
            const potion = healingPotions[0];
            const healAmount = 50; // Default heal amount
            
            this.gameState.healPlayer(healAmount);
            
            // Remove potion from inventory
            const potionIndex = this.gameState.player.inventory.indexOf(potion);
            this.gameState.removeItemFromInventory(potionIndex);
            
            this.gameState.addLogEntry(`Використав ${potion.name}`, 'heal');
        }
    }
    
    checkForEncounters() {
        // Random encounters while idle
        if (!this.gameState.isInCombat && Math.random() < 0.001) { // 0.1% chance per update
            const enemy = this.contentGenerator.generateEnemy(
                this.gameState.currentFloor,
                this.gameState.currentRoom
            );
            
            this.gameState.startCombat(enemy);
            this.gameState.addLogEntry('Несподівана зустріч!', 'combat');
        }
    }
    
    updateAutoProgression(deltaTime) {
        // Passive experience gain
        if (!this.gameState.isInCombat) {
            const passiveExpRate = 0.1; // XP per second
            const expGain = (deltaTime / 1000) * passiveExpRate * (1 + this.gameState.currentFloor * 0.1);
            
            if (expGain >= 1) {
                this.gameState.gainExperience(Math.floor(expGain));
            }
        }
        
        // Passive gold generation
        const passiveGoldRate = 0.05; // Gold per second
        const goldGain = (deltaTime / 1000) * passiveGoldRate * (1 + this.gameState.currentFloor * 0.05);
        
        if (goldGain >= 1) {
            this.gameState.gainGold(Math.floor(goldGain));
        }
    }
    
    // Control methods
    toggleAutoFight() {
        this.autoFight = !this.autoFight;
        this.gameState.autoFight = this.autoFight;
        this.gameState.addLogEntry(`Авто-бій: ${this.autoFight ? 'УВІМК' : 'ВИМК'}`, 'system');
    }
    
    toggleAutoExplore() {
        this.autoExplore = !this.autoExplore;
        this.gameState.autoProgress = this.autoExplore;
        this.gameState.addLogEntry(`Авто-дослідження: ${this.autoExplore ? 'УВІМК' : 'ВИМК'}`, 'system');
    }
    
    toggleAutoLoot() {
        this.autoLoot = !this.autoLoot;
        this.gameState.autoLoot = this.autoLoot;
        this.gameState.addLogEntry(`Авто-збір: ${this.autoLoot ? 'УВІМК' : 'ВИМК'}`, 'system');
    }
    
    toggleAutoHeal() {
        this.autoHeal = !this.autoHeal;
        this.gameState.addLogEntry(`Авто-лікування: ${this.autoHeal ? 'УВІМК' : 'ВИМК'}`, 'system');
    }
    
    // Upgrade methods
    upgradeCombatSpeed(cost) {
        if (this.gameState.spendGold(cost)) {
            this.combatSpeedMultiplier *= 1.1;
            this.gameState.addLogEntry('Покращено швидкість бою!', 'upgrade');
            return true;
        }
        return false;
    }
    
    upgradeExplorationSpeed(cost) {
        if (this.gameState.spendGold(cost)) {
            this.explorationSpeedMultiplier *= 1.1;
            this.gameState.addLogEntry('Покращено швидкість дослідження!', 'upgrade');
            return true;
        }
        return false;
    }
    
    upgradeLootMultiplier(cost) {
        if (this.gameState.spendGold(cost)) {
            this.lootMultiplier *= 1.1;
            this.gameState.addLogEntry('Покращено збір ресурсів!', 'upgrade');
            return true;
        }
        return false;
    }
    
    // Offline progress calculation
    calculateOfflineProgress(offlineTime) {
        const hours = offlineTime / (1000 * 60 * 60);
        
        // Simulate offline combat and exploration
        const offlineRoomsExplored = Math.floor(hours * 10); // 10 rooms per hour
        const offlineEnemiesKilled = Math.floor(offlineRoomsExplored * 0.7); // 70% combat rate
        
        // Calculate rewards
        const offlineExp = offlineEnemiesKilled * (20 + this.gameState.currentFloor * 5);
        const offlineGold = offlineEnemiesKilled * (5 + this.gameState.currentFloor * 2);
        
        // Apply rewards
        this.gameState.gainExperience(offlineExp);
        this.gameState.gainGold(offlineGold);
        
        // Update stats
        this.gameState.stats.enemiesKilled += offlineEnemiesKilled;
        this.idleStats.totalIdleTime += offlineTime;
        
        // Generate offline report
        return {
            timeAway: hours,
            roomsExplored: offlineRoomsExplored,
            enemiesKilled: offlineEnemiesKilled,
            experienceGained: offlineExp,
            goldGained: offlineGold
        };
    }
    
    // Serialization
    serialize() {
        return {
            autoFight: this.autoFight,
            autoExplore: this.autoExplore,
            autoLoot: this.autoLoot,
            autoHeal: this.autoHeal,
            combatSpeedMultiplier: this.combatSpeedMultiplier,
            explorationSpeedMultiplier: this.explorationSpeedMultiplier,
            lootMultiplier: this.lootMultiplier,
            idleStats: this.idleStats
        };
    }
    
    deserialize(data) {
        if (data.autoFight !== undefined) this.autoFight = data.autoFight;
        if (data.autoExplore !== undefined) this.autoExplore = data.autoExplore;
        if (data.autoLoot !== undefined) this.autoLoot = data.autoLoot;
        if (data.autoHeal !== undefined) this.autoHeal = data.autoHeal;
        if (data.combatSpeedMultiplier) this.combatSpeedMultiplier = data.combatSpeedMultiplier;
        if (data.explorationSpeedMultiplier) this.explorationSpeedMultiplier = data.explorationSpeedMultiplier;
        if (data.lootMultiplier) this.lootMultiplier = data.lootMultiplier;
        if (data.idleStats) this.idleStats = { ...this.idleStats, ...data.idleStats };
    }
}

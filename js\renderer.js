// Renderer module for Canvas 2D graphics
import { ParticleSystem } from './particleSystem.js';

export class Renderer {
    constructor(canvas, ctx) {
        this.canvas = canvas;
        this.ctx = ctx;
        this.width = canvas.width;
        this.height = canvas.height;

        // Initialize particle system
        this.particleSystem = new ParticleSystem(this);
        
        // Animation time for effects
        this.time = 0;
        
        // Sprite cache
        this.spriteCache = new Map();
        
        // Colors
        this.colors = {
            background: '#000000',
            wall: '#444444',
            floor: '#222222',
            player: '#00ff00',
            enemy: '#ff4444',
            item: '#ffff00',
            gold: '#ffd700',
            health: '#ff0000',
            mana: '#0066ff',
            experience: '#9966ff'
        };
    }
    
    updateCanvasSize() {
        this.width = this.canvas.width;
        this.height = this.canvas.height;
    }
    
    clear() {
        this.ctx.fillStyle = this.colors.background;
        this.ctx.fillRect(0, 0, this.width, this.height);
        this.time += 16; // Approximate 60fps
    }
    
    // Basic drawing primitives
    drawRect(x, y, width, height, color, filled = true) {
        this.ctx.strokeStyle = color;
        this.ctx.fillStyle = color;
        
        if (filled) {
            this.ctx.fillRect(x, y, width, height);
        } else {
            this.ctx.strokeRect(x, y, width, height);
        }
    }
    
    drawCircle(x, y, radius, color, filled = true) {
        this.ctx.beginPath();
        this.ctx.arc(x, y, radius, 0, Math.PI * 2);
        this.ctx.strokeStyle = color;
        this.ctx.fillStyle = color;
        
        if (filled) {
            this.ctx.fill();
        } else {
            this.ctx.stroke();
        }
    }
    
    drawText(text, x, y, color = '#ffffff', size = 16, align = 'left') {
        this.ctx.fillStyle = color;
        this.ctx.font = `${size}px 'Courier New', monospace`;
        this.ctx.textAlign = align;
        this.ctx.fillText(text, x, y);
    }
    
    // Procedural sprite generation
    generatePlayerSprite(size = 32) {
        const key = `player_${size}`;
        if (this.spriteCache.has(key)) {
            return this.spriteCache.get(key);
        }
        
        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;
        const ctx = canvas.getContext('2d');
        
        const centerX = size / 2;
        const centerY = size / 2;
        
        // Body (green circle)
        ctx.fillStyle = this.colors.player;
        ctx.beginPath();
        ctx.arc(centerX, centerY, size * 0.3, 0, Math.PI * 2);
        ctx.fill();
        
        // Eyes (animated)
        const eyeOffset = Math.sin(this.time * 0.01) * 2;
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(centerX - 6, centerY - 4 + eyeOffset, 3, 3);
        ctx.fillRect(centerX + 3, centerY - 4 + eyeOffset, 3, 3);
        
        // Pupils
        ctx.fillStyle = '#000000';
        ctx.fillRect(centerX - 5, centerY - 3 + eyeOffset, 1, 1);
        ctx.fillRect(centerX + 4, centerY - 3 + eyeOffset, 1, 1);
        
        // Weapon (simple sword)
        ctx.strokeStyle = '#cccccc';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(centerX + size * 0.3, centerY - size * 0.2);
        ctx.lineTo(centerX + size * 0.5, centerY - size * 0.4);
        ctx.stroke();
        
        this.spriteCache.set(key, canvas);
        return canvas;
    }
    
    generateEnemySprite(enemyType, size = 32) {
        const key = `enemy_${enemyType}_${size}`;
        if (this.spriteCache.has(key)) {
            return this.spriteCache.get(key);
        }
        
        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;
        const ctx = canvas.getContext('2d');
        
        const centerX = size / 2;
        const centerY = size / 2;
        
        // Different enemy types
        switch (enemyType) {
            case 'goblin':
                // Green body
                ctx.fillStyle = '#66aa66';
                ctx.beginPath();
                ctx.arc(centerX, centerY, size * 0.25, 0, Math.PI * 2);
                ctx.fill();
                
                // Red eyes
                ctx.fillStyle = '#ff0000';
                ctx.fillRect(centerX - 4, centerY - 2, 2, 2);
                ctx.fillRect(centerX + 2, centerY - 2, 2, 2);
                break;
                
            case 'skeleton':
                // White bones
                ctx.strokeStyle = '#ffffff';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(centerX, centerY, size * 0.25, 0, Math.PI * 2);
                ctx.stroke();
                
                // Skull features
                ctx.fillStyle = '#000000';
                ctx.fillRect(centerX - 4, centerY - 2, 2, 3);
                ctx.fillRect(centerX + 2, centerY - 2, 2, 3);
                break;
                
            case 'orc':
                // Dark green body
                ctx.fillStyle = '#336633';
                ctx.fillRect(centerX - size * 0.3, centerY - size * 0.3, size * 0.6, size * 0.6);
                
                // Tusks
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(centerX - 2, centerY + 2, 1, 4);
                ctx.fillRect(centerX + 1, centerY + 2, 1, 4);
                break;
                
            default:
                // Generic enemy (red circle)
                ctx.fillStyle = this.colors.enemy;
                ctx.beginPath();
                ctx.arc(centerX, centerY, size * 0.3, 0, Math.PI * 2);
                ctx.fill();
        }
        
        this.spriteCache.set(key, canvas);
        return canvas;
    }
    
    generateItemSprite(itemType, size = 24) {
        const key = `item_${itemType}_${size}`;
        if (this.spriteCache.has(key)) {
            return this.spriteCache.get(key);
        }
        
        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;
        const ctx = canvas.getContext('2d');
        
        const centerX = size / 2;
        const centerY = size / 2;
        
        switch (itemType) {
            case 'sword':
                ctx.strokeStyle = '#cccccc';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(centerX, centerY - size * 0.4);
                ctx.lineTo(centerX, centerY + size * 0.2);
                ctx.stroke();
                
                // Crossguard
                ctx.beginPath();
                ctx.moveTo(centerX - size * 0.2, centerY - size * 0.1);
                ctx.lineTo(centerX + size * 0.2, centerY - size * 0.1);
                ctx.stroke();
                break;
                
            case 'shield':
                ctx.fillStyle = '#8B4513';
                ctx.beginPath();
                ctx.arc(centerX, centerY, size * 0.3, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.strokeStyle = '#FFD700';
                ctx.lineWidth = 1;
                ctx.stroke();
                break;
                
            case 'potion':
                ctx.fillStyle = '#ff0066';
                ctx.fillRect(centerX - 3, centerY - 2, 6, 8);
                
                ctx.fillStyle = '#666666';
                ctx.fillRect(centerX - 2, centerY - 6, 4, 4);
                break;
                
            case 'gold':
                ctx.fillStyle = this.colors.gold;
                ctx.beginPath();
                ctx.arc(centerX, centerY, size * 0.2, 0, Math.PI * 2);
                ctx.fill();
                
                // Shine effect
                const shine = Math.sin(this.time * 0.005) * 0.3 + 0.7;
                ctx.fillStyle = `rgba(255, 255, 255, ${shine})`;
                ctx.fillRect(centerX - 2, centerY - 2, 2, 2);
                break;
        }
        
        this.spriteCache.set(key, canvas);
        return canvas;
    }
    
    // Main rendering methods
    renderWorld(gameState) {
        const tileSize = 32;
        const tilesX = Math.floor(this.width / tileSize);
        const tilesY = Math.floor(this.height / tileSize);
        
        // Draw floor tiles
        for (let x = 0; x < tilesX; x++) {
            for (let y = 0; y < tilesY; y++) {
                const tileX = x * tileSize;
                const tileY = y * tileSize;
                
                // Checkerboard pattern
                const isLight = (x + y) % 2 === 0;
                const color = isLight ? '#1a1a1a' : '#0f0f0f';
                this.drawRect(tileX, tileY, tileSize, tileSize, color);
                
                // Add some noise for texture
                if (Math.random() < 0.1) {
                    const noiseColor = isLight ? '#222222' : '#111111';
                    this.drawRect(
                        tileX + Math.random() * tileSize,
                        tileY + Math.random() * tileSize,
                        2, 2, noiseColor
                    );
                }
            }
        }
        
        // Draw room boundaries
        this.ctx.strokeStyle = this.colors.wall;
        this.ctx.lineWidth = 3;
        this.ctx.strokeRect(10, 10, this.width - 20, this.height - 20);
        
        // Draw location info
        this.drawText(
            gameState.currentLocation.name,
            20, 40,
            '#ffff00', 18
        );
        
        this.drawText(
            `Поверх ${gameState.currentFloor} - Кімната ${gameState.currentRoom}`,
            20, 65,
            '#cccccc', 14
        );
    }
    
    renderPlayer(player) {
        const sprite = this.generatePlayerSprite(48);
        const x = this.width * 0.3;
        const y = this.height * 0.5;
        
        // Add breathing animation
        const breathe = Math.sin(this.time * 0.003) * 2;
        this.ctx.drawImage(sprite, x - 24, y - 24 + breathe);
        
        // Player name
        this.drawText(
            player.name,
            x, y - 40,
            this.colors.player, 14, 'center'
        );
        
        // Level indicator
        this.drawText(
            `Рівень ${player.level}`,
            x, y + 40,
            '#ffff00', 12, 'center'
        );
    }
    
    renderEnemy(enemy) {
        const sprite = this.generateEnemySprite(enemy.type, 48);
        const x = this.width * 0.7;
        const y = this.height * 0.5;
        
        // Add combat shake
        const shake = enemy.isInCombat ? (Math.random() - 0.5) * 4 : 0;
        this.ctx.drawImage(sprite, x - 24 + shake, y - 24 + shake);
        
        // Enemy name
        this.drawText(
            enemy.name,
            x, y - 40,
            this.colors.enemy, 14, 'center'
        );
        
        // Health bar
        const barWidth = 60;
        const barHeight = 6;
        const healthPercent = enemy.hp / enemy.maxHp;
        
        this.drawRect(x - barWidth/2, y + 30, barWidth, barHeight, '#333333');
        this.drawRect(x - barWidth/2, y + 30, barWidth * healthPercent, barHeight, '#ff4444');
    }
    
    renderEffects(effects) {
        // Update and render particle system
        if (this.particleSystem) {
            this.particleSystem.render(this.ctx);
        }

        effects.forEach(effect => {
            switch (effect.type) {
                case 'damage':
                    this.renderDamageNumber(effect);
                    break;
                case 'heal':
                    this.renderHealEffect(effect);
                    break;
                case 'levelup':
                    this.renderLevelUpEffect(effect);
                    break;
            }
        });
    }

    // Update particles
    updateParticles(deltaTime) {
        if (this.particleSystem) {
            this.particleSystem.update(deltaTime);
        }
    }

    // Create particle effects
    createDamageParticles(x, y, damage, type = 'damage') {
        if (this.particleSystem) {
            this.particleSystem.createDamageParticles(x, y, damage, type);
        }
    }

    createLevelUpParticles(x, y) {
        if (this.particleSystem) {
            this.particleSystem.createLevelUpParticles(x, y);
        }
    }

    createGoldParticles(x, y, amount) {
        if (this.particleSystem) {
            this.particleSystem.createGoldParticles(x, y, amount);
        }
    }

    createCombatParticles(x, y, type = 'hit') {
        if (this.particleSystem) {
            this.particleSystem.createCombatParticles(x, y, type);
        }
    }

    createItemDropParticles(x, y, rarity = 'common') {
        if (this.particleSystem) {
            this.particleSystem.createItemDropParticles(x, y, rarity);
        }
    }
    
    renderDamageNumber(effect) {
        const alpha = 1 - (effect.age / effect.duration);
        const y = effect.y - effect.age * 0.1;
        
        this.ctx.save();
        this.ctx.globalAlpha = alpha;
        this.drawText(
            `-${effect.damage}`,
            effect.x, y,
            '#ff6666', 20, 'center'
        );
        this.ctx.restore();
    }
    
    renderHealEffect(effect) {
        const alpha = 1 - (effect.age / effect.duration);
        const scale = 1 + (effect.age / effect.duration) * 0.5;
        
        this.ctx.save();
        this.ctx.globalAlpha = alpha;
        this.ctx.scale(scale, scale);
        this.drawText(
            `+${effect.amount}`,
            effect.x / scale, effect.y / scale,
            '#66ff66', 16, 'center'
        );
        this.ctx.restore();
    }
    
    renderLevelUpEffect(effect) {
        const alpha = Math.sin(effect.age * 0.01) * 0.5 + 0.5;
        
        this.ctx.save();
        this.ctx.globalAlpha = alpha;
        this.drawText(
            'РІВЕНЬ ПІДВИЩЕНО!',
            this.width / 2, this.height / 2 - 50,
            '#ffff00', 24, 'center'
        );
        this.ctx.restore();
    }
}

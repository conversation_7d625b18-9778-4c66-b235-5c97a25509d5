// Renderer module for Canvas 2D graphics
import { ParticleSystem } from './particleSystem.js';
import { SpriteCache, PerformanceMonitor } from './spriteCache.js';

export class Renderer {
    constructor(canvas, ctx) {
        this.canvas = canvas;
        this.ctx = ctx;
        this.width = canvas.width;
        this.height = canvas.height;

        // Initialize systems
        this.particleSystem = new ParticleSystem(this);
        this.spriteCache = new SpriteCache();
        this.performanceMonitor = new PerformanceMonitor();

        // Preload common sprites
        setTimeout(() => this.spriteCache.preloadCommonSprites(this), 100);
        
        // Animation time for effects
        this.time = 0;
        
        // Sprite cache
        this.spriteCache = new Map();
        
        // Colors
        this.colors = {
            background: '#000000',
            wall: '#444444',
            floor: '#222222',
            player: '#00ff00',
            enemy: '#ff4444',
            item: '#ffff00',
            gold: '#ffd700',
            health: '#ff0000',
            mana: '#0066ff',
            experience: '#9966ff'
        };

        // Biome definitions
        this.biomes = {
            cave: {
                name: 'Печера',
                backgroundColor: '#1a1a1a',
                wallColor: '#333333',
                floorColor: '#2a2a2a',
                ambientColor: '#444444',
                enemyColorMod: 0.8,
                itemColorMod: 0.9
            },
            forest: {
                name: 'Ліс',
                backgroundColor: '#0d2818',
                wallColor: '#2d4a3d',
                floorColor: '#1a3d2e',
                ambientColor: '#4a6b5c',
                enemyColorMod: 1.1,
                itemColorMod: 1.0
            },
            desert: {
                name: 'Пустеля',
                backgroundColor: '#2d2416',
                wallColor: '#5c4a2d',
                floorColor: '#4a3d24',
                ambientColor: '#6b5c3d',
                enemyColorMod: 1.2,
                itemColorMod: 0.8
            },
            ice: {
                name: 'Льодовики',
                backgroundColor: '#161d2d',
                wallColor: '#2d3d5c',
                floorColor: '#243d4a',
                ambientColor: '#3d5c6b',
                enemyColorMod: 0.9,
                itemColorMod: 1.1
            },
            volcano: {
                name: 'Вулкан',
                backgroundColor: '#2d1616',
                wallColor: '#5c2d2d',
                floorColor: '#4a2424',
                ambientColor: '#6b3d3d',
                enemyColorMod: 1.3,
                itemColorMod: 1.2
            },
            abyss: {
                name: 'Безодня',
                backgroundColor: '#0a0a0a',
                wallColor: '#1a1a1a',
                floorColor: '#151515',
                ambientColor: '#2a2a2a',
                enemyColorMod: 0.7,
                itemColorMod: 0.6
            }
        };
    }
    
    updateCanvasSize() {
        this.width = this.canvas.width;
        this.height = this.canvas.height;
    }

    // Get biome based on floor depth
    getBiomeForFloor(floor) {
        if (floor <= 5) return this.biomes.cave;
        if (floor <= 15) return this.biomes.forest;
        if (floor <= 25) return this.biomes.desert;
        if (floor <= 35) return this.biomes.ice;
        if (floor <= 50) return this.biomes.volcano;
        return this.biomes.abyss;
    }

    // Apply biome color modification
    applyBiomeColorMod(color, modifier) {
        const hex = color.replace('#', '');
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);

        const newR = Math.min(255, Math.floor(r * modifier));
        const newG = Math.min(255, Math.floor(g * modifier));
        const newB = Math.min(255, Math.floor(b * modifier));

        return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
    }
    
    clear(gameState = null) {
        // Use biome background if gameState is provided
        let backgroundColor = this.colors.background;
        if (gameState && gameState.currentFloor) {
            const biome = this.getBiomeForFloor(gameState.currentFloor);
            backgroundColor = biome.backgroundColor;
        }

        this.ctx.fillStyle = backgroundColor;
        this.ctx.fillRect(0, 0, this.width, this.height);
        this.time += 16; // Approximate 60fps
    }
    
    // Basic drawing primitives
    drawRect(x, y, width, height, color, filled = true) {
        this.ctx.strokeStyle = color;
        this.ctx.fillStyle = color;
        
        if (filled) {
            this.ctx.fillRect(x, y, width, height);
        } else {
            this.ctx.strokeRect(x, y, width, height);
        }
    }
    
    drawCircle(x, y, radius, color, filled = true) {
        this.ctx.beginPath();
        this.ctx.arc(x, y, radius, 0, Math.PI * 2);
        this.ctx.strokeStyle = color;
        this.ctx.fillStyle = color;
        
        if (filled) {
            this.ctx.fill();
        } else {
            this.ctx.stroke();
        }
    }
    
    drawText(text, x, y, color = '#ffffff', size = 16, align = 'left') {
        this.ctx.fillStyle = color;
        this.ctx.font = `${size}px 'Courier New', monospace`;
        this.ctx.textAlign = align;
        this.ctx.fillText(text, x, y);
    }
    
    // Procedural sprite generation
    generatePlayerSprite(player = {}, size = 32) {
        const params = {
            level: player.level || 1,
            equipment: player.equipment || {},
            size: size
        };

        return this.spriteCache.getSprite('player', params, () => {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');

            const centerX = size / 2;
            const centerY = size / 2;

            // Body color based on level
            const bodyColor = this.getPlayerColor(params.level);
            ctx.fillStyle = bodyColor;
            ctx.beginPath();
            ctx.arc(centerX, centerY, size * 0.3, 0, Math.PI * 2);
            ctx.fill();

            // Eyes (animated)
            const eyeOffset = Math.sin(this.time * 0.01) * 2;
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(centerX - 6, centerY - 4 + eyeOffset, 3, 3);
            ctx.fillRect(centerX + 3, centerY - 4 + eyeOffset, 3, 3);

            // Pupils
            ctx.fillStyle = '#000000';
            ctx.fillRect(centerX - 5, centerY - 3 + eyeOffset, 1, 1);
            ctx.fillRect(centerX + 4, centerY - 3 + eyeOffset, 1, 1);

            // Equipment visualization
            this.drawPlayerEquipment(ctx, centerX, centerY, size, params.equipment, params.level);

            return canvas;
        });
    }

    getPlayerColor(level) {
        const colors = ['#00ff00', '#00cc00', '#009900', '#006600', '#ffff00', '#ff9900', '#ff6600', '#ff0000'];
        const colorIndex = Math.min(Math.floor(level / 5), colors.length - 1);
        return colors[colorIndex];
    }

    drawPlayerEquipment(ctx, centerX, centerY, size, equipment, level) {
        const equipmentColor = this.getEquipmentColor(level);

        // Weapon (sword)
        ctx.strokeStyle = equipmentColor;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(centerX + size * 0.3, centerY - size * 0.2);
        ctx.lineTo(centerX + size * 0.5, centerY - size * 0.4);
        ctx.stroke();

        // Armor (if equipped)
        if (equipment.armor) {
            ctx.fillStyle = equipmentColor;
            ctx.fillRect(centerX - size * 0.15, centerY + size * 0.1, size * 0.3, size * 0.2);
        }
    }

    getEquipmentColor(level) {
        const colors = ['#cccccc', '#00ff00', '#0066ff', '#9966ff', '#ff6600'];
        const colorIndex = Math.min(Math.floor(level / 10), colors.length - 1);
        return colors[colorIndex];
    }
    
    generateEnemySprite(enemy, size = 32) {
        const params = {
            type: enemy.type || 'generic',
            level: enemy.level || 1,
            floor: enemy.floor || 1,
            size: size
        };

        return this.spriteCache.getSprite('enemy', params, () => {
            return this.createEnemyCanvas(params);
        });
    }

    createEnemyCanvas(params) {
        const { size } = params;

        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;
        const ctx = canvas.getContext('2d');

        const centerX = size / 2;
        const centerY = size / 2;

        // Get biome for color modification
        const biome = this.getBiomeForFloor(params.floor);

        // Generate enemy based on type with biome variations
        this.drawEnemyByType(ctx, centerX, centerY, size, params, biome);

        return canvas;
    }

    drawEnemyByType(ctx, centerX, centerY, size, params, biome) {
        const { type, level } = params;

        // Base colors modified by biome
        const baseColors = {
            goblin: '#669900',
            skeleton: '#cccccc',
            orc: '#996633',
            spider: '#330033',
            dragon: '#cc0000'
        };

        const baseColor = baseColors[type] || this.colors.enemy;
        const modifiedColor = this.applyBiomeColorMod(baseColor, biome.enemyColorMod);

        // Size variation based on level
        const levelSizeBonus = 1 + (level - 1) * 0.1;
        const enemySize = size * 0.25 * levelSizeBonus;

        switch (type) {
            case 'goblin':
                this.drawGoblin(ctx, centerX, centerY, enemySize, modifiedColor, level);
                break;
            case 'skeleton':
                this.drawSkeleton(ctx, centerX, centerY, enemySize, modifiedColor, level);
                break;
            case 'orc':
                this.drawOrc(ctx, centerX, centerY, enemySize, modifiedColor, level);
                break;
            default:
                this.drawGenericEnemy(ctx, centerX, centerY, enemySize, modifiedColor, level);
        }
    }

    drawGoblin(ctx, x, y, size, color, level) {
        // Body
        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.arc(x, y, size, 0, Math.PI * 2);
        ctx.fill();

        // Eyes
        ctx.fillStyle = level > 5 ? '#ff6600' : '#ff0000';
        ctx.fillRect(x - size * 0.3, y - size * 0.2, size * 0.2, size * 0.2);
        ctx.fillRect(x + size * 0.1, y - size * 0.2, size * 0.2, size * 0.2);

        // Weapon for higher levels
        if (level > 3) {
            ctx.strokeStyle = '#666666';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(x + size * 1.2, y - size * 0.5);
            ctx.lineTo(x + size * 1.5, y - size * 1.2);
            ctx.stroke();
        }
    }

    drawSkeleton(ctx, x, y, size, color, level) {
        // Skull
        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.arc(x, y, size, 0, Math.PI * 2);
        ctx.fill();

        // Eye sockets
        ctx.fillStyle = '#000000';
        ctx.fillRect(x - size * 0.3, y - size * 0.2, size * 0.25, size * 0.3);
        ctx.fillRect(x + size * 0.05, y - size * 0.2, size * 0.25, size * 0.3);

        // Glowing eyes for higher levels
        if (level > 5) {
            ctx.fillStyle = '#00ff00';
            ctx.fillRect(x - size * 0.25, y - size * 0.1, size * 0.1, size * 0.1);
            ctx.fillRect(x + size * 0.15, y - size * 0.1, size * 0.1, size * 0.1);
        }
    }

    drawOrc(ctx, x, y, size, color, level) {
        // Body
        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.arc(x, y, size, 0, Math.PI * 2);
        ctx.fill();

        // Tusks
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(x - size * 0.2, y + size * 0.2, size * 0.1, size * 0.4);
        ctx.fillRect(x + size * 0.1, y + size * 0.2, size * 0.1, size * 0.4);

        // Eyes
        ctx.fillStyle = '#ff0000';
        ctx.fillRect(x - size * 0.3, y - size * 0.2, size * 0.15, size * 0.15);
        ctx.fillRect(x + size * 0.15, y - size * 0.2, size * 0.15, size * 0.15);
    }

    drawGenericEnemy(ctx, x, y, size, color, level) {
        // Body
        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.arc(x, y, size, 0, Math.PI * 2);
        ctx.fill();

        // Eyes
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(x - size * 0.3, y - size * 0.2, size * 0.2, size * 0.2);
        ctx.fillRect(x + size * 0.1, y - size * 0.2, size * 0.2, size * 0.2);
    }
    
    generateItemSprite(itemType, size = 24) {
        const key = `item_${itemType}_${size}`;
        if (this.spriteCache.has(key)) {
            return this.spriteCache.get(key);
        }
        
        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;
        const ctx = canvas.getContext('2d');
        
        const centerX = size / 2;
        const centerY = size / 2;
        
        switch (itemType) {
            case 'sword':
                ctx.strokeStyle = '#cccccc';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(centerX, centerY - size * 0.4);
                ctx.lineTo(centerX, centerY + size * 0.2);
                ctx.stroke();
                
                // Crossguard
                ctx.beginPath();
                ctx.moveTo(centerX - size * 0.2, centerY - size * 0.1);
                ctx.lineTo(centerX + size * 0.2, centerY - size * 0.1);
                ctx.stroke();
                break;
                
            case 'shield':
                ctx.fillStyle = '#8B4513';
                ctx.beginPath();
                ctx.arc(centerX, centerY, size * 0.3, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.strokeStyle = '#FFD700';
                ctx.lineWidth = 1;
                ctx.stroke();
                break;
                
            case 'potion':
                ctx.fillStyle = '#ff0066';
                ctx.fillRect(centerX - 3, centerY - 2, 6, 8);
                
                ctx.fillStyle = '#666666';
                ctx.fillRect(centerX - 2, centerY - 6, 4, 4);
                break;
                
            case 'gold':
                ctx.fillStyle = this.colors.gold;
                ctx.beginPath();
                ctx.arc(centerX, centerY, size * 0.2, 0, Math.PI * 2);
                ctx.fill();
                
                // Shine effect
                const shine = Math.sin(this.time * 0.005) * 0.3 + 0.7;
                ctx.fillStyle = `rgba(255, 255, 255, ${shine})`;
                ctx.fillRect(centerX - 2, centerY - 2, 2, 2);
                break;
        }
        
        this.spriteCache.set(key, canvas);
        return canvas;
    }
    
    // Main rendering methods
    renderWorld(gameState) {
        const tileSize = 32;
        const tilesX = Math.floor(this.width / tileSize);
        const tilesY = Math.floor(this.height / tileSize);
        
        // Draw floor tiles
        for (let x = 0; x < tilesX; x++) {
            for (let y = 0; y < tilesY; y++) {
                const tileX = x * tileSize;
                const tileY = y * tileSize;
                
                // Checkerboard pattern
                const isLight = (x + y) % 2 === 0;
                const color = isLight ? '#1a1a1a' : '#0f0f0f';
                this.drawRect(tileX, tileY, tileSize, tileSize, color);
                
                // Add some noise for texture
                if (Math.random() < 0.1) {
                    const noiseColor = isLight ? '#222222' : '#111111';
                    this.drawRect(
                        tileX + Math.random() * tileSize,
                        tileY + Math.random() * tileSize,
                        2, 2, noiseColor
                    );
                }
            }
        }
        
        // Draw room boundaries
        this.ctx.strokeStyle = this.colors.wall;
        this.ctx.lineWidth = 3;
        this.ctx.strokeRect(10, 10, this.width - 20, this.height - 20);
        
        // Draw location info
        this.drawText(
            gameState.currentLocation.name,
            20, 40,
            '#ffff00', 18
        );
        
        this.drawText(
            `Поверх ${gameState.currentFloor} - Кімната ${gameState.currentRoom}`,
            20, 65,
            '#cccccc', 14
        );
    }
    
    renderPlayer(player) {
        const sprite = this.generatePlayerSprite(48);
        const x = this.width * 0.3;
        const y = this.height * 0.5;
        
        // Add breathing animation
        const breathe = Math.sin(this.time * 0.003) * 2;
        this.ctx.drawImage(sprite, x - 24, y - 24 + breathe);
        
        // Player name
        this.drawText(
            player.name,
            x, y - 40,
            this.colors.player, 14, 'center'
        );
        
        // Level indicator
        this.drawText(
            `Рівень ${player.level}`,
            x, y + 40,
            '#ffff00', 12, 'center'
        );
    }
    
    renderEnemy(enemy) {
        const sprite = this.generateEnemySprite(enemy.type, 48);
        const x = this.width * 0.7;
        const y = this.height * 0.5;
        
        // Add combat shake
        const shake = enemy.isInCombat ? (Math.random() - 0.5) * 4 : 0;
        this.ctx.drawImage(sprite, x - 24 + shake, y - 24 + shake);
        
        // Enemy name
        this.drawText(
            enemy.name,
            x, y - 40,
            this.colors.enemy, 14, 'center'
        );
        
        // Health bar
        const barWidth = 60;
        const barHeight = 6;
        const healthPercent = enemy.hp / enemy.maxHp;
        
        this.drawRect(x - barWidth/2, y + 30, barWidth, barHeight, '#333333');
        this.drawRect(x - barWidth/2, y + 30, barWidth * healthPercent, barHeight, '#ff4444');
    }
    
    renderEffects(effects) {
        // Update and render particle system
        if (this.particleSystem) {
            this.particleSystem.render(this.ctx);
        }

        effects.forEach(effect => {
            switch (effect.type) {
                case 'damage':
                    this.renderDamageNumber(effect);
                    break;
                case 'heal':
                    this.renderHealEffect(effect);
                    break;
                case 'levelup':
                    this.renderLevelUpEffect(effect);
                    break;
            }
        });
    }

    // Update particles
    updateParticles(deltaTime) {
        if (this.particleSystem) {
            this.particleSystem.update(deltaTime);
        }
    }

    // Create particle effects
    createDamageParticles(x, y, damage, type = 'damage') {
        if (this.particleSystem) {
            this.particleSystem.createDamageParticles(x, y, damage, type);
        }
    }

    createLevelUpParticles(x, y) {
        if (this.particleSystem) {
            this.particleSystem.createLevelUpParticles(x, y);
        }
    }

    createGoldParticles(x, y, amount) {
        if (this.particleSystem) {
            this.particleSystem.createGoldParticles(x, y, amount);
        }
    }

    createCombatParticles(x, y, type = 'hit') {
        if (this.particleSystem) {
            this.particleSystem.createCombatParticles(x, y, type);
        }
    }

    createItemDropParticles(x, y, rarity = 'common') {
        if (this.particleSystem) {
            this.particleSystem.createItemDropParticles(x, y, rarity);
        }
    }
    
    renderDamageNumber(effect) {
        const alpha = 1 - (effect.age / effect.duration);
        const y = effect.y - effect.age * 0.1;
        
        this.ctx.save();
        this.ctx.globalAlpha = alpha;
        this.drawText(
            `-${effect.damage}`,
            effect.x, y,
            '#ff6666', 20, 'center'
        );
        this.ctx.restore();
    }
    
    renderHealEffect(effect) {
        const alpha = 1 - (effect.age / effect.duration);
        const scale = 1 + (effect.age / effect.duration) * 0.5;
        
        this.ctx.save();
        this.ctx.globalAlpha = alpha;
        this.ctx.scale(scale, scale);
        this.drawText(
            `+${effect.amount}`,
            effect.x / scale, effect.y / scale,
            '#66ff66', 16, 'center'
        );
        this.ctx.restore();
    }
    
    renderLevelUpEffect(effect) {
        const alpha = Math.sin(effect.age * 0.01) * 0.5 + 0.5;
        
        this.ctx.save();
        this.ctx.globalAlpha = alpha;
        this.drawText(
            'РІВЕНЬ ПІДВИЩЕНО!',
            this.width / 2, this.height / 2 - 50,
            '#ffff00', 24, 'center'
        );
        this.ctx.restore();
    }
}

// Renderer module for Canvas 2D graphics
import { ParticleSystem } from './particleSystem.js';
import { SpriteCache, PerformanceMonitor } from './spriteCache.js';

export class Renderer {
    constructor(canvas, ctx) {
        this.canvas = canvas;
        this.ctx = ctx;
        this.width = canvas.width;
        this.height = canvas.height;

        // Initialize systems
        this.particleSystem = new ParticleSystem(this);
        this.spriteCache = { cache: new Map() }; // Simple cache implementation
        this.performanceMonitor = new PerformanceMonitor();

        // Simple sprite cache is ready
        
        // Animation time for effects
        this.time = 0;
        
        // Sprite cache
        this.spriteCache = new Map();
        
        // Colors
        this.colors = {
            background: '#000000',
            wall: '#444444',
            floor: '#222222',
            player: '#00ff00',
            enemy: '#ff4444',
            item: '#ffff00',
            gold: '#ffd700',
            health: '#ff0000',
            mana: '#0066ff',
            experience: '#9966ff'
        };

        // Biome definitions
        this.biomes = {
            cave: {
                name: 'Печера',
                backgroundColor: '#2a2a2a',
                wallColor: '#444444',
                floorColor: '#3a3a3a',
                ambientColor: '#555555',
                enemyColorMod: 0.8,
                itemColorMod: 0.9
            },
            forest: {
                name: 'Ліс',
                backgroundColor: '#1a4a1a',
                wallColor: '#3d6a3d',
                floorColor: '#2a5a2a',
                ambientColor: '#5a8a5a',
                enemyColorMod: 1.1,
                itemColorMod: 1.0
            },
            desert: {
                name: 'Пустеля',
                backgroundColor: '#4a3a1a',
                wallColor: '#7a6a3a',
                floorColor: '#6a5a2a',
                ambientColor: '#8a7a4a',
                enemyColorMod: 1.2,
                itemColorMod: 0.8
            },
            glacier: {
                name: 'Льодовики',
                backgroundColor: '#1a3a5a',
                wallColor: '#3a5a7a',
                floorColor: '#2a4a6a',
                ambientColor: '#4a6a8a',
                enemyColorMod: 0.9,
                itemColorMod: 1.1
            },
            volcano: {
                name: 'Вулкан',
                backgroundColor: '#5a1a1a',
                wallColor: '#7a3a3a',
                floorColor: '#6a2a2a',
                ambientColor: '#8a4a4a',
                enemyColorMod: 1.3,
                itemColorMod: 1.2
            },
            abyss: {
                name: 'Безодня',
                backgroundColor: '#2a1a4a',
                wallColor: '#4a3a6a',
                floorColor: '#3a2a5a',
                ambientColor: '#5a4a7a',
                enemyColorMod: 0.7,
                itemColorMod: 0.6
            }
        };
    }
    
    updateCanvasSize() {
        this.width = this.canvas.width;
        this.height = this.canvas.height;
    }

    // Get biome based on floor depth
    getBiomeForFloor(floor) {
        if (floor <= 5) return this.biomes.cave;
        if (floor <= 15) return this.biomes.forest;
        if (floor <= 25) return this.biomes.desert;
        if (floor <= 35) return this.biomes.glacier;
        if (floor <= 50) return this.biomes.volcano;
        return this.biomes.abyss;
    }

    // Apply biome color modification
    applyBiomeColorMod(color, modifier) {
        const hex = color.replace('#', '');
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);

        const newR = Math.min(255, Math.floor(r * modifier));
        const newG = Math.min(255, Math.floor(g * modifier));
        const newB = Math.min(255, Math.floor(b * modifier));

        return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
    }
    
    clear(gameState = null) {
        try {
            // Create gradient background based on biome
            let biome = this.biomes.cave; // Default
            if (gameState && gameState.currentFloor) {
                biome = this.getBiomeForFloor(gameState.currentFloor);
                console.log(`[Renderer] Current biome: ${biome.name} (floor ${gameState.currentFloor}), color: ${biome.backgroundColor}`);
            }

            // Create radial gradient for atmospheric effect
            const gradient = this.ctx.createRadialGradient(
                this.width / 2, this.height / 2, 0,
                this.width / 2, this.height / 2, Math.max(this.width, this.height) / 2
            );

            gradient.addColorStop(0, biome.backgroundColor);
            gradient.addColorStop(0.7, this.darkenColor(biome.backgroundColor, 0.8));
            gradient.addColorStop(1, this.darkenColor(biome.backgroundColor, 0.6));

            this.ctx.fillStyle = gradient;
            this.ctx.fillRect(0, 0, this.width, this.height);

            // Add visual indicator that gradient is working
            this.ctx.fillStyle = 'rgba(255, 255, 0, 0.1)';
            this.ctx.fillRect(10, 10, 100, 20);
            this.ctx.fillStyle = '#ffff00';
            this.ctx.font = '12px monospace';
            this.ctx.fillText(`Biome: ${biome.name}`, 15, 25);

            this.time += 16; // Approximate 60fps
        } catch (error) {
            console.error('[Renderer] Error in clear():', error);
            // Fallback to simple black background
            this.ctx.fillStyle = '#000000';
            this.ctx.fillRect(0, 0, this.width, this.height);
        }
    }
    
    // Basic drawing primitives
    drawRect(x, y, width, height, color, filled = true) {
        this.ctx.strokeStyle = color;
        this.ctx.fillStyle = color;
        
        if (filled) {
            this.ctx.fillRect(x, y, width, height);
        } else {
            this.ctx.strokeRect(x, y, width, height);
        }
    }
    
    drawCircle(x, y, radius, color, filled = true) {
        this.ctx.beginPath();
        this.ctx.arc(x, y, radius, 0, Math.PI * 2);
        this.ctx.strokeStyle = color;
        this.ctx.fillStyle = color;
        
        if (filled) {
            this.ctx.fill();
        } else {
            this.ctx.stroke();
        }
    }
    
    drawText(text, x, y, color = '#ffffff', size = 16, align = 'left') {
        this.ctx.fillStyle = color;
        this.ctx.font = `${size}px 'Courier New', monospace`;
        this.ctx.textAlign = align;
        this.ctx.fillText(text, x, y);
    }
    
    // Procedural sprite generation
    generatePlayerSprite(player = {}, size = 32) {
        const key = `player_${player.level || 1}_${size}`;

        // Check cache first
        if (this.spriteCache && this.spriteCache.cache && this.spriteCache.cache.has(key)) {
            return this.spriteCache.cache.get(key);
        }

        // Generate new sprite
        const sprite = this.createPlayerSprite(player, size);

        // Cache it
        if (this.spriteCache && this.spriteCache.cache) {
            this.spriteCache.cache.set(key, sprite);
        }

        return sprite;
    }

    createPlayerSprite(player = {}, size = 32) {
        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;
        const ctx = canvas.getContext('2d');

        const centerX = size / 2;
        const centerY = size / 2;

        // Body color based on level
        const bodyColor = this.getPlayerColor(player.level || 1);
        ctx.fillStyle = bodyColor;
        ctx.beginPath();
        ctx.arc(centerX, centerY, size * 0.3, 0, Math.PI * 2);
        ctx.fill();

        // Eyes (animated)
        const eyeOffset = Math.sin(this.time * 0.01) * 2;
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(centerX - 6, centerY - 4 + eyeOffset, 3, 3);
        ctx.fillRect(centerX + 3, centerY - 4 + eyeOffset, 3, 3);

        // Pupils
        ctx.fillStyle = '#000000';
        ctx.fillRect(centerX - 5, centerY - 3 + eyeOffset, 1, 1);
        ctx.fillRect(centerX + 4, centerY - 3 + eyeOffset, 1, 1);

        // Equipment visualization
        this.drawPlayerEquipment(ctx, centerX, centerY, size, player.equipment || {}, player.level || 1);

        return canvas;
    }

    getPlayerColor(level) {
        const colors = ['#00ff00', '#00cc00', '#009900', '#006600', '#ffff00', '#ff9900', '#ff6600', '#ff0000'];
        const colorIndex = Math.min(Math.floor(level / 5), colors.length - 1);
        return colors[colorIndex];
    }

    drawPlayerEquipment(ctx, centerX, centerY, size, equipment, level) {
        const equipmentColor = this.getEquipmentColor(level);

        // Weapon (sword)
        ctx.strokeStyle = equipmentColor;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(centerX + size * 0.3, centerY - size * 0.2);
        ctx.lineTo(centerX + size * 0.5, centerY - size * 0.4);
        ctx.stroke();

        // Armor (if equipped)
        if (equipment.armor) {
            ctx.fillStyle = equipmentColor;
            ctx.fillRect(centerX - size * 0.15, centerY + size * 0.1, size * 0.3, size * 0.2);
        }
    }

    getEquipmentColor(level) {
        const colors = ['#cccccc', '#00ff00', '#0066ff', '#9966ff', '#ff6600'];
        const colorIndex = Math.min(Math.floor(level / 10), colors.length - 1);
        return colors[colorIndex];
    }
    
    generateEnemySprite(enemy, size = 32) {
        const key = `enemy_${enemy.type || 'generic'}_${enemy.level || 1}_${size}`;

        // Check cache first
        if (this.spriteCache && this.spriteCache.cache && this.spriteCache.cache.has(key)) {
            return this.spriteCache.cache.get(key);
        }

        // Generate new sprite
        const sprite = this.createEnemyCanvas(enemy, size);

        // Cache it
        if (this.spriteCache && this.spriteCache.cache) {
            this.spriteCache.cache.set(key, sprite);
        }

        return sprite;
    }

    createEnemyCanvas(enemy, size = 32) {
        const params = {
            type: enemy.type || 'generic',
            level: enemy.level || 1,
            floor: enemy.floor || 1,
            size: size
        };

        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;
        const ctx = canvas.getContext('2d');

        const centerX = size / 2;
        const centerY = size / 2;

        // Get biome for color modification
        const biome = this.getBiomeForFloor(params.floor);

        // Generate enemy based on type with biome variations
        this.drawEnemyByType(ctx, centerX, centerY, size, params, biome);

        return canvas;
    }

    drawEnemyByType(ctx, centerX, centerY, size, params, biome) {
        const { type, level } = params;

        // Base colors modified by biome
        const baseColors = {
            goblin: '#669900',
            skeleton: '#cccccc',
            orc: '#996633',
            spider: '#330033',
            dragon: '#cc0000'
        };

        const baseColor = baseColors[type] || this.colors.enemy;
        const modifiedColor = this.applyBiomeColorMod(baseColor, biome.enemyColorMod);

        // Size variation based on level
        const levelSizeBonus = 1 + (level - 1) * 0.1;
        const enemySize = size * 0.25 * levelSizeBonus;

        switch (type) {
            case 'goblin':
                this.drawGoblin(ctx, centerX, centerY, enemySize, modifiedColor, level);
                break;
            case 'skeleton':
                this.drawSkeleton(ctx, centerX, centerY, enemySize, modifiedColor, level);
                break;
            case 'orc':
                this.drawOrc(ctx, centerX, centerY, enemySize, modifiedColor, level);
                break;
            default:
                this.drawGenericEnemy(ctx, centerX, centerY, enemySize, modifiedColor, level);
        }
    }

    drawGoblin(ctx, x, y, size, color, level) {
        // Body
        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.arc(x, y, size, 0, Math.PI * 2);
        ctx.fill();

        // Eyes
        ctx.fillStyle = level > 5 ? '#ff6600' : '#ff0000';
        ctx.fillRect(x - size * 0.3, y - size * 0.2, size * 0.2, size * 0.2);
        ctx.fillRect(x + size * 0.1, y - size * 0.2, size * 0.2, size * 0.2);

        // Weapon for higher levels
        if (level > 3) {
            ctx.strokeStyle = '#666666';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(x + size * 1.2, y - size * 0.5);
            ctx.lineTo(x + size * 1.5, y - size * 1.2);
            ctx.stroke();
        }
    }

    drawSkeleton(ctx, x, y, size, color, level) {
        // Skull
        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.arc(x, y, size, 0, Math.PI * 2);
        ctx.fill();

        // Eye sockets
        ctx.fillStyle = '#000000';
        ctx.fillRect(x - size * 0.3, y - size * 0.2, size * 0.25, size * 0.3);
        ctx.fillRect(x + size * 0.05, y - size * 0.2, size * 0.25, size * 0.3);

        // Glowing eyes for higher levels
        if (level > 5) {
            ctx.fillStyle = '#00ff00';
            ctx.fillRect(x - size * 0.25, y - size * 0.1, size * 0.1, size * 0.1);
            ctx.fillRect(x + size * 0.15, y - size * 0.1, size * 0.1, size * 0.1);
        }
    }

    drawOrc(ctx, x, y, size, color, level) {
        // Body
        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.arc(x, y, size, 0, Math.PI * 2);
        ctx.fill();

        // Tusks
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(x - size * 0.2, y + size * 0.2, size * 0.1, size * 0.4);
        ctx.fillRect(x + size * 0.1, y + size * 0.2, size * 0.1, size * 0.4);

        // Eyes
        ctx.fillStyle = '#ff0000';
        ctx.fillRect(x - size * 0.3, y - size * 0.2, size * 0.15, size * 0.15);
        ctx.fillRect(x + size * 0.15, y - size * 0.2, size * 0.15, size * 0.15);
    }

    drawGenericEnemy(ctx, x, y, size, color, level) {
        // Body
        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.arc(x, y, size, 0, Math.PI * 2);
        ctx.fill();

        // Eyes
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(x - size * 0.3, y - size * 0.2, size * 0.2, size * 0.2);
        ctx.fillRect(x + size * 0.1, y - size * 0.2, size * 0.2, size * 0.2);
    }
    
    generateItemSprite(itemType, size = 24) {
        const key = `item_${itemType}_${size}`;
        if (this.spriteCache.has(key)) {
            return this.spriteCache.get(key);
        }
        
        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;
        const ctx = canvas.getContext('2d');
        
        const centerX = size / 2;
        const centerY = size / 2;
        
        switch (itemType) {
            case 'sword':
                ctx.strokeStyle = '#cccccc';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(centerX, centerY - size * 0.4);
                ctx.lineTo(centerX, centerY + size * 0.2);
                ctx.stroke();
                
                // Crossguard
                ctx.beginPath();
                ctx.moveTo(centerX - size * 0.2, centerY - size * 0.1);
                ctx.lineTo(centerX + size * 0.2, centerY - size * 0.1);
                ctx.stroke();
                break;
                
            case 'shield':
                ctx.fillStyle = '#8B4513';
                ctx.beginPath();
                ctx.arc(centerX, centerY, size * 0.3, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.strokeStyle = '#FFD700';
                ctx.lineWidth = 1;
                ctx.stroke();
                break;
                
            case 'potion':
                ctx.fillStyle = '#ff0066';
                ctx.fillRect(centerX - 3, centerY - 2, 6, 8);
                
                ctx.fillStyle = '#666666';
                ctx.fillRect(centerX - 2, centerY - 6, 4, 4);
                break;
                
            case 'gold':
                ctx.fillStyle = this.colors.gold;
                ctx.beginPath();
                ctx.arc(centerX, centerY, size * 0.2, 0, Math.PI * 2);
                ctx.fill();
                
                // Shine effect
                const shine = Math.sin(this.time * 0.005) * 0.3 + 0.7;
                ctx.fillStyle = `rgba(255, 255, 255, ${shine})`;
                ctx.fillRect(centerX - 2, centerY - 2, 2, 2);
                break;
        }
        
        this.spriteCache.set(key, canvas);
        return canvas;
    }
    
    // Main rendering methods
    renderWorld(gameState) {
        // Get current biome for theming
        const biome = this.getBiomeForFloor(gameState.currentFloor);

        // Draw atmospheric background elements
        this.drawAtmosphericBackground(gameState, biome);

        // Draw floor tiles with biome-appropriate textures
        this.drawFloorTiles(gameState, biome);

        // Draw room boundaries
        this.drawRoomBoundaries(gameState, biome);

        // Draw characters and enemies
        this.drawCharacters(gameState);

        // Draw location info
        this.drawLocationInfo(gameState);

        // Draw atmospheric effects (particles, etc.)
        this.drawAtmosphericEffects(gameState, biome);
    }

    renderWorldWithoutFloor(gameState) {
        // Get current biome for theming
        const biome = this.getBiomeForFloor(gameState.currentFloor);

        // Draw atmospheric background elements
        this.drawAtmosphericBackground(gameState, biome);

        // Skip floor tiles to preserve gradient background

        // Draw room boundaries
        this.drawRoomBoundaries(gameState, biome);

        // Draw characters and enemies
        this.drawCharacters(gameState);

        // Draw location info
        this.drawLocationInfo(gameState);

        // Draw atmospheric effects (particles, etc.)
        this.drawAtmosphericEffects(gameState, biome);
    }

    drawAtmosphericBackground(gameState, biome) {
        // Add subtle atmospheric elements based on biome
        const time = this.time * 0.001;

        // console.log(`[Renderer] Drawing atmospheric background for ${biome.name}`);

        switch (biome.name) {
            case 'cave':
                this.drawCaveBackground(time);
                break;
            case 'forest':
                this.drawForestBackground(time);
                break;
            case 'desert':
                this.drawDesertBackground(time);
                break;
            case 'glacier':
                this.drawGlacierBackground(time);
                break;
            case 'volcano':
                this.drawVolcanoBackground(time);
                break;
            case 'abyss':
                this.drawAbyssBackground(time);
                break;
            default:
                console.warn(`[Renderer] Unknown biome: ${biome.name}`);
        }
    }

    drawFloorTiles(gameState, biome) {
        const tileSize = 32;
        const tilesX = Math.floor(this.width / tileSize);
        const tilesY = Math.floor(this.height / tileSize);

        for (let x = 0; x < tilesX; x++) {
            for (let y = 0; y < tilesY; y++) {
                const tileX = x * tileSize;
                const tileY = y * tileSize;

                // Biome-appropriate tile colors
                const isLight = (x + y) % 2 === 0;
                let lightColor, darkColor;

                switch (biome.name) {
                    case 'cave':
                        lightColor = 'rgba(42, 42, 42, 0.3)';
                        darkColor = 'rgba(26, 26, 26, 0.5)';
                        break;
                    case 'forest':
                        lightColor = 'rgba(26, 61, 26, 0.3)';
                        darkColor = 'rgba(13, 43, 13, 0.5)';
                        break;
                    case 'desert':
                        lightColor = 'rgba(139, 115, 85, 0.3)';
                        darkColor = 'rgba(107, 86, 53, 0.5)';
                        break;
                    case 'glacier':
                        lightColor = 'rgba(70, 130, 180, 0.3)';
                        darkColor = 'rgba(46, 90, 138, 0.5)';
                        break;
                    case 'volcano':
                        lightColor = 'rgba(74, 26, 26, 0.3)';
                        darkColor = 'rgba(42, 10, 10, 0.5)';
                        break;
                    case 'abyss':
                        lightColor = 'rgba(26, 0, 51, 0.3)';
                        darkColor = 'rgba(10, 0, 34, 0.5)';
                        break;
                    default:
                        lightColor = 'rgba(42, 42, 42, 0.3)';
                        darkColor = 'rgba(26, 26, 26, 0.5)';
                }

                const color = isLight ? lightColor : darkColor;
                this.ctx.fillStyle = color;
                this.ctx.fillRect(tileX, tileY, tileSize, tileSize);

                // Add texture details
                this.addTileTexture(tileX, tileY, tileSize, biome, isLight);
            }
        }
    }

    addTileTexture(x, y, size, biome, isLight) {
        const random = Math.random();

        if (random < 0.15) {
            let textureColor;
            switch (biome.name) {
                case 'cave':
                    textureColor = isLight ? '#333333' : '#222222';
                    // Draw small rocks
                    this.ctx.fillStyle = textureColor;
                    this.ctx.fillRect(x + random * size, y + random * size, 2, 2);
                    break;
                case 'forest':
                    textureColor = '#2d5a2d';
                    // Draw small leaves or twigs
                    this.ctx.fillStyle = textureColor;
                    this.ctx.fillRect(x + random * size, y + random * size, 3, 1);
                    break;
                case 'desert':
                    textureColor = '#daa520';
                    // Draw sand particles
                    this.ctx.fillStyle = textureColor;
                    this.ctx.fillRect(x + random * size, y + random * size, 1, 1);
                    break;
                case 'glacier':
                    textureColor = '#87ceeb';
                    // Draw ice crystals
                    this.ctx.fillStyle = textureColor;
                    this.ctx.fillRect(x + random * size, y + random * size, 2, 2);
                    break;
                case 'volcano':
                    textureColor = '#ff4500';
                    // Draw lava spots
                    this.ctx.fillStyle = textureColor;
                    this.ctx.fillRect(x + random * size, y + random * size, 1, 1);
                    break;
                case 'abyss':
                    textureColor = '#9966ff';
                    // Draw void particles
                    this.ctx.fillStyle = textureColor;
                    this.ctx.fillRect(x + random * size, y + random * size, 1, 1);
                    break;
            }
        }
    }
    
    renderEffects(effects) {
        // Update and render particle system
        if (this.particleSystem) {
            this.particleSystem.render(this.ctx);
        }

        effects.forEach(effect => {
            switch (effect.type) {
                case 'damage':
                    this.renderDamageNumber(effect);
                    break;
                case 'heal':
                    this.renderHealEffect(effect);
                    break;
                case 'levelup':
                    this.renderLevelUpEffect(effect);
                    break;
            }
        });
    }

    // Update particles
    updateParticles(deltaTime) {
        if (this.particleSystem) {
            this.particleSystem.update(deltaTime);
        }
    }

    // Create particle effects
    createDamageParticles(x, y, damage, type = 'damage') {
        if (this.particleSystem) {
            this.particleSystem.createDamageParticles(x, y, damage, type);
        }
    }

    createLevelUpParticles(x, y) {
        if (this.particleSystem) {
            this.particleSystem.createLevelUpParticles(x, y);
        }
    }

    createGoldParticles(x, y, amount) {
        if (this.particleSystem) {
            this.particleSystem.createGoldParticles(x, y, amount);
        }
    }

    createCombatParticles(x, y, type = 'hit') {
        if (this.particleSystem) {
            this.particleSystem.createCombatParticles(x, y, type);
        }
    }

    createItemDropParticles(x, y, rarity = 'common') {
        if (this.particleSystem) {
            this.particleSystem.createItemDropParticles(x, y, rarity);
        }
    }
    
    renderDamageNumber(effect) {
        const alpha = 1 - (effect.age / effect.duration);
        const y = effect.y - effect.age * 0.1;
        
        this.ctx.save();
        this.ctx.globalAlpha = alpha;
        this.drawText(
            `-${effect.damage}`,
            effect.x, y,
            '#ff6666', 20, 'center'
        );
        this.ctx.restore();
    }
    
    renderHealEffect(effect) {
        const alpha = 1 - (effect.age / effect.duration);
        const scale = 1 + (effect.age / effect.duration) * 0.5;
        
        this.ctx.save();
        this.ctx.globalAlpha = alpha;
        this.ctx.scale(scale, scale);
        this.drawText(
            `+${effect.amount}`,
            effect.x / scale, effect.y / scale,
            '#66ff66', 16, 'center'
        );
        this.ctx.restore();
    }
    
    renderLevelUpEffect(effect) {
        const alpha = Math.sin(effect.age * 0.01) * 0.5 + 0.5;
        
        this.ctx.save();
        this.ctx.globalAlpha = alpha;
        this.drawText(
            'РІВЕНЬ ПІДВИЩЕНО!',
            this.width / 2, this.height / 2 - 50,
            '#ffff00', 24, 'center'
        );
        this.ctx.restore();
    }

    drawRoomBoundaries(gameState, biome) {
        // Draw room boundaries with biome-appropriate styling
        this.ctx.strokeStyle = biome.wallColor || this.colors.wall;
        this.ctx.lineWidth = 3;
        this.ctx.strokeRect(10, 10, this.width - 20, this.height - 20);

        // Add corner decorations
        const cornerSize = 20;
        this.ctx.fillStyle = biome.accentColor || '#ffff00';

        // Top-left corner
        this.ctx.fillRect(10, 10, cornerSize, 3);
        this.ctx.fillRect(10, 10, 3, cornerSize);

        // Top-right corner
        this.ctx.fillRect(this.width - 30, 10, cornerSize, 3);
        this.ctx.fillRect(this.width - 13, 10, 3, cornerSize);

        // Bottom-left corner
        this.ctx.fillRect(10, this.height - 13, cornerSize, 3);
        this.ctx.fillRect(10, this.height - 30, 3, cornerSize);

        // Bottom-right corner
        this.ctx.fillRect(this.width - 30, this.height - 13, cornerSize, 3);
        this.ctx.fillRect(this.width - 13, this.height - 30, 3, cornerSize);
    }

    drawCharacters(gameState) {
        // console.log('[Renderer] Drawing characters...');

        // Draw player character
        this.drawPlayer(gameState);

        // Draw current enemy if in combat
        if (gameState.inCombat && gameState.currentEnemy) {
            // console.log('[Renderer] Drawing enemy:', gameState.currentEnemy.name);
            this.drawEnemy(gameState.currentEnemy);
        }

        // Draw NPCs if any
        if (gameState.currentLocation && gameState.currentLocation.npcs) {
            gameState.currentLocation.npcs.forEach(npc => {
                this.drawNPC(npc);
            });
        }
    }

    drawPlayer(gameState) {
        const player = gameState.player;
        const centerX = this.width / 2 - 100; // Offset left for enemy space
        const centerY = this.height / 2;

        // Player shadow
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        this.ctx.fillRect(centerX - 25, centerY + 35, 50, 10);

        // Player body (rectangle for now, can be improved with sprites)
        this.ctx.fillStyle = '#00ff00';
        this.ctx.fillRect(centerX - 20, centerY - 30, 40, 60);

        // Player outline
        this.ctx.strokeStyle = '#ffffff';
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(centerX - 20, centerY - 30, 40, 60);

        // Player face
        this.ctx.fillStyle = '#ffff00';
        this.ctx.fillRect(centerX - 15, centerY - 25, 30, 20);

        // Player eyes
        this.ctx.fillStyle = '#000000';
        this.ctx.fillRect(centerX - 10, centerY - 20, 4, 4);
        this.ctx.fillRect(centerX + 6, centerY - 20, 4, 4);

        // Player health bar
        this.drawHealthBar(centerX - 30, centerY - 50, 60, 8, player.hp, player.maxHp, '#00ff00');

        // Player name
        this.drawText('Герой', centerX, centerY - 60, '#ffffff', 14, 'center');

        // Equipment indicators
        this.drawEquipmentIndicators(centerX, centerY, player);
    }

    drawEnemy(enemy) {
        const centerX = this.width / 2 + 100; // Offset right
        const centerY = this.height / 2;

        // Enemy shadow
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        this.ctx.fillRect(centerX - 25, centerY + 35, 50, 10);

        // Enemy body (different color based on type)
        const enemyColor = this.getEnemyColor(enemy);
        this.ctx.fillStyle = enemyColor;
        this.ctx.fillRect(centerX - 20, centerY - 30, 40, 60);

        // Enemy outline
        this.ctx.strokeStyle = '#ff0000';
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(centerX - 20, centerY - 30, 40, 60);

        // Enemy face/features
        this.drawEnemyFeatures(centerX, centerY, enemy);

        // Enemy health bar
        this.drawHealthBar(centerX - 30, centerY - 50, 60, 8, enemy.hp, enemy.maxHp, '#ff0000');

        // Enemy name
        this.drawText(enemy.name, centerX, centerY - 60, '#ff0000', 14, 'center');

        // Enemy level
        this.drawText(`Рівень ${enemy.level}`, centerX, centerY + 50, '#ffaa00', 12, 'center');
    }

    drawNPC(npc) {
        // Simple NPC representation
        const x = npc.x || (this.width / 4);
        const y = npc.y || (this.height / 4);

        this.ctx.fillStyle = '#0066ff';
        this.ctx.fillRect(x - 15, y - 20, 30, 40);

        this.ctx.strokeStyle = '#ffffff';
        this.ctx.lineWidth = 1;
        this.ctx.strokeRect(x - 15, y - 20, 30, 40);

        this.drawText(npc.name, x, y - 30, '#0066ff', 12, 'center');
    }

    drawEquipmentIndicators(x, y, player) {
        // Show equipped weapon
        if (player.equipment && player.equipment.weapon) {
            this.ctx.fillStyle = '#ffff00';
            this.ctx.fillRect(x + 25, y - 10, 15, 3); // Sword indicator
        }

        // Show equipped shield
        if (player.equipment && player.equipment.shield) {
            this.ctx.fillStyle = '#0066ff';
            this.ctx.fillRect(x - 40, y - 10, 10, 20); // Shield indicator
        }
    }

    getEnemyColor(enemy) {
        // Color based on enemy type or level
        if (enemy.type === 'boss') return '#800080';
        if (enemy.level >= 20) return '#ff4500';
        if (enemy.level >= 10) return '#ff6600';
        return '#cc0000';
    }

    drawEnemyFeatures(x, y, enemy) {
        // Different features based on enemy type
        switch (enemy.type) {
            case 'goblin':
                // Green face
                this.ctx.fillStyle = '#228b22';
                this.ctx.fillRect(x - 15, y - 25, 30, 20);
                // Red eyes
                this.ctx.fillStyle = '#ff0000';
                this.ctx.fillRect(x - 10, y - 20, 3, 3);
                this.ctx.fillRect(x + 7, y - 20, 3, 3);
                break;
            case 'skeleton':
                // White skull
                this.ctx.fillStyle = '#ffffff';
                this.ctx.fillRect(x - 15, y - 25, 30, 20);
                // Black eye sockets
                this.ctx.fillStyle = '#000000';
                this.ctx.fillRect(x - 10, y - 20, 6, 6);
                this.ctx.fillRect(x + 4, y - 20, 6, 6);
                break;
            case 'orc':
                // Dark green face
                this.ctx.fillStyle = '#006400';
                this.ctx.fillRect(x - 15, y - 25, 30, 20);
                // Yellow eyes
                this.ctx.fillStyle = '#ffff00';
                this.ctx.fillRect(x - 10, y - 20, 4, 4);
                this.ctx.fillRect(x + 6, y - 20, 4, 4);
                break;
            default:
                // Generic enemy face
                this.ctx.fillStyle = '#8b0000';
                this.ctx.fillRect(x - 15, y - 25, 30, 20);
                this.ctx.fillStyle = '#ff0000';
                this.ctx.fillRect(x - 10, y - 20, 4, 4);
                this.ctx.fillRect(x + 6, y - 20, 4, 4);
        }
    }

    drawHealthBar(x, y, width, height, currentHp, maxHp, color) {
        // Background
        this.ctx.fillStyle = '#333333';
        this.ctx.fillRect(x, y, width, height);

        // Health fill
        const healthPercent = Math.max(0, currentHp / maxHp);
        const fillWidth = width * healthPercent;

        // Color based on health percentage
        let healthColor = color;
        if (healthPercent < 0.3) {
            healthColor = '#ff0000';
        } else if (healthPercent < 0.6) {
            healthColor = '#ffaa00';
        }

        this.ctx.fillStyle = healthColor;
        this.ctx.fillRect(x, y, fillWidth, height);

        // Border
        this.ctx.strokeStyle = '#ffffff';
        this.ctx.lineWidth = 1;
        this.ctx.strokeRect(x, y, width, height);

        // Health text
        this.drawText(
            `${Math.ceil(currentHp)}/${maxHp}`,
            x + width / 2, y + height + 12,
            '#ffffff', 10, 'center'
        );
    }

    drawLocationInfo(gameState) {
        // Location name with background
        const textBg = this.ctx.createLinearGradient(0, 20, 300, 20);
        textBg.addColorStop(0, 'rgba(0, 0, 0, 0.8)');
        textBg.addColorStop(1, 'rgba(0, 0, 0, 0)');

        this.ctx.fillStyle = textBg;
        this.ctx.fillRect(20, 25, 300, 45);

        this.drawText(
            gameState.currentLocation.name,
            25, 45,
            '#ffff00', 18
        );

        this.drawText(
            `Поверх ${gameState.currentFloor} - Кімната ${gameState.currentRoom}`,
            25, 65,
            '#cccccc', 14
        );
    }

    drawAtmosphericEffects(gameState, biome) {
        // Add subtle atmospheric effects based on biome
        const time = this.time * 0.001;

        switch (biome.name) {
            case 'glacier':
                this.drawSnowflakes(time);
                break;
            case 'volcano':
                this.drawEmbers(time);
                break;
            case 'abyss':
                this.drawVoidParticles(time);
                break;
        }
    }

    // Atmospheric background methods
    drawCaveBackground(time) {
        // Subtle stalactites shadows
        for (let i = 0; i < 5; i++) {
            const x = (i * this.width / 5) + Math.sin(time + i) * 10;
            const gradient = this.ctx.createLinearGradient(x, 0, x, 50);
            gradient.addColorStop(0, 'rgba(0, 0, 0, 0.3)');
            gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');

            this.ctx.fillStyle = gradient;
            this.ctx.fillRect(x - 5, 0, 10, 50);
        }
    }

    drawForestBackground(time) {
        // Subtle tree shadows
        for (let i = 0; i < 3; i++) {
            const x = (i * this.width / 3) + Math.sin(time * 0.5 + i) * 5;
            const gradient = this.ctx.createRadialGradient(x, this.height, 0, x, this.height, 100);
            gradient.addColorStop(0, 'rgba(0, 50, 0, 0.2)');
            gradient.addColorStop(1, 'rgba(0, 50, 0, 0)');

            this.ctx.fillStyle = gradient;
            this.ctx.fillRect(x - 50, this.height - 100, 100, 100);
        }
    }

    drawDesertBackground(time) {
        // Heat shimmer effect
        for (let i = 0; i < this.width; i += 20) {
            const shimmer = Math.sin(time * 2 + i * 0.1) * 2;
            this.ctx.fillStyle = `rgba(255, 200, 100, ${0.1 + shimmer * 0.05})`;
            this.ctx.fillRect(i, this.height - 50, 20, 50);
        }
    }

    drawGlacierBackground(time) {
        // Ice crystal reflections
        for (let i = 0; i < 8; i++) {
            const x = Math.random() * this.width;
            const y = Math.random() * this.height;
            const alpha = 0.1 + Math.sin(time + i) * 0.05;

            this.ctx.fillStyle = `rgba(135, 206, 235, ${alpha})`;
            this.ctx.fillRect(x, y, 2, 2);
        }
    }

    drawVolcanoBackground(time) {
        // Lava glow
        const gradient = this.ctx.createLinearGradient(0, this.height - 50, 0, this.height);
        gradient.addColorStop(0, 'rgba(255, 69, 0, 0)');
        gradient.addColorStop(1, `rgba(255, 69, 0, ${0.2 + Math.sin(time) * 0.1})`);

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, this.height - 50, this.width, 50);
    }

    drawAbyssBackground(time) {
        // Void energy
        for (let i = 0; i < 10; i++) {
            const x = Math.random() * this.width;
            const y = Math.random() * this.height;
            const alpha = 0.05 + Math.sin(time * 2 + i) * 0.03;

            this.ctx.fillStyle = `rgba(153, 102, 255, ${alpha})`;
            this.ctx.fillRect(x, y, 1, 1);
        }
    }

    drawSnowflakes(time) {
        for (let i = 0; i < 20; i++) {
            const x = (Math.sin(time * 0.5 + i) * 50 + i * this.width / 20) % this.width;
            const y = (time * 40 + i * 60) % this.height;
            const size = 2 + Math.sin(time + i) * 1;

            this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            this.ctx.fillRect(x, y, size, size);

            // Add sparkle effect
            if (Math.random() < 0.1) {
                this.ctx.fillStyle = 'rgba(200, 230, 255, 0.6)';
                this.ctx.fillRect(x - 1, y - 1, size + 2, size + 2);
            }
        }
    }

    drawEmbers(time) {
        for (let i = 0; i < 15; i++) {
            const x = Math.random() * this.width;
            const y = this.height - (time * 25 + i * 40) % this.height;
            const alpha = 0.6 + Math.sin(time * 3 + i) * 0.4;
            const size = 1 + Math.sin(time + i) * 1;

            // Main ember
            this.ctx.fillStyle = `rgba(255, 100, 0, ${alpha})`;
            this.ctx.fillRect(x, y, size, size);

            // Glow effect
            this.ctx.fillStyle = `rgba(255, 200, 0, ${alpha * 0.3})`;
            this.ctx.fillRect(x - 1, y - 1, size + 2, size + 2);
        }
    }

    drawVoidParticles(time) {
        for (let i = 0; i < 12; i++) {
            const x = Math.sin(time + i) * 120 + this.width / 2;
            const y = Math.cos(time * 0.7 + i) * 100 + this.height / 2;
            const alpha = 0.4 + Math.sin(time * 2 + i) * 0.3;
            const size = 2 + Math.sin(time * 1.5 + i) * 1;

            // Main void particle
            this.ctx.fillStyle = `rgba(153, 102, 255, ${alpha})`;
            this.ctx.fillRect(x, y, size, size);

            // Void aura
            this.ctx.fillStyle = `rgba(102, 51, 204, ${alpha * 0.4})`;
            this.ctx.fillRect(x - 2, y - 2, size + 4, size + 4);
        }
    }

    // Helper method to darken colors
    darkenColor(color, factor) {
        // Simple color darkening - can be improved
        if (color.startsWith('#')) {
            const r = parseInt(color.slice(1, 3), 16);
            const g = parseInt(color.slice(3, 5), 16);
            const b = parseInt(color.slice(5, 7), 16);

            return `rgb(${Math.floor(r * factor)}, ${Math.floor(g * factor)}, ${Math.floor(b * factor)})`;
        }
        return color;
    }
}

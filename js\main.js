// Main game entry point
import { Renderer } from './renderer.js';
import { AudioManager } from './audio.js';
import { GameState } from './gameState.js';
import { ContentGenerator } from './contentGenerator.js';
import { IdleManager } from './idleManager.js';
import { SaveManager } from './saveManager.js';
import { UIManager } from './ui.js';
import { SkillSystem } from './skillSystem.js';
import { BossSystem } from './bossSystem.js';
import { PrestigeSystem } from './prestigeSystem.js';

class Game {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        
        // Initialize game systems
        this.renderer = new Renderer(this.canvas, this.ctx);
        this.audio = new AudioManager();
        this.gameState = new GameState();
        this.contentGenerator = new ContentGenerator();
        this.skillSystem = new SkillSystem(this.gameState);
        this.bossSystem = new BossSystem(this.gameState, this.contentGenerator);
        this.prestigeSystem = new PrestigeSystem(this.gameState);
        this.idleManager = new IdleManager(this.gameState, this.contentGenerator);
        this.saveManager = new SaveManager(this.gameState);
        this.ui = new UIManager(this.gameState);
        
        // Game loop
        this.lastTime = 0;
        this.isRunning = false;
        
        // Bind methods
        this.gameLoop = this.gameLoop.bind(this);
        this.handleResize = this.handleResize.bind(this);
        
        this.init();
    }
    
    async init() {
        try {
            // Setup canvas
            this.handleResize();
            window.addEventListener('resize', this.handleResize);
            
            // Initialize audio
            await this.audio.init();

            // Initialize UI first
            this.ui.init();
            this.ui.setSystems(this.skillSystem, this.bossSystem, this.prestigeSystem);
            this.ui.initMobileSupport();

            // Load save data
            this.saveManager.loadGame();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Start game loop
            this.start();
            
            // Hide loading screen
            document.getElementById('loading').style.display = 'none';
            document.getElementById('gameContainer').style.display = 'flex';
            
            console.log('Game initialized successfully');
        } catch (error) {
            console.error('Failed to initialize game:', error);
        }
    }
    
    setupEventListeners() {
        // Next room button
        document.getElementById('nextRoomBtn').addEventListener('click', () => {
            this.gameState.nextRoom();
            this.ui.updateUI();
        });
        
        // Next floor button
        document.getElementById('nextFloorBtn').addEventListener('click', () => {
            this.gameState.nextFloor();
            this.ui.updateUI();
        });
        
        // Auto fight toggle
        document.getElementById('autoFightBtn').addEventListener('click', () => {
            this.idleManager.toggleAutoFight();
            this.ui.updateUI();
        });
        
        // Save game periodically
        setInterval(() => {
            this.saveManager.saveGame();
        }, 10000); // Save every 10 seconds
        
        // Handle page visibility for offline progress
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.saveManager.saveGame();
            } else {
                this.saveManager.calculateOfflineProgress();
                this.ui.updateUI();
            }
        });
        
        // Save before page unload
        window.addEventListener('beforeunload', () => {
            this.saveManager.saveGame();
        });
    }
    
    handleResize() {
        const container = document.getElementById('gameContainer');
        const ui = document.getElementById('ui');
        
        if (window.innerWidth <= 768) {
            // Mobile layout
            this.canvas.width = window.innerWidth - 4; // Account for borders
            this.canvas.height = window.innerHeight * 0.6 - 4;
        } else {
            // Desktop layout
            const uiWidth = ui.offsetWidth;
            this.canvas.width = window.innerWidth - uiWidth - 6; // Account for borders
            this.canvas.height = window.innerHeight - 4;
        }
        
        // Update renderer
        this.renderer.updateCanvasSize();
    }
    
    start() {
        this.isRunning = true;
        this.gameLoop(performance.now());
    }
    
    stop() {
        this.isRunning = false;
    }
    
    gameLoop(currentTime) {
        if (!this.isRunning) return;
        
        const deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        
        // Update game systems
        this.update(deltaTime);
        
        // Render
        this.render();
        
        // Continue loop
        requestAnimationFrame(this.gameLoop);
    }
    
    update(deltaTime) {
        // Update idle mechanics
        this.idleManager.update(deltaTime);

        // Update skill system
        this.skillSystem.update(deltaTime);

        // Update boss system
        this.bossSystem.update(deltaTime);

        // Update particles
        this.renderer.updateParticles(deltaTime);

        // Update audio based on game state
        this.audio.update(this.gameState);

        // Update UI if needed
        if (this.gameState.needsUIUpdate) {
            this.ui.updateUI();
            this.gameState.needsUIUpdate = false;
        }
    }
    
    render() {
        // Clear canvas
        this.renderer.clear();
        
        // Render game world
        this.renderer.renderWorld(this.gameState);
        
        // Render player
        this.renderer.renderPlayer(this.gameState.player);
        
        // Render enemies
        if (this.gameState.currentEnemy) {
            this.renderer.renderEnemy(this.gameState.currentEnemy);
        }
        
        // Render effects
        this.renderer.renderEffects(this.gameState.effects);
    }
}

// Start the game when page loads
window.addEventListener('load', () => {
    window.game = new Game();
});

// Export for debugging
window.Game = Game;

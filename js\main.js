// Main game entry point
import { Renderer } from './renderer.js';
import { AudioManager } from './audio.js';
import { GameState } from './gameState.js';
import { ContentGenerator } from './contentGenerator.js';
import { IdleManager } from './idleManager.js';
import { SaveManager } from './saveManager.js';
import { UIManager } from './ui.js';
import { SkillSystem } from './skillSystem.js';
import { BossSystem } from './bossSystem.js';
import { PrestigeSystem } from './prestigeSystem.js';
import { SettingsManager } from './settingsManager.js';
import { EquipmentSystem } from './equipmentSystem.js';
import { ShopSystem } from './shopSystem.js';
import { ConsumableSystem } from './consumableSystem.js';
import { ItemIconSystem } from './itemIconSystem.js';
import { BiomeBackgroundSystem } from './biomeBackgroundSystem.js';

class Game {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        
        // Initialize game systems
        this.renderer = new Renderer(this.canvas, this.ctx);
        this.audio = new AudioManager();
        this.gameState = new GameState();
        this.contentGenerator = new ContentGenerator();
        this.skillSystem = new SkillSystem(this.gameState);
        this.bossSystem = new BossSystem(this.gameState, this.contentGenerator);
        this.prestigeSystem = new PrestigeSystem(this.gameState);
        this.idleManager = new IdleManager(this.gameState, this.contentGenerator);
        this.iconSystem = new ItemIconSystem();
        this.equipmentSystem = new EquipmentSystem(this.gameState, this.renderer);
        this.consumableSystem = new ConsumableSystem(this.gameState);
        this.shopSystem = new ShopSystem(this.gameState, this.contentGenerator);
        this.biomeSystem = new BiomeBackgroundSystem(this.canvas, this.ctx);

        // Initialize UI after all systems are ready
        console.log('Initializing UI with iconSystem...');
        this.ui = new UIManager(this.gameState);

        // Pass iconSystem to UI
        this.ui.iconSystem = this.iconSystem;
        console.log('IconSystem passed to UI:', this.ui.iconSystem ? 'SUCCESS' : 'FAILED');

        // Initialize settings manager after audio is ready
        this.settingsManager = null;
        this.saveManager = new SaveManager(this.gameState);
        // UI will be initialized after other systems
        
        // Game loop
        this.lastTime = 0;
        this.isRunning = false;
        
        // Bind methods
        this.gameLoop = this.gameLoop.bind(this);
        this.handleResize = this.handleResize.bind(this);
        
        this.init();
    }
    
    async init() {
        try {
            // Setup canvas
            this.handleResize();
            window.addEventListener('resize', this.handleResize);
            
            // Initialize audio with fallback
            try {
                await this.audio.init();
            } catch (audioError) {
                console.warn('Audio initialization failed, continuing without audio:', audioError);
                this.audio.enabled = false;
            }

            // Initialize settings manager after audio is ready
            this.settingsManager = new SettingsManager(this.gameState, this.audio);

            // Initialize UI first
            this.ui.init();
            this.ui.setSystems(this.skillSystem, this.bossSystem, this.prestigeSystem, this.settingsManager, this.equipmentSystem);
            this.ui.initMobileSupport();

            // Load save data with error handling
            try {
                this.saveManager.loadGame();
            } catch (saveError) {
                console.warn('Failed to load save data, starting new game:', saveError);
                this.gameState.addLogEntry('Не вдалося завантажити збереження, розпочато нову гру', 'warning');
            }

            // Initialize equipment bonuses after loading
            this.equipmentSystem.syncFromGameState();

            // Sync auto settings between systems
            this.idleManager.autoFight = this.gameState.autoFight;
            this.idleManager.autoLoot = this.gameState.autoLoot;
            this.idleManager.autoExplore = this.gameState.autoProgress;
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Start game loop
            this.start();
            
            // Hide loading screen with animation
            this.showGameWithAnimation();
            
            console.log('Game initialized successfully');
        } catch (error) {
            console.error('Failed to initialize game:', error);
            this.showCriticalError('Критична помилка ініціалізації гри. Перезавантажте сторінку.');
        }
    }

    showCriticalError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #ff0000;
            color: #ffffff;
            padding: 20px;
            border-radius: 10px;
            z-index: 10000;
            text-align: center;
            font-family: Arial, sans-serif;
        `;
        errorDiv.innerHTML = `
            <h3>Помилка гри</h3>
            <p>${message}</p>
            <button onclick="location.reload()" style="padding: 10px 20px; margin-top: 10px;">
                Перезавантажити
            </button>
        `;
        document.body.appendChild(errorDiv);
    }
    
    setupEventListeners() {
        // Next room button
        document.getElementById('nextRoomBtn').addEventListener('click', () => {
            this.gameState.nextRoom();
            this.ui.updateUI();
        });
        
        // Next floor button
        document.getElementById('nextFloorBtn').addEventListener('click', () => {
            this.gameState.nextFloor();
            this.ui.updateUI();
        });
        
        // Auto fight toggle
        document.getElementById('autoFightBtn').addEventListener('click', () => {
            this.idleManager.toggleAutoFight();
            this.ui.updateUI();
        });

        // Equipment button
        document.getElementById('equipmentBtn').addEventListener('click', () => {
            this.equipmentSystem.toggleEquipmentInterface();
        });

        // Shop button
        document.getElementById('shopBtn').addEventListener('click', () => {
            this.shopSystem.toggleShopInterface();
        });
        
        // Save game periodically
        this.saveInterval = setInterval(() => {
            this.saveManager.saveGame();
        }, 10000); // Save every 10 seconds
        
        // Handle page visibility for offline progress
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.saveManager.saveGame();
            } else {
                this.saveManager.calculateOfflineProgress();
                this.ui.updateUI();
            }
        });
        
        // Save before page unload
        window.addEventListener('beforeunload', () => {
            this.saveManager.saveGame();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Prevent shortcuts when typing in input fields
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                return;
            }

            switch (e.key.toLowerCase()) {
                case 'i':
                    e.preventDefault();
                    this.ui.toggleInventory();
                    break;
                case 'e':
                    e.preventDefault();
                    this.equipmentSystem.toggleEquipmentInterface();
                    break;
                case 's':
                    e.preventDefault();
                    this.shopSystem.toggleShopInterface();
                    break;
                case 'f':
                    e.preventDefault();
                    this.idleManager.toggleAutoFight();
                    this.ui.updateUI();
                    break;
                case ' ':
                case 'enter':
                    e.preventDefault();
                    if (this.gameState.inCombat) {
                        // Manual attack in combat
                        this.gameState.attack();
                    } else {
                        // Next room/floor
                        if (this.gameState.canProgress()) {
                            this.gameState.nextRoom();
                        }
                    }
                    this.ui.updateUI();
                    break;
                case 'escape':
                    e.preventDefault();
                    // Close all open interfaces
                    this.equipmentSystem.hideEquipmentInterface();
                    this.shopSystem.hideShopInterface();
                    break;
            }
        });
    }
    
    handleResize() {
        const container = document.getElementById('gameContainer');
        const ui = document.getElementById('ui');
        
        if (window.innerWidth <= 768) {
            // Mobile layout
            this.canvas.width = window.innerWidth - 4; // Account for borders
            this.canvas.height = window.innerHeight * 0.6 - 4;
        } else {
            // Desktop layout
            const uiWidth = ui.offsetWidth;
            this.canvas.width = window.innerWidth - uiWidth - 6; // Account for borders
            this.canvas.height = window.innerHeight - 4;
        }
        
        // Update renderer
        this.renderer.updateCanvasSize();

        // Update biome system if initialized
        if (this.biomeSystem) {
            this.biomeSystem.handleResize(this.canvas.width, this.canvas.height);
        }
    }
    
    start() {
        this.isRunning = true;
        this.gameLoop(performance.now());
    }
    
    stop() {
        this.isRunning = false;
    }
    
    gameLoop(currentTime) {
        if (!this.isRunning) return;
        
        const deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        
        // Update game systems
        this.update(deltaTime);
        
        // Render
        this.render();
        
        // Continue loop
        requestAnimationFrame(this.gameLoop);
    }
    
    update(deltaTime) {
        try {
            // Update idle mechanics
            this.idleManager.update(deltaTime);

            // Update skill system
            this.skillSystem.update(deltaTime);

            // Update boss system
            this.bossSystem.update(deltaTime);

            // Update shop system
            this.shopSystem.update(deltaTime);

            // Update consumable system
            this.consumableSystem.update(deltaTime);

            // Update biome system
            this.biomeSystem.update(deltaTime);
            this.biomeSystem.updateBiomeBackground(this.gameState.currentFloor);

            // Update settings and statistics
            if (this.settingsManager) {
                this.settingsManager.updateStats(deltaTime);
            }

            // Update particles
            this.renderer.updateParticles(deltaTime);

            // Update audio based on game state
            this.audio.update(this.gameState);

            // Update UI if needed
            if (this.gameState.needsUIUpdate) {
                this.ui.updateUI();
                this.gameState.needsUIUpdate = false;
            }
        } catch (error) {
            console.error('Error in game update loop:', error);
            // Continue running but log the error
        }
    }
    
    render() {
        try {
            // Update performance monitor
            this.renderer.performanceMonitor.update();

            // Clear canvas with biome background
            this.renderer.clear(this.gameState);

            // Render biome background particles (behind everything)
            this.biomeSystem.render();

            // Render game world WITHOUT floor tiles (to preserve gradient)
            this.renderer.renderWorldWithoutFloor(this.gameState);

            // Render effects (damage numbers, level up, etc.)
            this.renderer.renderEffects(this.gameState.effects);

        } catch (error) {
            console.error('Error in render:', error);
            // Fallback rendering
            this.renderer.clear();
            this.renderer.drawText('Помилка рендерингу', this.renderer.width/2, this.renderer.height/2, '#ff0000', 24, 'center');
        }
    }

    // Cleanup method to prevent memory leaks
    cleanup() {
        if (this.saveInterval) {
            clearInterval(this.saveInterval);
            this.saveInterval = null;
        }

        if (this.gameLoopId) {
            cancelAnimationFrame(this.gameLoopId);
            this.gameLoopId = null;
        }

        // Cleanup audio context
        if (this.audio && this.audio.cleanup) {
            this.audio.cleanup();
        }

        // Cleanup particle system
        if (this.renderer && this.renderer.particleSystem && this.renderer.particleSystem.cleanup) {
            this.renderer.particleSystem.cleanup();
        }
    }

    // Show game with beautiful animations
    showGameWithAnimation() {
        const loading = document.getElementById('loading');
        const gameContainer = document.getElementById('gameContainer');

        // Fade out loading screen
        loading.style.transition = 'opacity 0.5s ease';
        loading.style.opacity = '0';

        setTimeout(() => {
            loading.style.display = 'none';
            gameContainer.style.display = 'flex';

            // Add fade-in animation to UI panels
            const uiPanels = document.querySelectorAll('.ui-panel');
            uiPanels.forEach((panel, index) => {
                panel.style.opacity = '0';
                panel.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    panel.style.transition = 'all 0.5s ease';
                    panel.style.opacity = '1';
                    panel.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // Add slide-in animation to canvas
            const canvas = document.getElementById('gameCanvas');
            canvas.style.opacity = '0';
            canvas.style.transform = 'translateX(-20px)';
            setTimeout(() => {
                canvas.style.transition = 'all 0.5s ease';
                canvas.style.opacity = '1';
                canvas.style.transform = 'translateX(0)';
            }, 200);

            // Add slide-in animation to UI
            const ui = document.getElementById('ui');
            ui.style.opacity = '0';
            ui.style.transform = 'translateX(20px)';
            setTimeout(() => {
                ui.style.transition = 'all 0.5s ease';
                ui.style.opacity = '1';
                ui.style.transform = 'translateX(0)';
            }, 300);

        }, 500);
    }
}

// Global error handling
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    // Don't crash the game for non-critical errors
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    event.preventDefault(); // Prevent the default browser behavior
});

// Start the game when page loads
window.addEventListener('load', () => {
    try {
        window.game = new Game();
    } catch (error) {
        console.error('Failed to create game instance:', error);
        document.body.innerHTML = `
            <div style="text-align: center; padding: 50px; color: red;">
                <h1>Помилка завантаження гри</h1>
                <p>Не вдалося ініціалізувати гру. Перезавантажте сторінку.</p>
                <button onclick="location.reload()">Перезавантажити</button>
            </div>
        `;
    }
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.game && window.game.cleanup) {
        window.game.cleanup();
    }
});

// Export for debugging
window.Game = Game;

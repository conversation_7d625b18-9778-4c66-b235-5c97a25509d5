/* Additional animations and effects */

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px #00ff00;
    }
    50% {
        box-shadow: 0 0 20px #00ff00, 0 0 30px #00ff00;
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

@keyframes levelUp {
    0% {
        transform: scale(1);
        color: #ffff00;
    }
    50% {
        transform: scale(1.2);
        color: #ffffff;
        text-shadow: 0 0 20px #ffff00;
    }
    100% {
        transform: scale(1);
        color: #ffff00;
    }
}

/* Button animations */
.button {
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.button:hover::before {
    left: 100%;
}

.button.active {
    animation: glow 2s infinite;
}

/* Progress bar animations */
.progress-fill {
    transition: width 0.3s ease;
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-image: linear-gradient(
        -45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 50px 50px;
    animation: move 2s linear infinite;
}

@keyframes move {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 50px 50px;
    }
}

/* Inventory animations */
.inventory-slot {
    transition: all 0.2s ease;
}

.inventory-slot:hover {
    transform: scale(1.05);
    box-shadow: 0 0 10px #00ff00;
}

.inventory-slot.has-item {
    animation: pulse 3s infinite;
}

/* Log animations */
.log-entry {
    animation: slideIn 0.3s ease;
}

.log-entry.combat {
    animation: slideIn 0.3s ease, shake 0.5s ease 0.3s;
}

.log-entry.level {
    animation: slideIn 0.3s ease, levelUp 1s ease 0.3s;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .button {
        padding: 12px 20px;
        font-size: 16px;
    }
    
    .inventory-slot {
        width: 50px;
        height: 50px;
    }
    
    .progress-bar {
        height: 25px;
    }
}

/* Loading animation */
.loading-text {
    animation: pulse 1s infinite;
}

/* Notification styles */
.notification {
    animation: slideIn 0.3s ease;
    border-radius: 5px;
    backdrop-filter: blur(10px);
}

.notification.success {
    border-color: #00ff00;
    color: #00ff00;
}

.notification.error {
    border-color: #ff0000;
    color: #ff0000;
}

.notification.warning {
    border-color: #ffff00;
    color: #ffff00;
}

/* Game menu animations */
.game-menu {
    animation: slideIn 0.3s ease;
    backdrop-filter: blur(5px);
}

/* Canvas effects */
#gameCanvas {
    transition: filter 0.3s ease;
}

#gameCanvas.combat {
    filter: brightness(1.2) contrast(1.1);
}

#gameCanvas.paused {
    filter: grayscale(0.5) blur(2px);
}

/* Rarity colors for items */
.rarity-common { color: #ffffff; }
.rarity-uncommon { color: #00ff00; }
.rarity-rare { color: #0066ff; }
.rarity-epic { color: #9966ff; }
.rarity-legendary { color: #ff6600; }

/* Stat change animations */
.stat-increase {
    animation: levelUp 0.5s ease;
    color: #00ff00 !important;
}

.stat-decrease {
    animation: shake 0.5s ease;
    color: #ff0000 !important;
}

/* Health bar color transitions */
.progress-fill.health {
    transition: background-color 0.3s ease;
}

.progress-fill.health.low {
    background: linear-gradient(90deg, #ff0000, #ff6666) !important;
    animation: pulse 1s infinite;
}

.progress-fill.health.critical {
    background: #ff0000 !important;
    animation: shake 0.2s infinite;
}

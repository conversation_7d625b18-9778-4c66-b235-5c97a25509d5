// Skill system and achievements
export class SkillSystem {
    constructor(gameState) {
        this.gameState = gameState;
        
        // Skills
        this.skills = {
            combat: {
                name: 'Бойові навички',
                level: 1,
                experience: 0,
                experienceToNext: 100,
                bonuses: {
                    attackMultiplier: 1.0,
                    criticalChance: 0.05,
                    combatSpeed: 1.0
                }
            },
            exploration: {
                name: 'Дослідження',
                level: 1,
                experience: 0,
                experienceToNext: 100,
                bonuses: {
                    movementSpeed: 1.0,
                    treasureChance: 1.0,
                    secretRoomChance: 0.1
                }
            },
            survival: {
                name: 'Виживання',
                level: 1,
                experience: 0,
                experienceToNext: 100,
                bonuses: {
                    healthRegen: 0.1,
                    resistanceMultiplier: 1.0,
                    healingEfficiency: 1.0
                }
            },
            luck: {
                name: 'Удача',
                level: 1,
                experience: 0,
                experienceToNext: 100,
                bonuses: {
                    dropRateMultiplier: 1.0,
                    rarityBonus: 0.0,
                    goldMultiplier: 1.0
                }
            },
            magic: {
                name: 'Магія',
                level: 1,
                experience: 0,
                experienceToNext: 100,
                bonuses: {
                    spellPower: 1.0,
                    manaRegen: 0.1,
                    enchantmentChance: 0.05
                }
            }
        };
        
        // Achievements
        this.achievements = {
            firstKill: {
                name: 'Перша кров',
                description: 'Переможіть першого ворога',
                unlocked: false,
                reward: { type: 'skill_exp', skill: 'combat', amount: 50 }
            },
            levelUp10: {
                name: 'Досвідчений воїн',
                description: 'Досягніть 10 рівня',
                unlocked: false,
                reward: { type: 'stat_boost', stat: 'attack', amount: 5 }
            },
            floor10: {
                name: 'Глибинний дослідник',
                description: 'Досягніть 10 поверху',
                unlocked: false,
                reward: { type: 'skill_exp', skill: 'exploration', amount: 100 }
            },
            gold1000: {
                name: 'Багатій',
                description: 'Накопичіть 1000 золота',
                unlocked: false,
                reward: { type: 'skill_exp', skill: 'luck', amount: 75 }
            },
            items50: {
                name: 'Колекціонер',
                description: 'Знайдіть 50 предметів',
                unlocked: false,
                reward: { type: 'skill_exp', skill: 'luck', amount: 100 }
            },
            survive1hour: {
                name: 'Виживальник',
                description: 'Грайте протягом 1 години',
                unlocked: false,
                reward: { type: 'skill_exp', skill: 'survival', amount: 150 }
            },
            noDeaths: {
                name: 'Безсмертний',
                description: 'Досягніть 5 поверху без смертей',
                unlocked: false,
                reward: { type: 'stat_boost', stat: 'maxHp', amount: 50 }
            },
            speedRun: {
                name: 'Швидкісний',
                description: 'Досягніть 5 поверху за 10 хвилин',
                unlocked: false,
                reward: { type: 'skill_exp', skill: 'exploration', amount: 200 }
            },
            bossKiller: {
                name: 'Вбивця боссів',
                description: 'Переможіть 5 боссів',
                unlocked: false,
                reward: { type: 'skill_exp', skill: 'combat', amount: 300 }
            },
            masterExplorer: {
                name: 'Майстер дослідник',
                description: 'Дослідіть 100 кімнат',
                unlocked: false,
                reward: { type: 'skill_exp', skill: 'exploration', amount: 250 }
            }
        };
        
        // Skill trees
        this.skillTrees = {
            combat: [
                { name: 'Сила удару', maxLevel: 10, cost: 1, effect: 'attackMultiplier', value: 0.1 },
                { name: 'Критичний удар', maxLevel: 5, cost: 2, effect: 'criticalChance', value: 0.02 },
                { name: 'Швидкість бою', maxLevel: 8, cost: 1, effect: 'combatSpeed', value: 0.05 },
                { name: 'Берсерк', maxLevel: 3, cost: 3, effect: 'berserkChance', value: 0.1 }
            ],
            exploration: [
                { name: 'Швидкість руху', maxLevel: 10, cost: 1, effect: 'movementSpeed', value: 0.1 },
                { name: 'Пошук скарбів', maxLevel: 8, cost: 2, effect: 'treasureChance', value: 0.15 },
                { name: 'Таємні кімнати', maxLevel: 5, cost: 3, effect: 'secretRoomChance', value: 0.05 },
                { name: 'Картограф', maxLevel: 3, cost: 4, effect: 'mapReveal', value: 1 }
            ],
            survival: [
                { name: 'Регенерація', maxLevel: 10, cost: 1, effect: 'healthRegen', value: 0.05 },
                { name: 'Опір', maxLevel: 8, cost: 2, effect: 'resistanceMultiplier', value: 0.1 },
                { name: 'Ефективність лікування', maxLevel: 5, cost: 2, effect: 'healingEfficiency', value: 0.2 },
                { name: 'Друге дихання', maxLevel: 1, cost: 5, effect: 'secondWind', value: 1 }
            ],
            luck: [
                { name: 'Удача в бою', maxLevel: 10, cost: 1, effect: 'dropRateMultiplier', value: 0.1 },
                { name: 'Рідкісні предмети', maxLevel: 5, cost: 3, effect: 'rarityBonus', value: 0.05 },
                { name: 'Золота лихоманка', maxLevel: 8, cost: 2, effect: 'goldMultiplier', value: 0.15 },
                { name: 'Джекпот', maxLevel: 3, cost: 4, effect: 'jackpotChance', value: 0.02 }
            ],
            magic: [
                { name: 'Сила заклинань', maxLevel: 10, cost: 1, effect: 'spellPower', value: 0.2 },
                { name: 'Регенерація мани', maxLevel: 8, cost: 2, effect: 'manaRegen', value: 0.05 },
                { name: 'Зачарування', maxLevel: 5, cost: 3, effect: 'enchantmentChance', value: 0.03 },
                { name: 'Архімаг', maxLevel: 1, cost: 10, effect: 'archmage', value: 1 }
            ]
        };
        
        // Skill points
        this.skillPoints = 0;
        this.totalSkillPoints = 0;
    }
    
    // Skill experience gain
    gainSkillExperience(skillName, amount) {
        const skill = this.skills[skillName];
        if (!skill) return;
        
        skill.experience += amount;
        
        // Check for level up
        while (skill.experience >= skill.experienceToNext) {
            this.levelUpSkill(skillName);
        }
        
        this.gameState.needsUIUpdate = true;
    }
    
    levelUpSkill(skillName) {
        const skill = this.skills[skillName];
        skill.experience -= skill.experienceToNext;
        skill.level++;
        
        // Award skill point
        this.skillPoints++;
        this.totalSkillPoints++;
        
        // Calculate next level requirement
        skill.experienceToNext = Math.floor(100 * Math.pow(1.3, skill.level - 1));
        
        // Update bonuses
        this.updateSkillBonuses(skillName);
        
        this.gameState.addLogEntry(`${skill.name} підвищено до рівня ${skill.level}!`, 'level');
        this.gameState.addLogEntry(`Отримано очко навички!`, 'level');
    }
    
    updateSkillBonuses(skillName) {
        const skill = this.skills[skillName];
        const baseMultiplier = 1 + (skill.level - 1) * 0.05; // 5% per level
        
        switch (skillName) {
            case 'combat':
                skill.bonuses.attackMultiplier = baseMultiplier;
                skill.bonuses.criticalChance = 0.05 + (skill.level - 1) * 0.01;
                skill.bonuses.combatSpeed = 1 + (skill.level - 1) * 0.02;
                break;
            case 'exploration':
                skill.bonuses.movementSpeed = baseMultiplier;
                skill.bonuses.treasureChance = 1 + (skill.level - 1) * 0.1;
                skill.bonuses.secretRoomChance = 0.1 + (skill.level - 1) * 0.02;
                break;
            case 'survival':
                skill.bonuses.healthRegen = 0.1 + (skill.level - 1) * 0.05;
                skill.bonuses.resistanceMultiplier = baseMultiplier;
                skill.bonuses.healingEfficiency = baseMultiplier;
                break;
            case 'luck':
                skill.bonuses.dropRateMultiplier = baseMultiplier;
                skill.bonuses.rarityBonus = (skill.level - 1) * 0.02;
                skill.bonuses.goldMultiplier = baseMultiplier;
                break;
            case 'magic':
                skill.bonuses.spellPower = baseMultiplier;
                skill.bonuses.manaRegen = 0.1 + (skill.level - 1) * 0.03;
                skill.bonuses.enchantmentChance = 0.05 + (skill.level - 1) * 0.01;
                break;
        }
    }
    
    // Achievement system
    checkAchievements() {
        const player = this.gameState.player;
        const stats = this.gameState.stats;
        
        // Check each achievement
        Object.keys(this.achievements).forEach(key => {
            const achievement = this.achievements[key];
            if (achievement.unlocked) return;
            
            let unlocked = false;
            
            switch (key) {
                case 'firstKill':
                    unlocked = stats.enemiesKilled >= 1;
                    break;
                case 'levelUp10':
                    unlocked = player.level >= 10;
                    break;
                case 'floor10':
                    unlocked = this.gameState.currentFloor >= 10;
                    break;
                case 'gold1000':
                    unlocked = player.gold >= 1000;
                    break;
                case 'items50':
                    unlocked = stats.itemsFound >= 50;
                    break;
                case 'survive1hour':
                    unlocked = stats.totalPlayTime >= 3600000; // 1 hour in ms
                    break;
                case 'noDeaths':
                    unlocked = this.gameState.currentFloor >= 5 && stats.deathCount === 0;
                    break;
                case 'speedRun':
                    unlocked = this.gameState.currentFloor >= 5 && stats.totalPlayTime <= 600000; // 10 minutes
                    break;
                case 'bossKiller':
                    unlocked = (stats.bossesKilled || 0) >= 5;
                    break;
                case 'masterExplorer':
                    unlocked = (stats.roomsExplored || 0) >= 100;
                    break;
            }
            
            if (unlocked) {
                this.unlockAchievement(key);
            }
        });
    }
    
    unlockAchievement(achievementKey) {
        const achievement = this.achievements[achievementKey];
        achievement.unlocked = true;
        
        // Apply reward
        this.applyAchievementReward(achievement.reward);
        
        this.gameState.addLogEntry(`🏆 Досягнення розблоковано: ${achievement.name}!`, 'achievement');
        
        // Show notification
        if (window.game && window.game.ui) {
            window.game.ui.showNotification(`Досягнення: ${achievement.name}`, 'success', 5000);
        }
    }
    
    applyAchievementReward(reward) {
        switch (reward.type) {
            case 'skill_exp':
                this.gainSkillExperience(reward.skill, reward.amount);
                break;
            case 'stat_boost':
                this.gameState.player[reward.stat] += reward.amount;
                break;
            case 'gold':
                this.gameState.gainGold(reward.amount);
                break;
            case 'item':
                this.gameState.addItemToInventory(reward.item);
                break;
        }
    }
    
    // Get total bonuses from all skills
    getTotalBonuses() {
        const bonuses = {
            attackMultiplier: 1,
            criticalChance: 0,
            combatSpeed: 1,
            movementSpeed: 1,
            treasureChance: 1,
            secretRoomChance: 0,
            healthRegen: 0,
            resistanceMultiplier: 1,
            healingEfficiency: 1,
            dropRateMultiplier: 1,
            rarityBonus: 0,
            goldMultiplier: 1,
            spellPower: 1,
            manaRegen: 0,
            enchantmentChance: 0
        };
        
        Object.values(this.skills).forEach(skill => {
            Object.keys(skill.bonuses).forEach(bonus => {
                if (bonuses.hasOwnProperty(bonus)) {
                    if (bonus.includes('Multiplier') || bonus.includes('Speed') || bonus.includes('Efficiency') || bonus.includes('Power')) {
                        bonuses[bonus] *= skill.bonuses[bonus];
                    } else {
                        bonuses[bonus] += skill.bonuses[bonus];
                    }
                }
            });
        });
        
        return bonuses;
    }
    
    // Update method called from game loop
    update(deltaTime) {
        // Passive skill experience gain
        const passiveRate = 0.1; // XP per second
        const expGain = (deltaTime / 1000) * passiveRate;
        
        if (expGain >= 1) {
            // Gain experience in random skill
            const skillNames = Object.keys(this.skills);
            const randomSkill = skillNames[Math.floor(Math.random() * skillNames.length)];
            this.gainSkillExperience(randomSkill, Math.floor(expGain));
        }
        
        // Check achievements
        this.checkAchievements();
    }
    
    // Serialization
    serialize() {
        return {
            skills: this.skills,
            achievements: this.achievements,
            skillPoints: this.skillPoints,
            totalSkillPoints: this.totalSkillPoints
        };
    }
    
    deserialize(data) {
        if (data.skills) this.skills = { ...this.skills, ...data.skills };
        if (data.achievements) this.achievements = { ...this.achievements, ...data.achievements };
        if (data.skillPoints !== undefined) this.skillPoints = data.skillPoints;
        if (data.totalSkillPoints !== undefined) this.totalSkillPoints = data.totalSkillPoints;
    }
}

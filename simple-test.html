<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test - Icon System</title>
    <link rel="stylesheet" href="css/icons.css">
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .test-container {
            border: 1px solid #00ff00;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .icon-test {
            display: flex;
            align-items: center;
            margin: 10px 0;
            gap: 10px;
        }
        button {
            background: #003300;
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 10px 20px;
            cursor: pointer;
            margin: 5px;
            border-radius: 3px;
        }
        button:hover { background: #006600; }
    </style>
</head>
<body>
    <h1>🧪 Simple Test - Система іконок</h1>
    
    <div class="test-container">
        <h2>Тест 1: Прямий імпорт ItemIconSystem</h2>
        <button onclick="testDirectImport()">Тестувати прямий імпорт</button>
        <div id="directTest"></div>
    </div>
    
    <div class="test-container">
        <h2>Тест 2: Створення іконок</h2>
        <button onclick="testIconCreation()">Створити тестові іконки</button>
        <div id="iconTest"></div>
    </div>
    
    <div class="test-container">
        <h2>Тест 3: Перевірка основної гри</h2>
        <button onclick="checkMainGame()">Перевірити основну гру</button>
        <div id="gameTest"></div>
    </div>

    <script type="module">
        async function testDirectImport() {
            const container = document.getElementById('directTest');
            container.innerHTML = '';
            
            try {
                console.log('Importing ItemIconSystem...');
                const { ItemIconSystem } = await import('./js/itemIconSystem.js');
                console.log('ItemIconSystem imported:', ItemIconSystem);
                
                const iconSystem = new ItemIconSystem();
                console.log('IconSystem created:', iconSystem);
                
                container.innerHTML = '<div style="color: #00ff00;">✅ ItemIconSystem імпортовано та створено успішно!</div>';
                
                // Тестуємо методи
                const testIcon = iconSystem.getItemIcon('sword');
                container.innerHTML += `<div>Іконка меча: ${testIcon}</div>`;
                
                const testColor = iconSystem.getRarityColor('epic');
                container.innerHTML += `<div>Колір епічного: ${testColor}</div>`;
                
            } catch (error) {
                console.error('Error in direct import test:', error);
                container.innerHTML = `<div style="color: #ff0000;">❌ Помилка: ${error.message}</div>`;
            }
        }

        async function testIconCreation() {
            const container = document.getElementById('iconTest');
            container.innerHTML = '';
            
            try {
                const { ItemIconSystem } = await import('./js/itemIconSystem.js');
                const iconSystem = new ItemIconSystem();
                
                const testItems = [
                    { name: 'Епічний меч', type: 'sword', rarity: 'epic', level: 5, enhancement: 2 },
                    { name: 'Легендарний щит', type: 'shield', rarity: 'legendary', level: 10, enhancement: 5 },
                    { name: 'Рідкісне кільце', type: 'ring', rarity: 'rare', level: 3, enhancement: 0 },
                    { name: 'Зілля лікування', type: 'potion', rarity: 'common', level: 1, enhancement: 0 }
                ];
                
                testItems.forEach((item, index) => {
                    const iconElement = iconSystem.createItemIconElement(item, 48);
                    
                    const testDiv = document.createElement('div');
                    testDiv.className = 'icon-test';
                    testDiv.appendChild(iconElement);
                    
                    const label = document.createElement('span');
                    label.textContent = `${item.name} (${item.rarity})`;
                    testDiv.appendChild(label);
                    
                    container.appendChild(testDiv);
                });
                
                container.innerHTML = '<div style="color: #00ff00;">✅ Іконки створено успішно!</div>' + container.innerHTML;
                
            } catch (error) {
                console.error('Error in icon creation test:', error);
                container.innerHTML = `<div style="color: #ff0000;">❌ Помилка: ${error.message}</div>`;
            }
        }

        async function checkMainGame() {
            const container = document.getElementById('gameTest');
            container.innerHTML = '';
            
            try {
                // Перевіряємо, чи доступна основна гра
                if (window.opener && window.opener.game) {
                    const game = window.opener.game;
                    container.innerHTML += '<div style="color: #00ff00;">✅ Основна гра знайдена в opener</div>';
                    
                    if (game.iconSystem) {
                        container.innerHTML += '<div style="color: #00ff00;">✅ IconSystem знайдено в грі</div>';
                    } else {
                        container.innerHTML += '<div style="color: #ff0000;">❌ IconSystem НЕ знайдено в грі</div>';
                    }
                    
                    if (game.ui && game.ui.iconSystem) {
                        container.innerHTML += '<div style="color: #00ff00;">✅ IconSystem знайдено в UI</div>';
                    } else {
                        container.innerHTML += '<div style="color: #ff0000;">❌ IconSystem НЕ знайдено в UI</div>';
                    }
                    
                } else if (window.parent && window.parent.game) {
                    const game = window.parent.game;
                    container.innerHTML += '<div style="color: #00ff00;">✅ Основна гра знайдена в parent</div>';
                    
                } else {
                    container.innerHTML += '<div style="color: #ffff00;">⚠️ Основна гра не знайдена</div>';
                    container.innerHTML += '<div>Спробуйте відкрити index.html в іншій вкладці</div>';
                }
                
                // Перевіряємо глобальний об'єкт
                if (window.game) {
                    container.innerHTML += '<div style="color: #00ff00;">✅ window.game існує</div>';
                } else {
                    container.innerHTML += '<div style="color: #ff0000;">❌ window.game не існує</div>';
                }
                
            } catch (error) {
                console.error('Error checking main game:', error);
                container.innerHTML += `<div style="color: #ff0000;">❌ Помилка: ${error.message}</div>`;
            }
        }

        // Глобальні функції
        window.testDirectImport = testDirectImport;
        window.testIconCreation = testIconCreation;
        window.checkMainGame = checkMainGame;

        // Автоматично запускаємо базовий тест
        window.addEventListener('load', () => {
            console.log('Simple test page loaded');
            testDirectImport();
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gradient Test - Біомні градієнти</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            background: #000;
            color: #00ff00;
        }

        .test-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
        }

        .gradient-test {
            width: 300px;
            height: 200px;
            border: 2px solid #00ff00;
            border-radius: 8px;
            margin: 10px;
            position: relative;
        }

        .gradient-label {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 5px 10px;
            border-radius: 3px;
            color: #ffff00;
            font-weight: bold;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        button {
            background: #003300;
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 10px 20px;
            cursor: pointer;
            margin: 5px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }

        button:hover {
            background: #006600;
            box-shadow: 0 0 10px #00ff00;
        }

        .info {
            text-align: center;
            margin: 20px 0;
            color: #cccccc;
        }
    </style>
</head>
<body>
    <h1>🎨 Gradient Test - Тест біомних градієнтів</h1>
    
    <div class="info">
        Простий тест градієнтів без складної логіки рендерингу
    </div>

    <div class="controls">
        <button onclick="testGradients()">Тестувати градієнти</button>
        <button onclick="testWithFloor()">Тест з підлогою</button>
        <button onclick="clearAll()">Очистити</button>
    </div>

    <div class="test-container">
        <canvas class="gradient-test" id="caveGradient" width="300" height="200">
            <div class="gradient-label">Печера</div>
        </canvas>
        
        <canvas class="gradient-test" id="forestGradient" width="300" height="200">
            <div class="gradient-label">Ліс</div>
        </canvas>
        
        <canvas class="gradient-test" id="desertGradient" width="300" height="200">
            <div class="gradient-label">Пустеля</div>
        </canvas>
        
        <canvas class="gradient-test" id="glacierGradient" width="300" height="200">
            <div class="gradient-label">Льодовики</div>
        </canvas>
        
        <canvas class="gradient-test" id="volcanoGradient" width="300" height="200">
            <div class="gradient-label">Вулкан</div>
        </canvas>
        
        <canvas class="gradient-test" id="abyssGradient" width="300" height="200">
            <div class="gradient-label">Безодня</div>
        </canvas>
    </div>

    <script>
        // Біомні кольори (копія з renderer.js)
        const biomes = {
            cave: { backgroundColor: '#2a2a2a' },
            forest: { backgroundColor: '#1a4a1a' },
            desert: { backgroundColor: '#4a3a1a' },
            glacier: { backgroundColor: '#1a3a5a' },
            volcano: { backgroundColor: '#5a1a1a' },
            abyss: { backgroundColor: '#2a1a4a' }
        };

        function darkenColor(color, factor) {
            if (color.startsWith('#')) {
                const r = parseInt(color.slice(1, 3), 16);
                const g = parseInt(color.slice(3, 5), 16);
                const b = parseInt(color.slice(5, 7), 16);
                
                return `rgb(${Math.floor(r * factor)}, ${Math.floor(g * factor)}, ${Math.floor(b * factor)})`;
            }
            return color;
        }

        function createGradient(canvas, biome) {
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            // Створюємо радіальний градієнт
            const gradient = ctx.createRadialGradient(
                width / 2, height / 2, 0,
                width / 2, height / 2, Math.max(width, height) / 2
            );

            gradient.addColorStop(0, biome.backgroundColor);
            gradient.addColorStop(0.7, darkenColor(biome.backgroundColor, 0.8));
            gradient.addColorStop(1, darkenColor(biome.backgroundColor, 0.6));

            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);

            console.log(`Gradient created for biome with color: ${biome.backgroundColor}`);
        }

        function addFloorPattern(canvas, biome) {
            const ctx = canvas.getContext('2d');
            const tileSize = 20;
            const tilesX = Math.floor(canvas.width / tileSize);
            const tilesY = Math.floor(canvas.height / tileSize);

            for (let x = 0; x < tilesX; x++) {
                for (let y = 0; y < tilesY; y++) {
                    const tileX = x * tileSize;
                    const tileY = y * tileSize;
                    
                    const isLight = (x + y) % 2 === 0;
                    const alpha = isLight ? 0.2 : 0.4;
                    
                    ctx.fillStyle = `rgba(0, 0, 0, ${alpha})`;
                    ctx.fillRect(tileX, tileY, tileSize, tileSize);
                }
            }
        }

        window.testGradients = function() {
            console.log('Testing gradients...');
            
            Object.keys(biomes).forEach(biomeName => {
                const canvas = document.getElementById(`${biomeName}Gradient`);
                if (canvas) {
                    createGradient(canvas, biomes[biomeName]);
                    console.log(`Created gradient for ${biomeName}`);
                } else {
                    console.error(`Canvas not found for ${biomeName}`);
                }
            });
        };

        window.testWithFloor = function() {
            console.log('Testing gradients with floor...');
            
            Object.keys(biomes).forEach(biomeName => {
                const canvas = document.getElementById(`${biomeName}Gradient`);
                if (canvas) {
                    createGradient(canvas, biomes[biomeName]);
                    addFloorPattern(canvas, biomes[biomeName]);
                    console.log(`Created gradient + floor for ${biomeName}`);
                }
            });
        };

        window.clearAll = function() {
            Object.keys(biomes).forEach(biomeName => {
                const canvas = document.getElementById(`${biomeName}Gradient`);
                if (canvas) {
                    const ctx = canvas.getContext('2d');
                    ctx.fillStyle = '#000000';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                }
            });
            console.log('All canvases cleared');
        };

        // Автоматично запускаємо тест при завантаженні
        window.addEventListener('load', () => {
            console.log('Gradient test page loaded');
            setTimeout(testGradients, 1000);
        });
    </script>
</body>
</html>

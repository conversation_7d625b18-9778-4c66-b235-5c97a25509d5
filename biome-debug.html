<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Biome Debug - Idle Roguelike Adventure</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            background: #000;
            color: #00ff00;
        }

        .debug-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
        }

        .debug-canvas {
            border: 2px solid #00ff00;
            border-radius: 8px;
            margin: 10px;
        }

        .debug-info {
            background: #111;
            border: 1px solid #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-size: 12px;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        button {
            background: #003300;
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 10px 20px;
            cursor: pointer;
            margin: 5px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }

        button:hover {
            background: #006600;
            box-shadow: 0 0 10px #00ff00;
        }

        .step {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #333;
            border-radius: 5px;
        }

        .step h3 {
            color: #ffff00;
            margin: 0 0 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔍 Biome Debug - Діагностика біомних фонів</h1>
    
    <div class="controls">
        <button onclick="testStep1()">Крок 1: Тільки clear()</button>
        <button onclick="testStep2()">Крок 2: clear() + підлога</button>
        <button onclick="testStep3()">Крок 3: Повний рендеринг</button>
        <button onclick="testAllBiomes()">Тест всіх біомів</button>
        <button onclick="clearCanvas()">Очистити</button>
    </div>

    <div class="debug-container">
        <canvas id="debugCanvas" width="600" height="400" class="debug-canvas"></canvas>
    </div>

    <div class="debug-info" id="debugInfo">
        Інформація про діагностику з'явиться тут...
    </div>

    <div class="step">
        <h3>Крок 1: Тільки clear()</h3>
        <p>Тестує чи працює радіальний градієнт без підлоги</p>
    </div>

    <div class="step">
        <h3>Крок 2: clear() + підлога</h3>
        <p>Перевіряє чи підлога перекриває градієнт</p>
    </div>

    <div class="step">
        <h3>Крок 3: Повний рендеринг</h3>
        <p>Тестує повний цикл рендерингу з персонажами</p>
    </div>

    <script type="module">
        let renderer = null;
        let currentFloor = 1;

        function log(message) {
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.innerHTML += `<div>[${new Date().toLocaleTimeString()}] ${message}</div>`;
            debugInfo.scrollTop = debugInfo.scrollHeight;
            console.log(message);
        }

        async function initRenderer() {
            if (renderer) return;

            try {
                const { Renderer } = await import('./js/renderer.js');
                
                const canvas = document.getElementById('debugCanvas');
                const ctx = canvas.getContext('2d');
                renderer = new Renderer(canvas, ctx);
                
                log('Renderer ініціалізовано успішно');
            } catch (error) {
                log(`Помилка ініціалізації: ${error.message}`);
                console.error('Failed to initialize renderer:', error);
            }
        }

        function createGameState(floor) {
            return {
                currentFloor: floor,
                currentRoom: 1,
                currentLocation: { name: 'Тестова локація' },
                inCombat: true,
                player: {
                    name: 'Тестовий герой',
                    level: 10,
                    hp: 75,
                    maxHp: 100,
                    equipment: {
                        weapon: { name: 'Меч' },
                        shield: { name: 'Щит' }
                    }
                },
                currentEnemy: {
                    name: 'Тестовий гоблін',
                    type: 'goblin',
                    level: 5,
                    hp: 30,
                    maxHp: 50
                },
                effects: []
            };
        }

        window.testStep1 = async function() {
            await initRenderer();
            
            const gameState = createGameState(currentFloor);
            const biome = renderer.getBiomeForFloor(currentFloor);
            
            log(`Крок 1: Тестування clear() для поверху ${currentFloor}`);
            log(`Біом: ${biome.name}, Колір фону: ${biome.backgroundColor}`);
            
            // Тільки clear
            renderer.clear(gameState);
            
            log('Крок 1 завершено - має бути видно радіальний градієнт');
        };

        window.testStep2 = async function() {
            await initRenderer();
            
            const gameState = createGameState(currentFloor);
            const biome = renderer.getBiomeForFloor(currentFloor);
            
            log(`Крок 2: clear() + підлога для поверху ${currentFloor}`);
            
            // Clear + підлога
            renderer.clear(gameState);
            renderer.drawFloorTiles(gameState, biome);
            
            log('Крок 2 завершено - перевірте чи підлога перекриває градієнт');
        };

        window.testStep3 = async function() {
            await initRenderer();
            
            const gameState = createGameState(currentFloor);
            
            log(`Крок 3: Повний рендеринг для поверху ${currentFloor}`);
            
            // Повний рендеринг
            renderer.renderWorld(gameState);
            
            log('Крок 3 завершено - повний рендеринг');
        };

        window.testAllBiomes = async function() {
            await initRenderer();
            
            const floors = [1, 10, 20, 30, 40, 55]; // Різні біоми
            let index = 0;
            
            function testNextBiome() {
                if (index >= floors.length) {
                    log('Тест всіх біомів завершено');
                    return;
                }
                
                const floor = floors[index];
                const gameState = createGameState(floor);
                const biome = renderer.getBiomeForFloor(floor);
                
                log(`Тестування біому: ${biome.name} (поверх ${floor})`);
                
                renderer.clear(gameState);
                
                index++;
                setTimeout(testNextBiome, 2000); // 2 секунди між біомами
            }
            
            testNextBiome();
        };

        window.clearCanvas = function() {
            if (renderer) {
                renderer.ctx.fillStyle = '#000000';
                renderer.ctx.fillRect(0, 0, renderer.width, renderer.height);
                log('Canvas очищено');
            }
        };

        // Додаємо контроли для зміни поверху
        const controls = document.querySelector('.controls');
        const floorControl = document.createElement('div');
        floorControl.innerHTML = `
            <label style="color: #00ff00; margin: 0 10px;">Поверх: 
                <input type="number" id="floorInput" value="1" min="1" max="60" 
                       style="background: #111; color: #00ff00; border: 1px solid #333; padding: 5px; width: 60px;">
            </label>
            <button onclick="updateFloor()">Оновити поверх</button>
        `;
        controls.appendChild(floorControl);

        window.updateFloor = function() {
            const input = document.getElementById('floorInput');
            currentFloor = parseInt(input.value) || 1;
            log(`Поверх змінено на: ${currentFloor}`);
        };

        // Автоматично ініціалізуємо при завантаженні
        window.addEventListener('load', () => {
            log('Сторінка діагностики завантажена');
            log('Натисніть кнопки для тестування різних кроків рендерингу');
        });
    </script>
</body>
</html>

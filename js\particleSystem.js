// Particle system for visual effects
export class ParticleSystem {
    constructor(renderer) {
        this.renderer = renderer;
        this.particles = [];
        this.maxParticles = 100;
        this.performanceMode = false;
        this.particlePool = []; // Object pooling for better performance
        this.poolSize = 200;

        // Initialize particle pool
        this.initializePool();
    }

    initializePool() {
        for (let i = 0; i < this.poolSize; i++) {
            this.particlePool.push(this.createEmptyParticle());
        }
    }

    createEmptyParticle() {
        return {
            x: 0, y: 0, vx: 0, vy: 0,
            life: 0, maxLife: 0, size: 0,
            color: '#ffffff', type: 'circle',
            gravity: 0, fade: 0, active: false
        };
    }

    getParticleFromPool() {
        for (let i = 0; i < this.particlePool.length; i++) {
            if (!this.particlePool[i].active) {
                this.particlePool[i].active = true;
                return this.particlePool[i];
            }
        }
        // If pool is exhausted, create new particle
        return this.createEmptyParticle();
    }

    returnParticleToPool(particle) {
        particle.active = false;
        // Reset particle properties
        Object.assign(particle, this.createEmptyParticle());
    }
    
    // Create different types of particles
    createDamageParticles(x, y, damage, type = 'damage') {
        // Reduce particle count in performance mode
        const baseCount = Math.min(5, Math.floor(damage / 10) + 1);
        const particleCount = this.performanceMode ? Math.max(1, Math.floor(baseCount / 2)) : baseCount;

        for (let i = 0; i < particleCount; i++) {
            const particle = this.getParticleFromPool();
            Object.assign(particle, {
                x: x + (Math.random() - 0.5) * 20,
                y: y + (Math.random() - 0.5) * 20,
                vx: (Math.random() - 0.5) * 2,
                vy: -Math.random() * 3 - 1,
                life: 1.0,
                maxLife: 1.0,
                size: Math.random() * 3 + 2,
                color: type === 'heal' ? '#00ff00' : '#ff4444',
                type: 'text',
                text: damage.toString(),
                gravity: 0.1,
                active: true
            });
            this.particles.push(particle);
        }
    }
    
    createLevelUpParticles(x, y) {
        const particleCount = 20;
        
        for (let i = 0; i < particleCount; i++) {
            const angle = (i / particleCount) * Math.PI * 2;
            const speed = Math.random() * 3 + 2;
            
            this.particles.push({
                x: x,
                y: y,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                life: 1.0,
                maxLife: 1.0,
                size: Math.random() * 4 + 2,
                color: '#ffff00',
                type: 'star',
                gravity: -0.05,
                fade: 0.02
            });
        }
    }
    
    createGoldParticles(x, y, amount) {
        const particleCount = Math.min(10, Math.floor(amount / 5) + 1);
        
        for (let i = 0; i < particleCount; i++) {
            this.particles.push({
                x: x + (Math.random() - 0.5) * 30,
                y: y + (Math.random() - 0.5) * 30,
                vx: (Math.random() - 0.5) * 4,
                vy: -Math.random() * 2 - 1,
                life: 1.0,
                maxLife: 1.0,
                size: Math.random() * 3 + 1,
                color: '#ffd700',
                type: 'circle',
                gravity: 0.05,
                bounce: 0.7,
                groundY: y + 50
            });
        }
    }
    
    createCombatParticles(x, y, type = 'hit') {
        const particleCount = type === 'critical' ? 15 : 8;
        
        for (let i = 0; i < particleCount; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = Math.random() * 4 + 1;
            
            this.particles.push({
                x: x,
                y: y,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                life: 1.0,
                maxLife: 0.5,
                size: Math.random() * 2 + 1,
                color: type === 'critical' ? '#ff0066' : '#ffffff',
                type: 'spark',
                gravity: 0.1,
                fade: 0.05
            });
        }
    }
    
    createMagicParticles(x, y, color = '#9966ff') {
        const particleCount = 12;
        
        for (let i = 0; i < particleCount; i++) {
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * 30 + 10;
            
            this.particles.push({
                x: x + Math.cos(angle) * radius,
                y: y + Math.sin(angle) * radius,
                vx: -Math.cos(angle) * 0.5,
                vy: -Math.sin(angle) * 0.5,
                life: 1.0,
                maxLife: 2.0,
                size: Math.random() * 3 + 1,
                color: color,
                type: 'magic',
                gravity: -0.02,
                fade: 0.01,
                pulse: Math.random() * Math.PI * 2
            });
        }
    }
    
    createDeathParticles(x, y) {
        const particleCount = 25;
        
        for (let i = 0; i < particleCount; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = Math.random() * 5 + 2;
            
            this.particles.push({
                x: x,
                y: y,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                life: 1.0,
                maxLife: 1.5,
                size: Math.random() * 4 + 2,
                color: '#666666',
                type: 'smoke',
                gravity: -0.03,
                fade: 0.015,
                rotation: Math.random() * Math.PI * 2,
                rotationSpeed: (Math.random() - 0.5) * 0.1
            });
        }
    }
    
    createItemDropParticles(x, y, rarity = 'common') {
        const colors = {
            'common': '#ffffff',
            'uncommon': '#00ff00',
            'rare': '#0066ff',
            'epic': '#9966ff',
            'legendary': '#ff6600'
        };
        
        const particleCount = rarity === 'legendary' ? 20 : 10;
        const color = colors[rarity] || '#ffffff';
        
        for (let i = 0; i < particleCount; i++) {
            const angle = (i / particleCount) * Math.PI * 2;
            const speed = Math.random() * 2 + 1;
            
            this.particles.push({
                x: x,
                y: y,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                life: 1.0,
                maxLife: 1.0,
                size: Math.random() * 2 + 1,
                color: color,
                type: 'sparkle',
                gravity: 0,
                fade: 0.02,
                twinkle: Math.random() * Math.PI * 2
            });
        }
    }
    
    // Update all particles
    update(deltaTime) {
        const dt = deltaTime / 1000; // Convert to seconds
        
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            
            // Update position
            particle.x += particle.vx * dt * 60; // 60fps normalization
            particle.y += particle.vy * dt * 60;
            
            // Apply gravity
            if (particle.gravity) {
                particle.vy += particle.gravity * dt * 60;
            }
            
            // Handle bouncing
            if (particle.bounce && particle.groundY && particle.y >= particle.groundY) {
                particle.y = particle.groundY;
                particle.vy *= -particle.bounce;
                particle.vx *= 0.8; // Friction
            }
            
            // Update life
            particle.life -= (particle.fade || (1 / particle.maxLife)) * dt;
            
            // Update special properties
            if (particle.pulse !== undefined) {
                particle.pulse += dt * 5;
            }
            
            if (particle.twinkle !== undefined) {
                particle.twinkle += dt * 10;
            }
            
            if (particle.rotation !== undefined) {
                particle.rotation += particle.rotationSpeed * dt * 60;
            }
            
            // Remove dead particles and return to pool
            if (particle.life <= 0) {
                this.returnParticleToPool(particle);
                this.particles.splice(i, 1);
            }
        }
        
        // Limit particle count
        if (this.particles.length > this.maxParticles) {
            this.particles.splice(0, this.particles.length - this.maxParticles);
        }
    }
    
    // Render all particles
    render(ctx) {
        this.particles.forEach(particle => {
            const alpha = particle.life;
            
            ctx.save();
            ctx.globalAlpha = alpha;
            
            switch (particle.type) {
                case 'text':
                    this.renderTextParticle(ctx, particle);
                    break;
                case 'circle':
                    this.renderCircleParticle(ctx, particle);
                    break;
                case 'star':
                    this.renderStarParticle(ctx, particle);
                    break;
                case 'spark':
                    this.renderSparkParticle(ctx, particle);
                    break;
                case 'magic':
                    this.renderMagicParticle(ctx, particle);
                    break;
                case 'smoke':
                    this.renderSmokeParticle(ctx, particle);
                    break;
                case 'sparkle':
                    this.renderSparkleParticle(ctx, particle);
                    break;
            }
            
            ctx.restore();
        });
    }
    
    renderTextParticle(ctx, particle) {
        ctx.fillStyle = particle.color;
        ctx.font = `bold ${particle.size * 4}px 'Courier New'`;
        ctx.textAlign = 'center';
        ctx.fillText(particle.text, particle.x, particle.y);
    }
    
    renderCircleParticle(ctx, particle) {
        ctx.fillStyle = particle.color;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fill();
    }
    
    renderStarParticle(ctx, particle) {
        ctx.fillStyle = particle.color;
        ctx.beginPath();
        
        const spikes = 5;
        const outerRadius = particle.size;
        const innerRadius = particle.size * 0.5;
        
        for (let i = 0; i < spikes * 2; i++) {
            const angle = (i / (spikes * 2)) * Math.PI * 2;
            const radius = i % 2 === 0 ? outerRadius : innerRadius;
            const x = particle.x + Math.cos(angle) * radius;
            const y = particle.y + Math.sin(angle) * radius;
            
            if (i === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }
        
        ctx.closePath();
        ctx.fill();
    }
    
    renderSparkParticle(ctx, particle) {
        ctx.strokeStyle = particle.color;
        ctx.lineWidth = particle.size;
        ctx.beginPath();
        ctx.moveTo(particle.x - particle.vx * 0.1, particle.y - particle.vy * 0.1);
        ctx.lineTo(particle.x + particle.vx * 0.1, particle.y + particle.vy * 0.1);
        ctx.stroke();
    }
    
    renderMagicParticle(ctx, particle) {
        const pulseSize = particle.size * (1 + Math.sin(particle.pulse) * 0.3);
        ctx.fillStyle = particle.color;
        ctx.shadowColor = particle.color;
        ctx.shadowBlur = 10;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, pulseSize, 0, Math.PI * 2);
        ctx.fill();
        ctx.shadowBlur = 0;
    }
    
    renderSmokeParticle(ctx, particle) {
        ctx.save();
        ctx.translate(particle.x, particle.y);
        ctx.rotate(particle.rotation);
        ctx.fillStyle = particle.color;
        ctx.fillRect(-particle.size, -particle.size, particle.size * 2, particle.size * 2);
        ctx.restore();
    }
    
    renderSparkleParticle(ctx, particle) {
        const twinkleAlpha = (Math.sin(particle.twinkle) + 1) * 0.5;
        ctx.globalAlpha *= twinkleAlpha;
        ctx.fillStyle = particle.color;
        ctx.shadowColor = particle.color;
        ctx.shadowBlur = 5;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.shadowBlur = 0;
    }
    
    // Clear all particles
    clear() {
        this.particles = [];
    }
    
    // Get particle count for debugging
    getParticleCount() {
        return this.particles.length;
    }

    // Performance control
    setPerformanceMode(enabled) {
        this.performanceMode = enabled;
        if (enabled) {
            this.maxParticles = 50; // Reduce max particles
        } else {
            this.maxParticles = 100;
        }
    }

    // Get performance statistics
    getPerformanceStats() {
        return {
            activeParticles: this.particles.length,
            maxParticles: this.maxParticles,
            poolSize: this.poolSize,
            poolUsage: this.particlePool.filter(p => p.active).length,
            performanceMode: this.performanceMode
        };
    }
}
